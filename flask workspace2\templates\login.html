{% extends "base.html" %} {% block auth_content %}

<div class="login-container">
  <div class="login-card{% if show_force_login %} force-login-mode{% endif %}">
    <div class="login-header">
      <div class="login-logo">
        <i class="fas fa-building"></i>
      </div>
      <h2>GRS Wall Designer</h2>
      <p>Sign in to your account</p>
    </div>

    {% if show_force_login %}
    <!-- Force Login Options -->
    <div class="force-login-container">
      <div class="session-warning">
        <div class="warning-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="warning-content">
          <h3>Active Session Detected</h3>
          <p>
            You are already logged in from another device or browser. Choose an
            option below:
          </p>
        </div>
      </div>

      <div class="force-login-options">
        <!-- Force Login Option -->
        <div class="option-card primary-option">
          <div class="option-header">
            <i class="fas fa-sign-in-alt"></i>
            <h4>Force Login</h4>
          </div>
          <p>Logout from all other sessions and login here</p>
          <form method="POST" action="{{ url_for('login') }}">
            <input
              type="hidden"
              name="username"
              value="{{ session.get('temp_username', '') }}"
            />
            <input
              type="hidden"
              name="password"
              value="{{ session.get('temp_password', '') }}"
            />
            <input type="hidden" name="force_login" value="true" />
            <button type="submit" class="btn btn-danger btn-force">
              <i class="fas fa-sign-in-alt"></i>
              Force Login
            </button>
          </form>
        </div>

        <!-- Cancel Option -->
        <div class="option-card secondary-option">
          <div class="option-header">
            <i class="fas fa-arrow-left"></i>
            <h4>Cancel</h4>
          </div>
          <p>Go back and logout from other sessions manually</p>
          <a
            href="{{ url_for('clear_temp_session') }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-arrow-left"></i>
            Back to Login
          </a>
        </div>

        <!-- Remote Logout Option -->
        <div class="option-card remote-option">
          <div class="option-header">
            <i class="fas fa-sign-out-alt"></i>
            <h4>Remote Logout</h4>
          </div>
          <p>Can't access your other device? Logout all sessions remotely:</p>
          <form method="POST" action="{{ url_for('logout_all_sessions') }}">
            <input
              type="hidden"
              name="username"
              value="{{ session.get('temp_username', '') }}"
            />
            <input
              type="hidden"
              name="password"
              value="{{ session.get('temp_password', '') }}"
            />
            <button type="submit" class="btn btn-info">
              <i class="fas fa-sign-out-alt"></i>
              Logout All Sessions
            </button>
          </form>
        </div>
      </div>
    </div>

    {% else %}
    <!-- Normal Login Form -->
    <div class="login-form">
      <form method="POST" action="{{ url_for('login') }}" id="loginForm">
        <div class="form-group">
          <label for="username">Username</label>
          <input
            type="text"
            class="form-control"
            name="username"
            id="username"
            value="{{ username or '' }}"
            required
            placeholder="Enter your username"
          />
        </div>
        <div class="form-group">
          <label for="password">Password</label>
          <input
            type="password"
            class="form-control"
            name="password"
            id="password"
            required
            placeholder="Enter your password"
          />
        </div>
        <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
          <i class="fas fa-sign-in-alt"></i>
          Sign In
        </button>
      </form>

      <div class="login-footer">
        <p>Don't have an account?</p>
        <a
          href="{{ url_for('request_access') }}"
          class="btn btn-outline-primary"
        >
          <i class="fas fa-user-plus"></i>
          Request Access
        </a>
      </div>
    </div>
    {% endif %}

    <div class="login-notices">
      <div class="notice notice-info">
        <i class="fas fa-info-circle"></i>
        <span>Don't have credentials? Request access above.</span>
      </div>

      <div class="notice notice-warning">
        <i class="fas fa-shield-alt"></i>
        <span
          ><strong>Security Notice:</strong> Only one active session is allowed
          per user.</span
        >
      </div>
    </div>
  </div>
</div>

<style>
  /* Override any conflicting body styles for login page */
  body {
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
  }

  html {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Ensure auth container fills entire viewport */
  .auth-container {
    min-height: 100vh !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    background: linear-gradient(
      135deg,
      var(--primary-color) 0%,
      var(--primary-dark) 100%
    ) !important;
    overflow-x: hidden !important;
  }

  /* Modern Login Page Styles */
  .login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem;
    width: 100%;
    box-sizing: border-box;
  }

  .login-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
  }

  /* Expand login card for force login scenario */
  .login-card.force-login-mode {
    max-width: 1000px;
  }

  .login-header {
    text-align: center;
    margin-bottom: 2.5rem;
  }

  .login-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(
      135deg,
      var(--primary-color),
      var(--primary-dark)
    );
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: var(--shadow-md);
  }

  .login-logo i {
    font-size: 2rem;
    color: white;
  }

  .login-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
  }

  .login-header p {
    color: var(--gray-600);
    font-size: 1rem;
    margin: 0;
  }

  .login-form .form-group {
    margin-bottom: 1.5rem;
  }

  .login-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
  }

  .login-form .form-control {
    width: 100%;
    padding: 0.875rem;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all var(--transition-fast);
  }

  .login-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    outline: none;
  }

  .btn-login {
    width: 100%;
    padding: 0.875rem;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
  }

  .login-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
  }

  .login-footer p {
    color: var(--gray-600);
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }

  .login-footer .btn {
    width: 100%;
    padding: 0.75rem;
  }

  .login-notices {
    margin-top: 2rem;
  }

  .notice {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }

  .notice-info {
    background-color: rgba(239, 246, 255, 0.9);
    border: none;
    color: #1e40af;
  }

  .notice-warning {
    background-color: rgba(255, 251, 235, 0.9);
    border: none;
    color: #92400e;
  }

  .notice i {
    margin-top: 0.125rem;
    flex-shrink: 0;
  }

  @media (max-width: 480px) {
    .login-container {
      padding: 1rem;
    }

    .login-card {
      padding: 2rem;
      margin: 0;
    }

    .login-logo {
      width: 60px;
      height: 60px;
    }

    .login-logo i {
      font-size: 1.5rem;
    }

    .login-header h2 {
      font-size: 1.5rem;
    }
  }

  /* Force Login Styles */
  .force-login-container {
    width: 100%;
  }

  .session-warning {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 2px solid #f59e0b;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  }

  .warning-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  }

  .warning-icon i {
    font-size: 1.5rem;
    color: white;
  }

  .warning-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #92400e;
    margin-bottom: 0.5rem;
  }

  .warning-content p {
    color: #92400e;
    margin: 0;
    font-size: 0.95rem;
  }

  .force-login-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
    width: 100%;
  }

  .option-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease-in-out;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
  }

  .option-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  }

  .primary-option {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2, white);
  }

  .secondary-option {
    border-color: #94a3b8;
    background: linear-gradient(135deg, #f8fafc, white);
  }

  .remote-option {
    border-color: #2563eb;
    background: linear-gradient(135deg, #eff6ff, white);
  }

  .option-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .option-header i {
    font-size: 1.5rem;
    padding: 0.75rem;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .primary-option .option-header i {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
  }

  .secondary-option .option-header i {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
  }

  .remote-option .option-header i {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: white;
  }

  .option-header h4 {
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
    color: #1e293b;
  }

  .option-card p {
    color: #64748b;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    line-height: 1.4;
    flex-grow: 1;
  }

  .btn-force {
    width: 100%;
    padding: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
  }

  .option-card .btn {
    width: 100%;
    padding: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
  }

  @media (max-width: 992px) {
    .force-login-options {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .login-card.force-login-mode {
      max-width: 600px;
    }
  }

  @media (max-width: 768px) {
    .session-warning {
      flex-direction: column;
      text-align: center;
      padding: 1rem;
    }

    .option-card {
      padding: 1.25rem;
      min-height: 240px;
    }

    .login-card {
      padding: 2rem;
    }

    .login-card.force-login-mode {
      max-width: 100%;
      margin: 0;
    }
  }

  @media (max-width: 480px) {
    .login-container {
      padding: 0.75rem;
    }

    .login-card {
      padding: 1.5rem;
    }

    .option-card {
      padding: 1rem;
      min-height: 200px;
    }
  }

  /* Login Popup Styles */
  .login-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .login-popup.show {
    opacity: 1;
    visibility: visible;
  }

  .login-popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }

  .login-popup-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    min-width: 320px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transition: transform 0.3s ease;
  }

  .login-popup.show .login-popup-content {
    transform: translate(-50%, -50%) scale(1);
  }

  .login-popup-header {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
  }

  .login-popup-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 600;
  }

  .login-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
  }

  .login-popup-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .login-popup-body {
    padding: 30px 20px;
    color: #374151;
    line-height: 1.6;
  }

  .login-popup-body p {
    margin: 0;
    font-size: 1.1em;
    text-align: center;
  }

  .login-popup-footer {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .login-popup-btn {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .login-popup-btn:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const loginForm = document.getElementById("loginForm");
    const loginBtn = document.getElementById("loginBtn");

    if (loginForm) {
      loginForm.addEventListener("submit", function (e) {
        e.preventDefault();

        const formData = new FormData(loginForm);
        const username = formData.get("username");
        const password = formData.get("password");

        // Disable button and show loading state
        loginBtn.disabled = true;
        loginBtn.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> Signing In...';

        // Send AJAX request
        fetch('{{ url_for("login") }}', {
          method: "POST",
          headers: {
            "X-Requested-With": "XMLHttpRequest",
          },
          body: formData,
        })
          .then((response) => {
            if (response.redirected) {
              // Successful login - redirect
              window.location.href = response.url;
              return;
            }

            // Check if response is JSON
            const contentType = response.headers.get("content-type");
            if (contentType && contentType.includes("application/json")) {
              return response.json();
            } else {
              // Non-JSON response (likely HTML with flash message) - reload page
              window.location.reload();
              return;
            }
          })
          .then((data) => {
            if (data && data.status === "error") {
              // Show error popup only for JSON error responses (invalid credentials)
              showLoginErrorPopup(data.message);

              // Reset button
              loginBtn.disabled = false;
              loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';

              // Clear password field
              document.getElementById("password").value = "";
            } else if (data && data.status === "multi_session") {
              // Multi-session detected - reload page to show force login interface
              window.location.reload();
            }
          })
          .catch((error) => {
            console.error("Login error:", error);

            // Reset button
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';

            // For network errors, reload the page to show any flash messages
            window.location.reload();
          });
      });
    }
  });

  function showLoginErrorPopup(message) {
    // Remove any existing popup
    const existingPopup = document.getElementById("loginErrorPopup");
    if (existingPopup) {
      existingPopup.remove();
    }

    // Create popup HTML
    const popup = document.createElement("div");
    popup.id = "loginErrorPopup";
    popup.className = "login-popup";

    popup.innerHTML = `
        <div class="login-popup-overlay" onclick="closeLoginPopup()"></div>
        <div class="login-popup-content">
            <div class="login-popup-header">
                <h3>❌ Login Failed</h3>
                <button class="login-popup-close" onclick="closeLoginPopup()">&times;</button>
            </div>
            <div class="login-popup-body">
                <p>${message}</p>
            </div>
            <div class="login-popup-footer">
                <button class="login-popup-btn" onclick="closeLoginPopup()">OK</button>
            </div>
        </div>
    `;

    // Add to document
    document.body.appendChild(popup);

    // Show popup with animation
    setTimeout(() => {
      popup.classList.add("show");
    }, 10);
  }

  function closeLoginPopup() {
    const popup = document.getElementById("loginErrorPopup");
    if (popup) {
      popup.classList.remove("show");
      setTimeout(() => {
        popup.remove();
      }, 300);
    }
  }

  // Close popup on Escape key
  document.addEventListener("keydown", function (e) {
    if (e.key === "Escape") {
      closeLoginPopup();
    }
  });
</script>

{% endblock %}
