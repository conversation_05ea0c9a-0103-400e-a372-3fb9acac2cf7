form {
    max-width: 500px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

label {
    display: block;
    margin-top: 1rem;
    font-size: 16px;
    color: #4a4a4a;
    font-weight: 600;
}

input[type="text"] {
    width: 100%;
    padding: 8px;
    margin-top: 0.5rem;
    border: 2px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
}

input[type="text"]:focus {
    border: 2px solid #66afe9;
    background-color: #ffffff;
}

button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 1rem;
}

button:hover {
    background-color: #45a049;
}

#back-button {
    background-color: #555;
    float: right;
}

#back-button:hover {
    background-color: #333;
}
