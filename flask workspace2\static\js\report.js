document.addEventListener('DOMContentLoaded', function () {
    const screenshot = sessionStorage.getItem('lastDesignScreenshot');
    
    if (screenshot) {
        const container = document.createElement('div');
        container.id = 'design-screenshot-container';
        container.innerHTML = `
            <h3>Design Visualization</h3>
            <div class="screenshot-wrapper">
                <img id="report-screenshot" 
                     src="${screenshot}" 
                     style="display:block; max-width:100%; height:auto; border:1px solid #ccc;" 
                     crossorigin="anonymous">
                <p class="timestamp">${new Date().toLocaleString()}</p>
            </div>
        `;

        // Insert above footer or into report container
        const footer = document.querySelector('.page-footer');
        footer?.parentNode.insertBefore(container, footer);
    }
});


