from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
import math

app = Flask(__name__)
app.secret_key = 'your_secret_key'  # Replace with your actual secret key

@app.route('/')
def home():
    return render_template('home.html')

@app.route('/project_info', methods=['GET', 'POST'])
def project_info():
    if request.method == 'POST':
        # Process form data
        project_name = request.form.get('project_name')
        project_id = request.form.get('project_id')
        designer = request.form.get('designer')
        client = request.form.get('client')
        description = request.form.get('description')
        date = request.form.get('date')
        revision = request.form.get('revision')

        # Save the data to the session
        session['project_info'] = {
            'project_name': project_name,
            'project_id': project_id,
            'designer': designer,
            'client': client,
            'description': description,
            'date': date,
            'revision': revision
        }

        # Flash success message
        flash('Project info has been saved!', 'success')

        # Return a JSON response for AJAX
        return jsonify({'message': 'Project info has been saved!'})

    # Handle GET request to display the form with data from the session
    return render_template('project_info.html', data=session.get('project_info', {}))


@app.route('/geometry', methods=['GET', 'POST'])
def geometry():
    if request.method == 'POST':
        # Getting the form data from the request
        wall_height = request.form.get('wall_height')
        embedment_depth = request.form.get('embedment_depth')
        wall_length = request.form.get('wall_length')
        wall_batter = request.form.get('wall_batter')
        backslope_angle = request.form.get('backslope_angle')
        backslope_rise = request.form.get('backslope_rise')

        # Save the data to the session
        session['geometry'] = {
            'wall_height': wall_height,
            'embedment_depth': embedment_depth,
            'wall_length': wall_length,
            'wall_batter': wall_batter,
            'backslope_angle': backslope_angle,
            'backslope_rise': backslope_rise
        }

        # Flash success message
        flash('Geometry data has been saved!', 'success')

        # Return a JSON response for AJAX
        return jsonify({'message': 'Geometry data has been saved!'})

    # Handle GET request to display the form with data from the session
    return render_template('geometry.html', data=session.get('geometry', {}))


@app.route('/reinforcedsoil', methods=['GET', 'POST'])
def reinforcedsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        soil_density = request.form.get('soil_density')
        friction_angle = request.form.get('friction_angle')
        cohesion = request.form.get('cohesion')
        
        # Save the data to the session (or to a database, if needed)
        session['soil_density'] = soil_density
        session['friction_angle'] = friction_angle
        session['cohesion'] = cohesion
        
        # Optionally, you can save the data to a database here for permanent storage
        # Set a message that data has been saved
        flash('Reinforced soil data has been saved!', 'success')
        # Return a response (can return a message or redirect)
        return redirect(url_for('reinforcedsoil'))
        
    # Handle GET request to display the form with data from session
    return render_template('reinforcedsoil.html', 
                           soil_density=session.get('soil_density', ''),
                           friction_angle=session.get('friction_angle', ''),
                           cohesion=session.get('cohesion', ''))


@app.route('/retainedsoil', methods=['GET', 'POST'])
def retainedsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        retainedsoil_density = request.form.get('retainedsoil_density')
        retainedfriction_angle = request.form.get('retainedfriction_angle')
        retainedcohesion = request.form.get('retainedcohesion')
        
        # Save the data to the session (or to a database, if needed)
        session['retainedsoil_density'] = retainedsoil_density
        session['retainedfriction_angle'] = retainedfriction_angle
        session['retainedcohesion'] = retainedcohesion
        
        # Optionally, you can save the data to a database here for permanent storage
        # Set a message that data has been saved
        flash('Retained soil data has been saved!', 'success')
        # Return a JSON response for AJAX
        return jsonify({'message': 'Retained soil data has been saved!'})

    # Handle GET request to display the form with data from session
    return render_template('retainedsoil.html', 
                           retainedsoil_density=session.get('retainedsoil_density', ''),
                           retainedfriction_angle=session.get('retainedfriction_angle', ''),
                           retainedcohesion=session.get('retainedcohesion', ''))

@app.route('/foundationsoil', methods=['GET', 'POST'])
def foundationsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        foundationsoildensity = request.form.get('foundationsoildensity')
        foundationsoilfriction_angle = request.form.get('foundationsoilfriction_angle')
        foundationsoilcohesion = request.form.get('foundationsoilcohesion')
        eccentricity = request.form.get('eccentricity')
        eccentricity_seismic = request.form.get('eccentricity_seismic')
        watertable = request.form.get('watertable')

        # Save the data to the session
        session['foundationsoil'] = {
            'foundationsoildensity': foundationsoildensity,
            'foundationsoilfriction_angle': foundationsoilfriction_angle,
            'foundationsoilcohesion': foundationsoilcohesion,
            'eccentricity': eccentricity,
            'eccentricity_seismic': eccentricity_seismic,
            'watertable': watertable
        }

        # Flash success message
        flash('Foundation soil data has been saved!', 'success')

        # Return a JSON response for AJAX
        return jsonify({'message': 'Foundation soil data has been saved!'})

    # Handle GET request to display the form with data from the session
    return render_template('foundationsoil.html', data=session.get('foundationsoil', {}))

@app.route('/externalloads', methods=['GET', 'POST'])
def externalloads():
    if request.method == 'POST':
        # Getting the form data from the request
        # Dead Loads
        dead_load1 = request.form.get('dead_load1')
        dead_load2 = request.form.get('dead_load2')
        dead_load3 = request.form.get('dead_load3')

        # Live Loads
        live_load1 = request.form.get('live_load1')
        live_load2 = request.form.get('live_load2')
        live_load3 = request.form.get('live_load3')

        # Strip Loads (3 sets)
        vertical_strip_load1 = request.form.get('vertical_strip_load1')
        horizontal_strip_load1 = request.form.get('horizontal_strip_load1')
        strip_load1_width = request.form.get('strip_load_width1')
        strip_load1_distance = request.form.get('strip_load_distance1')

        vertical_strip_load2 = request.form.get('vertical_strip_load2')
        horizontal_strip_load2 = request.form.get('horizontal_strip_load2')
        strip_load2_width = request.form.get('strip_load_width2')
        strip_load2_distance = request.form.get('strip_load_distance2')

        vertical_strip_load3 = request.form.get('vertical_strip_load3')
        horizontal_strip_load3 = request.form.get('horizontal_strip_load3')
        strip_load3_width = request.form.get('strip_load_width3')
        strip_load3_distance = request.form.get('strip_load_distance3')

        # Earthquake Loads
        earthquake_acceleration = request.form.get('earthquake_acceleration')
        seismic_force = request.form.get('seismic_force') if request.form.get('use_direct_kh') else None

        # Impact Loads
        rupture_impact_upper = request.form.get('rupture_impact_upper')
        rupture_impact_second = request.form.get('rupture_impact_second')
        pullout_impact_upper = request.form.get('pullout_impact_upper')
        pullout_impact_second = request.form.get('pullout_impact_second')

        # Save the data to the session (or to a database)
        session.update({
            'dead_load1': dead_load1,
            'dead_load2': dead_load2,
            'dead_load3': dead_load3,
            'live_load1': live_load1,
            'live_load2': live_load2,
            'live_load3': live_load3,
            'vertical_strip_load1': vertical_strip_load1,
            'horizontal_strip_load1': horizontal_strip_load1,
            'strip_load1_width': strip_load1_width,
            'strip_load1_distance': strip_load1_distance,
            'vertical_strip_load2': vertical_strip_load2,
            'horizontal_strip_load2': horizontal_strip_load2,
            'strip_load2_width': strip_load2_width,
            'strip_load2_distance': strip_load2_distance,
            'vertical_strip_load3': vertical_strip_load3,
            'horizontal_strip_load3': horizontal_strip_load3,
            'strip_load3_width': strip_load3_width,
            'strip_load3_distance': strip_load3_distance,
            'earthquake_acceleration': earthquake_acceleration,
            'seismic_force': seismic_force,
            'rupture_impact_upper': rupture_impact_upper,
            'rupture_impact_second': rupture_impact_second,
            'pullout_impact_upper': pullout_impact_upper,
            'pullout_impact_second': pullout_impact_second,
        })

        # Flash message
        flash('External loads data has been saved!', 'success')

        # Return a JSON response for AJAX
        return jsonify({'message': 'External loads data has been saved!'})

    # Handle GET request to display the form with session data
    return render_template('externalloads.html',
                           dead_load1=session.get('dead_load1', ''),
                           dead_load2=session.get('dead_load2', ''),
                           dead_load3=session.get('dead_load3', ''),
                           live_load1=session.get('live_load1', ''),
                           live_load2=session.get('live_load2', ''),
                           live_load3=session.get('live_load3', ''),
                           vertical_strip_load1=session.get('vertical_strip_load1', ''),
                           horizontal_strip_load1=session.get('horizontal_strip_load1', ''),
                           strip_load1_width=session.get('strip_load1_width', ''),
                           strip_load1_distance=session.get('strip_load1_distance', ''),
                           vertical_strip_load2=session.get('vertical_strip_load2', ''),
                           horizontal_strip_load2=session.get('horizontal_strip_load2', ''),
                           strip_load2_width=session.get('strip_load2_width', ''),
                           strip_load2_distance=session.get('strip_load2_distance', ''),
                           vertical_strip_load3=session.get('vertical_strip_load3', ''),
                           horizontal_strip_load3=session.get('horizontal_strip_load3', ''),
                           strip_load3_width=session.get('strip_load3_width', ''),
                           strip_load3_distance=session.get('strip_load3_distance', ''),
                           earthquake_acceleration=session.get('earthquake_acceleration', ''),
                           seismic_force=session.get('seismic_force', ''),
                           rupture_impact_upper=session.get('rupture_impact_upper', ''),
                           rupture_impact_second=session.get('rupture_impact_second', ''),
                           pullout_impact_upper=session.get('pullout_impact_upper', ''),
                           pullout_impact_second=session.get('pullout_impact_second', ''))


@app.route('/reinforcementproperties', methods=['GET', 'POST'])
def reinforcementproperties():
    if request.method == 'POST':
        # Extract multiple reinforcement rows
        reinforcement_data = []
        row_count = int(request.form.get('row_count', 0))  # Get number of rows

        for i in range(row_count):
            reinforcement_data.append({
                'type_id': request.form.get(f'type_id_{i}'),
                'name': request.form.get(f'name_{i}'),  # Ensuring name is retained
                'tult': request.form.get(f'tult_{i}'),
                'rfid': request.form.get(f'rfid_{i}'),
                'rfw': request.form.get(f'rfw_{i}'),
                'rfcr': request.form.get(f'rfcr_{i}'),
                'fs': request.form.get(f'fs_{i}'),
                'pullout_angle': request.form.get(f'pullout_angle_{i}'),
                'sliding_angle': request.form.get(f'sliding_angle_{i}'),
                'scale_factor': request.form.get(f'scale_factor_{i}')
            })

        # Save in session to retain data
        session['reinforcement_data'] = reinforcement_data

        flash('Reinforcement properties saved successfully!', 'success')
        return jsonify({'message': 'Reinforcement properties saved successfully!'})

    # Retrieve stored reinforcement data
    reinforcement_data = session.get('reinforcement_data', [])

    return render_template('reinforcementproperties.html', reinforcement_data=reinforcement_data)

@app.route('/reinforcementlayout', methods=['GET', 'POST'])
def reinforcementlayout():
    if request.method == 'POST':
        # Process the data submitted by the form
        data = request.get_json()
        session['reinforcement_layout_data'] = data # Save the data to the session

        flash('Reinforcement layout data saved successfully!', 'success')
        return jsonify({'message': 'Reinforcement layout data saved successfully!'})

    # Handle GET request to display the form
    return render_template('reinforcementlayout.html')

@app.route('/reinforcementlayout/data', methods=['GET'])
def reinforcementlayout_data():
    # Retrieve the data from the session (or database)
    data = session.get('reinforcement_layout_data', [])

    return jsonify(data)


@app.context_processor
def inject_reinforcement_data():
    reinforcement_data = session.get('reinforcement_data', [])
    return dict(reinforcement_data=reinforcement_data)


@app.route('/calculate', methods=['POST'])
def calculate():
    # Perform calculations based on input data
    # This is a placeholder for your actual calculation logic
    result = "Calculation results would go here"
    return jsonify({'result': result})

if __name__ == '__main__':
    app.run(debug=True)
