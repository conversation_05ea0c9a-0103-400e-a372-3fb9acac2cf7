{% extends "base.html" %} {% block content %}
<div class="container" id="reinforcementproperties-form">
  <h2 class="text-center">Reinforcement Properties</h2>

  <div class="table-container">
    <table id="reinforcementTable" class="table table-bordered">
      <thead class="table-light">
        <tr>
          <th>Type ID</th>
          <th style="width: 200px">Name</th>
          <th>Tult (kN/m)</th>
          <th>RFid</th>
          <th>RFw</th>
          <th>RFcr</th>
          <th>FS</th>
          <th>Pullout Angle (°)</th>
          <th>Sliding Angle (°)</th>
          <th>Scale Factor</th>
          <th>Remove</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <button class="btn-add" onclick="addRow()">Add Reinforcement Type</button>
</div>

<script src="{{ url_for('static', filename='js/reinforcementproperties.js') }}"></script>

{% endblock %}
