/**
 * Shared Drawing Utilities for GRS Wall Visualizations
 * Contains common drawing functions used across geometry, external loads, and layout visualizations
 * @version 2.0.0 - Production Ready
 */

// Shared drawing utilities namespace
window.DrawingUtils = (function() {
    'use strict';

    // Private constants
    const CONSTANTS = {
        BASE_SCALE: 50,
        BASE_X: 100,
        BASE_Y: 400,
        FASCIA_THICKNESS_RATIO: 0.2,
        COLORS: {
            FASCIA: '#666',
            REINFORCED_FILL: '#D6B85A',
            RETAINED_FILL: '#D2B48C',
            FOUNDATION_SOIL: '#A98B6D',
            BACKSLOPE: '#FFA500',
            TEXT: '#000',
            DIMENSION_LINE: '#000'
        },
        FONTS: {
            DEFAULT: '18px Arial',
            DIMENSION: '18px Arial'
        }
    };

    // Private utility functions
    function validateInputs(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise) {
        const inputs = { wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise };
        for (const [key, value] of Object.entries(inputs)) {
            if (typeof value !== 'number' || isNaN(value) || value < 0) {
                throw new Error(`Invalid ${key}: ${value}. Must be a non-negative number.`);
            }
        }
    }

    function toRadians(degrees) {
        return (degrees * Math.PI) / 180;
    }

    // Public API
    return {
        // Expose constants for external use
        CONSTANTS: CONSTANTS,

        /**
         * Calculate geometry components for GRS wall
         * @param {number} wallHeight - Wall height in meters
         * @param {number} embedmentDepth - Embedment depth in meters
         * @param {number} wallLength - Wall length in meters
         * @param {number} wallBatter - Wall batter angle in degrees
         * @param {number} backslopeAngle - Backslope angle in degrees
         * @param {number} backslopeRise - Backslope rise in meters
         * @returns {Object} Geometry calculations object
         */
        calculateGeometry: function(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise) {
            try {
                validateInputs(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise);

                const baseScale = CONSTANTS.BASE_SCALE;
                const baseX = CONSTANTS.BASE_X;
                const baseY = CONSTANTS.BASE_Y;
                const batterOffset = Math.tan(toRadians(wallBatter)) * (wallHeight * baseScale);
                const fasciaThickness = CONSTANTS.FASCIA_THICKNESS_RATIO * baseScale;

                return {
                    baseScale,
                    baseX,
                    baseY,
                    batterOffset,
                    fasciaThickness,

                    // Fascia geometry
                    fascia: {
                        x1: baseX - fasciaThickness, y1: baseY,
                        x2: baseX - fasciaThickness + batterOffset, y2: baseY - (wallHeight * baseScale),
                        x3: baseX + batterOffset + fasciaThickness, y3: baseY - (wallHeight * baseScale),
                        x4: baseX, y4: baseY
                    },

                    // Reinforced fill geometry
                    reinforcedFill: {
                        x1: baseX, y1: baseY,
                        x2: baseX + batterOffset, y2: baseY - (wallHeight * baseScale),
                        x3: baseX + (wallLength * baseScale) + batterOffset, y3: baseY - (wallHeight * baseScale),
                        x4: baseX + (wallLength * baseScale), y4: baseY
                    },

                    // Retained fill geometry
                    retainedFill: {
                        x1: baseX + (wallLength * baseScale), y1: baseY,
                        x2: baseX + (wallLength * baseScale) + batterOffset, y2: baseY - (wallHeight * baseScale),
                        x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y3: baseY - (wallHeight * baseScale),
                        x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y4: baseY
                    },

                    // Embedment geometry
                    embedment: {
                        x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY,
                        x2: baseX - 10 + batterOffset, y2: baseY - (embedmentDepth * baseScale)
                    },

                    // Foundation soil geometry
                    foundationSoil: {
                        x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY + (0.5 * wallHeight * baseScale),
                        x2: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y2: baseY
                    }
                };
            } catch (error) {
                console.error('Error calculating geometry:', error);
                throw error;
            }
        },

        /**
         * Calculate backslope geometry
         * @param {Object} geometry - Geometry calculations from calculateGeometry
         * @param {number} backslopeAngle - Backslope angle in degrees
         * @param {number} backslopeRise - Backslope rise in meters
         * @returns {Object} Backslope calculations object
         */
        calculateBackslope: function(geometry, backslopeAngle, backslopeRise) {
            try {
                if (!geometry || typeof geometry !== 'object') {
                    throw new Error('Invalid geometry object provided');
                }

                const { reinforcedFill, retainedFill, baseScale } = geometry;
                const backslopeAngleRadians = toRadians(backslopeAngle);

                if (backslopeAngle === 0 || Math.tan(backslopeAngleRadians) === 0) {
                    // Handle zero angle case
                    return {
                        slopeStartX: reinforcedFill.x2,
                        slopeStartY: reinforcedFill.y2,
                        slopeEndX: retainedFill.x3,
                        slopeEndY: reinforcedFill.y2,
                        isCase1: true,
                        backslopeAngleRadians: 0
                    };
                }

                const slopeBaseLength = backslopeRise / Math.tan(backslopeAngleRadians);
                const slopeStartX = reinforcedFill.x2;
                const slopeStartY = reinforcedFill.y2;
                let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);
                let slopeEndY = slopeStartY - (backslopeRise * baseScale);

                // Determine slope case
                const isCase1 = slopeEndX <= retainedFill.x3;

                if (!isCase1) {
                    // Case 2: Slope extends beyond retainedFill.x3
                    slopeEndX = retainedFill.x3;
                    slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));
                }

                return {
                    slopeStartX,
                    slopeStartY,
                    slopeEndX,
                    slopeEndY,
                    isCase1,
                    backslopeAngleRadians
                };
            } catch (error) {
                console.error('Error calculating backslope:', error);
                throw error;
            }
        },

        /**
         * Draw basic GRS wall structure (without loads or reinforcement)
         * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
         * @param {Object} geometry - Geometry calculations from calculateGeometry
         * @param {Object} slope - Slope calculations from calculateBackslope
         */
        drawBasicWall: function(ctx, geometry, slope) {
            try {
                if (!ctx || !geometry || !slope) {
                    throw new Error('Missing required parameters for drawBasicWall');
                }

                const { fascia, reinforcedFill, retainedFill, embedment, foundationSoil } = geometry;
                const { slopeStartX, slopeStartY, slopeEndX, slopeEndY, isCase1 } = slope;

                // Draw foundation soil
                ctx.fillStyle = CONSTANTS.COLORS.FOUNDATION_SOIL;
                ctx.fillRect(foundationSoil.x1, foundationSoil.y1,
                           foundationSoil.x2 - foundationSoil.x1,
                           foundationSoil.y2 - foundationSoil.y1);

                // Draw embedment
                ctx.fillStyle = CONSTANTS.COLORS.FOUNDATION_SOIL;
                ctx.fillRect(embedment.x1, embedment.y1,
                           embedment.x2 - embedment.x1,
                           embedment.y2 - embedment.y1);

                // Draw fascia
                ctx.fillStyle = CONSTANTS.COLORS.FASCIA;
                ctx.beginPath();
                ctx.moveTo(fascia.x1, fascia.y1);
                ctx.lineTo(fascia.x2, fascia.y2);
                ctx.lineTo(fascia.x3, fascia.y3);
                ctx.lineTo(fascia.x4, fascia.y4);
                ctx.closePath();
                ctx.fill();

                // Draw reinforced fill
                ctx.fillStyle = CONSTANTS.COLORS.REINFORCED_FILL;
                ctx.beginPath();
                ctx.moveTo(reinforcedFill.x1, reinforcedFill.y1);
                ctx.lineTo(reinforcedFill.x2, reinforcedFill.y2);
                ctx.lineTo(reinforcedFill.x3, reinforcedFill.y3);
                ctx.lineTo(reinforcedFill.x4, reinforcedFill.y4);
                ctx.closePath();
                ctx.fill();

                // Draw retained fill
                ctx.fillStyle = CONSTANTS.COLORS.RETAINED_FILL;
                ctx.beginPath();
                ctx.moveTo(retainedFill.x1, retainedFill.y1);
                ctx.lineTo(retainedFill.x2, retainedFill.y2);
                ctx.lineTo(retainedFill.x3, retainedFill.y3);
                ctx.lineTo(retainedFill.x4, retainedFill.y4);
                ctx.closePath();
                ctx.fill();

                // Draw backslope
                this._drawBackslope(ctx, slope, retainedFill);

            } catch (error) {
                console.error('Error drawing basic wall:', error);
                throw error;
            }
        },

        /**
         * Private method to draw backslope
         * @private
         */
        _drawBackslope: function(ctx, slope, retainedFill) {
            const { slopeStartX, slopeStartY, slopeEndX, slopeEndY, isCase1 } = slope;

            ctx.fillStyle = CONSTANTS.COLORS.BACKSLOPE;
            ctx.strokeStyle = CONSTANTS.COLORS.DIMENSION_LINE;
            ctx.lineWidth = 1;

            if (isCase1) {
                // Case 1: Slope ends before or exactly at retainedFill.x3
                const horizontalEndX = retainedFill.x3;
                const horizontalEndY = slopeEndY;

                // Draw slope line
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.stroke();

                // Draw horizontal extension
                ctx.beginPath();
                ctx.moveTo(slopeEndX, slopeEndY);
                ctx.lineTo(horizontalEndX, horizontalEndY);
                ctx.stroke();

                // Fill area (Trapezoidal)
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.lineTo(horizontalEndX, horizontalEndY);
                ctx.lineTo(retainedFill.x3, retainedFill.y3);
                ctx.closePath();
                ctx.fill();
            } else {
                // Case 2: Slope extends beyond retainedFill.x3
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.stroke();

                // Fill area (Triangular)
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.lineTo(retainedFill.x3, retainedFill.y3);
                ctx.closePath();
                ctx.fill();
            }
        },

        /**
         * Draw dimension arrows and labels
         * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
         * @param {Object} geometry - Geometry calculations
         * @param {number} wallHeight - Wall height in meters
         * @param {number} wallLength - Wall length in meters
         * @param {number} embedmentDepth - Embedment depth in meters
         */
        drawDimensions: function(ctx, geometry, wallHeight, wallLength, embedmentDepth) {
            try {
                if (!ctx || !geometry) {
                    throw new Error('Missing required parameters for drawDimensions');
                }

                const { baseX, baseY, baseScale, fasciaThickness } = geometry;

                ctx.strokeStyle = CONSTANTS.COLORS.DIMENSION_LINE;
                ctx.lineWidth = 1;
                ctx.font = CONSTANTS.FONTS.DIMENSION;
                ctx.fillStyle = CONSTANTS.COLORS.TEXT;

                // Wall height dimension
                ctx.beginPath();
                ctx.moveTo(baseX - 20, baseY);
                ctx.lineTo(baseX - 20, baseY - (wallHeight * baseScale));
                ctx.stroke();
                this.drawArrow(ctx, baseX - 20, baseY, baseX - 20, baseY - (wallHeight * baseScale), 0.05);
                ctx.textAlign = "center";
                ctx.textBaseline = "top";
                ctx.fillText(`${wallHeight} m`, baseX - 40, baseY - (0.5 * wallHeight * baseScale) - 10);

                // Wall length dimension
                ctx.beginPath();
                ctx.moveTo(baseX, baseY);
                ctx.lineTo(baseX + (wallLength * baseScale), baseY);
                ctx.stroke();
                this.drawArrow(ctx, baseX, baseY, baseX + (wallLength * baseScale), baseY, 0.05);
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(`${wallLength} m`, baseX + ((wallLength * baseScale) / 2), baseY + 10);

                // Embedment depth dimension
                ctx.beginPath();
                ctx.moveTo(baseX - 40, baseY);
                ctx.lineTo(baseX - 40, baseY - (embedmentDepth * baseScale));
                ctx.stroke();
                this.drawArrow(ctx, baseX - 40, baseY, baseX - 40, baseY - (embedmentDepth * baseScale), 0.2);
                ctx.textAlign = "center";
                ctx.textBaseline = "top";
                ctx.fillText(`${embedmentDepth} m`, baseX - 50 - fasciaThickness, baseY - (0.5 * embedmentDepth * baseScale) - 10);
            } catch (error) {
                console.error('Error drawing dimensions:', error);
                throw error;
            }
        },

        /**
         * Draw arrow between two points
         * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
         * @param {number} x1 - Start x coordinate
         * @param {number} y1 - Start y coordinate
         * @param {number} x2 - End x coordinate
         * @param {number} y2 - End y coordinate
         * @param {number} arrowSize - Arrow size ratio (default: 0.05)
         */
        drawArrow: function(ctx, x1, y1, x2, y2, arrowSize = 0.05) {
            try {
                if (!ctx) {
                    throw new Error('Canvas context is required');
                }

                ctx.strokeStyle = CONSTANTS.COLORS.DIMENSION_LINE;
                ctx.fillStyle = CONSTANTS.COLORS.DIMENSION_LINE;

                const dx = x2 - x1;
                const dy = y2 - y1;
                const angle = Math.atan2(dy, dx);
                const arrowLength = Math.sqrt(dx * dx + dy * dy) * arrowSize;

                // Draw arrow head at the end
                ctx.save();
                ctx.translate(x2, y2);
                ctx.rotate(angle);
                ctx.beginPath();
                ctx.moveTo(0, 0);
                ctx.lineTo(-arrowLength, -arrowLength / 2);
                ctx.lineTo(-arrowLength, arrowLength / 2);
                ctx.closePath();
                ctx.fill();
                ctx.restore();

                // Draw arrow head at the start
                ctx.save();
                ctx.translate(x1, y1);
                ctx.rotate(angle + Math.PI);
                ctx.beginPath();
                ctx.moveTo(0, 0);
                ctx.lineTo(-arrowLength, -arrowLength / 2);
                ctx.lineTo(-arrowLength, arrowLength / 2);
                ctx.closePath();
                ctx.fill();
                ctx.restore();

                // Draw the arrow line
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            } catch (error) {
                console.error('Error drawing arrow:', error);
                throw error;
            }
        },

        /**
         * Add labels to different sections
         * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
         * @param {Object} geometry - Geometry calculations
         * @param {number} wallLength - Wall length in meters
         */
        drawLabels: function(ctx, geometry, wallLength) {
            try {
                if (!ctx || !geometry) {
                    throw new Error('Missing required parameters for drawLabels');
                }

                const { baseX, baseY, baseScale } = geometry;

                ctx.font = CONSTANTS.FONTS.DEFAULT;
                ctx.fillStyle = CONSTANTS.COLORS.TEXT;
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";

                // Reinforced fill label
                ctx.fillText(`Reinforced Fill`,
                           baseX + ((0.5 * wallLength * baseScale)),
                           baseY - (0.5 * wallLength * baseScale) - 10);

                // Retained fill label
                ctx.fillText(`Retained Fill`,
                           baseX + ((1.5 * wallLength * baseScale)),
                           baseY - (0.5 * wallLength * baseScale) - 10);

                // Foundation soil label
                ctx.fillText(`Foundation Soil`,
                           baseX + ((1.0 * wallLength * baseScale)),
                           baseY + (0.25 * wallLength * baseScale) - 10);
            } catch (error) {
                console.error('Error drawing labels:', error);
                throw error;
            }
        },

        /**
         * Utility function to draw load arrows (for external loads)
         * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
         * @param {number} fromX - Start x coordinate
         * @param {number} fromY - Start y coordinate
         * @param {number} toX - End x coordinate
         * @param {number} toY - End y coordinate
         * @param {number} size - Arrow head size
         * @param {string} color - Arrow color
         * @param {boolean} hollow - Whether arrow should be hollow
         */
        drawLoadArrow: function(ctx, fromX, fromY, toX, toY, size, color, hollow = false) {
            try {
                if (!ctx) {
                    throw new Error('Canvas context is required');
                }

                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);

                if (hollow) {
                    ctx.stroke();
                } else {
                    ctx.lineTo(toX - size * Math.cos(angle - Math.PI / 6), toY - size * Math.sin(angle - Math.PI / 6));
                    ctx.moveTo(toX, toY);
                    ctx.lineTo(toX - size * Math.cos(angle + Math.PI / 6), toY - size * Math.sin(angle + Math.PI / 6));
                    ctx.stroke();
                }
            } catch (error) {
                console.error('Error drawing load arrow:', error);
                throw error;
            }
        }
    };
})();

// Production ready - only log in development
if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost') {
    console.log('✅ Drawing utilities loaded (development mode)');
}
