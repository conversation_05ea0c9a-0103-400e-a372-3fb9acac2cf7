/**
 * Shared Drawing Utilities for GRS Wall Visualizations
 * Contains common drawing functions used across geometry, external loads, and layout visualizations
 */

// Shared drawing utilities namespace
window.DrawingUtils = {
    // Common constants
    BASE_SCALE: 50,
    BASE_X: 100,
    BASE_Y: 400,
    FASCIA_THICKNESS_RATIO: 0.2,

    /**
     * Calculate geometry components for GRS wall
     */
    calculateGeometry: function(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise) {
        const baseScale = this.BASE_SCALE;
        const baseX = this.BASE_X;
        const baseY = this.BASE_Y;
        const batterOffset = Math.tan((wallBatter * Math.PI) / 180) * (wallHeight * baseScale);
        const fasciaThickness = this.FASCIA_THICKNESS_RATIO * baseScale;

        return {
            baseScale,
            baseX,
            baseY,
            batterOffset,
            fasciaThickness,
            
            // Fascia geometry
            fascia: {
                x1: baseX - fasciaThickness, y1: baseY,
                x2: baseX - fasciaThickness + batterOffset, y2: baseY - (wallHeight * baseScale),
                x3: baseX + batterOffset + fasciaThickness, y3: baseY - (wallHeight * baseScale),
                x4: baseX, y4: baseY
            },

            // Reinforced fill geometry
            reinforcedFill: {
                x1: baseX, y1: baseY,
                x2: baseX + batterOffset, y2: baseY - (wallHeight * baseScale),
                x3: baseX + (wallLength * baseScale) + batterOffset, y3: baseY - (wallHeight * baseScale),
                x4: baseX + (wallLength * baseScale), y4: baseY
            },

            // Retained fill geometry
            retainedFill: {
                x1: baseX + (wallLength * baseScale), y1: baseY,
                x2: baseX + (wallLength * baseScale) + batterOffset, y2: baseY - (wallHeight * baseScale),
                x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y3: baseY - (wallHeight * baseScale),
                x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y4: baseY
            },

            // Embedment geometry
            embedment: {
                x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY,
                x2: baseX - 10 + batterOffset, y2: baseY - (embedmentDepth * baseScale)
            },

            // Foundation soil geometry
            foundationSoil: {
                x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY + (0.5 * wallHeight * baseScale),
                x2: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y2: baseY
            }
        };
    },

    /**
     * Calculate backslope geometry
     */
    calculateBackslope: function(geometry, backslopeAngle, backslopeRise) {
        const { reinforcedFill, retainedFill, baseScale } = geometry;
        const backslopeAngleRadians = (backslopeAngle * Math.PI) / 180;
        const slopeBaseLength = backslopeRise / Math.tan(backslopeAngleRadians);
        const slopeStartX = reinforcedFill.x2;
        const slopeStartY = reinforcedFill.y2;
        let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);
        let slopeEndY = slopeStartY - (backslopeRise * baseScale);

        // Determine slope case
        const isCase1 = slopeEndX <= retainedFill.x3;
        
        if (!isCase1) {
            // Case 2: Slope extends beyond retainedFill.x3
            slopeEndX = retainedFill.x3;
            slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));
        }

        return {
            slopeStartX,
            slopeStartY,
            slopeEndX,
            slopeEndY,
            isCase1,
            backslopeAngleRadians
        };
    },

    /**
     * Draw basic GRS wall structure (without loads or reinforcement)
     */
    drawBasicWall: function(ctx, geometry, slope) {
        const { fascia, reinforcedFill, retainedFill, embedment, foundationSoil } = geometry;
        const { slopeStartX, slopeStartY, slopeEndX, slopeEndY, isCase1 } = slope;

        // Draw foundation soil
        ctx.fillStyle = "#A98B6D";
        ctx.fillRect(foundationSoil.x1, foundationSoil.y1, foundationSoil.x2 - foundationSoil.x1, foundationSoil.y2 - foundationSoil.y1);

        // Draw embedment
        ctx.fillStyle = "#A98B6D";
        ctx.fillRect(embedment.x1, embedment.y1, embedment.x2 - embedment.x1, embedment.y2 - embedment.y1);

        // Draw fascia
        ctx.fillStyle = "#666";
        ctx.beginPath();
        ctx.moveTo(fascia.x1, fascia.y1);
        ctx.lineTo(fascia.x2, fascia.y2);
        ctx.lineTo(fascia.x3, fascia.y3);
        ctx.lineTo(fascia.x4, fascia.y4);
        ctx.fill();

        // Draw reinforced fill
        ctx.fillStyle = "#D6B85A";
        ctx.beginPath();
        ctx.moveTo(reinforcedFill.x1, reinforcedFill.y1);
        ctx.lineTo(reinforcedFill.x2, reinforcedFill.y2);
        ctx.lineTo(reinforcedFill.x3, reinforcedFill.y3);
        ctx.lineTo(reinforcedFill.x4, reinforcedFill.y4);
        ctx.fill();

        // Draw retained fill
        ctx.fillStyle = "#D2B48C";
        ctx.beginPath();
        ctx.moveTo(retainedFill.x1, retainedFill.y1);
        ctx.lineTo(retainedFill.x2, retainedFill.y2);
        ctx.lineTo(retainedFill.x3, retainedFill.y3);
        ctx.lineTo(retainedFill.x4, retainedFill.y4);
        ctx.fill();

        // Draw backslope
        ctx.fillStyle = "#FFA500"; // Soil color
        ctx.strokeStyle = "#000";
        ctx.lineWidth = 1;

        if (isCase1) {
            // Case 1: Slope ends before or exactly at retainedFill.x3
            const horizontalEndX = retainedFill.x3;
            const horizontalEndY = slopeEndY;

            // Draw slope line
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.stroke();

            // Draw horizontal extension
            ctx.beginPath();
            ctx.moveTo(slopeEndX, slopeEndY);
            ctx.lineTo(horizontalEndX, horizontalEndY);
            ctx.stroke();

            // Fill area (Trapezoidal)
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.lineTo(horizontalEndX, horizontalEndY);
            ctx.lineTo(retainedFill.x3, retainedFill.y3);
            ctx.closePath();
            ctx.fill();
        } else {
            // Case 2: Slope extends beyond retainedFill.x3
            // Draw slope line
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.stroke();

            // Fill area (Triangular)
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.lineTo(retainedFill.x3, retainedFill.y3);
            ctx.closePath();
            ctx.fill();
        }
    },

    /**
     * Draw dimension arrows and labels
     */
    drawDimensions: function(ctx, geometry, wallHeight, wallLength, embedmentDepth, backslopeRise, slope) {
        const { baseX, baseY, baseScale, fasciaThickness } = geometry;
        const { isCase1, slopeEndY } = slope;

        ctx.strokeStyle = "#000";
        ctx.lineWidth = 1;
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";

        // Wall height dimension
        ctx.beginPath();
        ctx.moveTo(baseX - 20, baseY);
        ctx.lineTo(baseX - 20, baseY - (wallHeight * baseScale));
        ctx.stroke();
        this.drawArrow(ctx, baseX - 20, baseY, baseX - 20, baseY - (wallHeight * baseScale), 0.05);
        ctx.textAlign = "center";
        ctx.textBaseline = "top";
        ctx.fillText(`${wallHeight} m`, baseX - 40, baseY - (0.5 * wallHeight * baseScale) - 10);

        // Wall length dimension
        ctx.beginPath();
        ctx.moveTo(baseX, baseY);
        ctx.lineTo(baseX + (wallLength * baseScale), baseY);
        ctx.stroke();
        this.drawArrow(ctx, baseX, baseY, baseX + (wallLength * baseScale), baseY, 0.05);
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`${wallLength} m`, baseX + ((wallLength * baseScale) / 2), baseY + 10);

        // Embedment depth dimension
        ctx.beginPath();
        ctx.moveTo(baseX - 40, baseY);
        ctx.lineTo(baseX - 40, baseY - (embedmentDepth * baseScale));
        ctx.stroke();
        this.drawArrow(ctx, baseX - 40, baseY, baseX - 40, baseY - (embedmentDepth * baseScale), 0.2);
        ctx.textAlign = "center";
        ctx.textBaseline = "top";
        ctx.fillText(`${embedmentDepth} m`, baseX - 50 - fasciaThickness, baseY - (0.5 * embedmentDepth * baseScale) - 10);
    },

    /**
     * Draw arrow between two points
     */
    drawArrow: function(ctx, x1, y1, x2, y2, arrowSize = 0.05) {
        ctx.strokeStyle = "#000";
        ctx.fillStyle = "#000";

        const dx = x2 - x1;
        const dy = y2 - y1;
        const angle = Math.atan2(dy, dx);
        const arrowLength = Math.sqrt(dx * dx + dy * dy) * arrowSize;

        // Draw arrow head at the end
        ctx.save();
        ctx.translate(x2, y2);
        ctx.rotate(angle);
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(-arrowLength, -arrowLength / 2);
        ctx.lineTo(-arrowLength, arrowLength / 2);
        ctx.closePath();
        ctx.fill();
        ctx.restore();

        // Draw arrow head at the start
        ctx.save();
        ctx.translate(x1, y1);
        ctx.rotate(angle + Math.PI);
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(-arrowLength, -arrowLength / 2);
        ctx.lineTo(-arrowLength, arrowLength / 2);
        ctx.closePath();
        ctx.fill();
        ctx.restore();

        // Draw the arrow line
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
    },

    /**
     * Add labels to different sections
     */
    drawLabels: function(ctx, geometry, wallLength) {
        const { baseX, baseY, baseScale } = geometry;
        
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        // Reinforced fill label
        ctx.fillText(`Reinforced Fill`, baseX + ((0.5 * wallLength * baseScale)), baseY - (0.5 * wallLength * baseScale) - 10);

        // Retained fill label
        ctx.fillText(`Retained Fill`, baseX + ((1.5 * wallLength * baseScale)), baseY - (0.5 * wallLength * baseScale) - 10);

        // Foundation soil label
        ctx.fillText(`Foundation Soil`, baseX + ((1.0 * wallLength * baseScale)), baseY + (0.25 * wallLength * baseScale) - 10);
    }
};

console.log('✅ Drawing utilities loaded');
