<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login</title>
  </head>
  <body>
    {% extends "base.html" %} {% block content %}
    <div class="hero">
      <h1>Welcome to GRS Wall Designer</h1>
      <p class="tagline">
        Innovative Solutions for Geosynthetic Reinforced Soil Walls
      </p>
    </div>

    <div class="features">
      <div class="feature">
        <i class="fas fa-cogs"></i>
        <h2>Precise Calculations</h2>
        <p>
          Our advanced algorithms ensure accurate design and analysis of GRS
          walls.
        </p>
      </div>
      <div class="feature">
        <i class="fas fa-chart-line"></i>
        <h2>Comprehensive Reports</h2>
        <p>Generate detailed reports with visualizations for your projects.</p>
      </div>
      <div class="feature">
        <i class="fas fa-users"></i>
        <h2>User-Friendly Interface</h2>
        <p>
          Intuitive design makes it easy for engineers of all levels to use.
        </p>
      </div>
    </div>

    <div class="cta">
      <button id="get-started-btn" class="cta-button">Get Started</button>
    </div>

    <div id="login-form" style="display: none">
      <h2>Login</h2>
      <form method="POST" action="{{ url_for('login') }}">
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" required /><br />
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required /><br />
        <button type="submit">Login</button>
      </form>
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %}
      <ul>
        {% for category, message in messages %}
        <li
          class="alert alert-{{ 'danger' if category == 'error' else 'success' }}"
        >
          {{ message }}
        </li>
        {% endfor %}
      </ul>
      {% endif %} {% endwith %}
    </div>

    <script>
      document
        .getElementById("get-started-btn")
        .addEventListener("click", function () {
          document.getElementById("login-form").style.display = "block";
          this.style.display = "none";
        });
    </script>
    {% endblock %}
  </body>
</html>
