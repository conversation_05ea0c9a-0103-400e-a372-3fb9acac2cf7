{% extends "base.html" %} {% block content %}
<form id="externalloadsform">
  <div class="container">
    <h3>External Loads Input</h3>

    <div
      class="button-group"
      style="
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-bottom: 20px;
      "
    >
      <button
        type="button"
        class="tab-button"
        onclick="showSection('dead_loads')"
      >
        Dead Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('live_loads')"
      >
        Live Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('strip_loads')"
      >
        Strip Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('earthquake_loads')"
      >
        Earthquake Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('impact_loads')"
      >
        Impact Loads
      </button>
    </div>

    <div class="load-sections">
      <!-- Dead Loads Section -->
      <div id="dead_loads" class="load-input-section">
        <h3>Dead Loads</h3>
        <div class="input-group">
          <label for="dead_load1">Dead Load 1 (kN/m²):</label>
          <input type="number" id="dead_load1" name="dead_load1" />
        </div>
        <div class="input-group">
          <label for="dead_load2">Dead Load 2 (kN/m²):</label>
          <input type="number" id="dead_load2" name="dead_load2" />
        </div>
        <div class="input-group">
          <label for="dead_load3">Dead Load 3 (kN/m²):</label>
          <input type="number" id="dead_load3" name="dead_load3" />
        </div>
      </div>

      <!-- Live Loads Section -->
      <div id="live_loads" class="load-input-section">
        <h3>Live Loads</h3>
        <div class="input-group">
          <label for="live_load1">Live Load 1 (kN/m²):</label>
          <input type="number" id="live_load1" name="live_load1" />
        </div>
        <div class="input-group">
          <label for="live_load2">Live Load 2 (kN/m²):</label>
          <input type="number" id="live_load2" name="live_load2" />
        </div>
        <div class="input-group">
          <label for="live_load3">Live Load 3 (kN/m²):</label>
          <input type="number" id="live_load3" name="live_load3" />
        </div>
      </div>

      <!-- Strip Loads Section -->
      <div id="strip_loads" class="load-input-section">
        <h3>Strip Loads</h3>

        <!-- Flexbox Container for Strip Loads -->
        <div class="strip-load-container">
          {% for i in range(1, 4) %}
          <!-- Individual Strip Load Box -->
          <div class="strip-load-box">
            <h4>Strip Load {{ i }}</h4>
            <div class="input-group">
              <label for="vertical_strip_load{{ i }}"
                >Vertical Load (kN/m):</label
              >
              <input
                type="number"
                id="vertical_strip_load{{ i }}"
                name="vertical_strip_load{{ i }}"
              />
            </div>
            <div class="input-group">
              <label for="horizontal_strip_load{{ i }}"
                >Horizontal Load (kN/m):</label
              >
              <input
                type="number"
                id="horizontal_strip_load{{ i }}"
                name="horizontal_strip_load{{ i }}"
              />
            </div>
            <div class="input-group">
              <label for="strip_load_width{{ i }}">Width (m):</label>
              <input
                type="number"
                id="strip_load_width{{ i }}"
                name="strip_load_width{{ i }}"
              />
            </div>
            <div class="input-group">
              <label for="strip_load_distance{{ i }}"
                >Distance from Wall (m):</label
              >
              <input
                type="number"
                id="strip_load_distance{{ i }}"
                name="strip_load_distance{{ i }}"
              />
            </div>
          </div>
          {% endfor %}
        </div>
      </div>

      <!-- Earthquake Loads Section -->
      <div id="earthquake_loads" class="load-input-section">
        <h3>Earthquake Loads</h3>
        <div class="input-group">
          <label for="earthquake_acceleration">Acceleration (g):</label>
          <input
            type="number"
            id="earthquake_acceleration"
            name="earthquake_acceleration"
          />
        </div>
        <div class="input-group">
          <input
            type="checkbox"
            id="use_direct_kh"
            name="use_direct_kh"
            onclick="toggleKhInput()"
          />
          <label for="use_direct_kh"
            >Directly Input Seismic Force for External Stability</label
          >
        </div>
        <div class="input-group">
          <label for="seismic_force"
            >Seismic Horizontal Earth Pressure Force (P):</label
          >
          <input
            type="number"
            id="seismic_force"
            name="seismic_force"
            disabled
          />
        </div>
      </div>

      <!-- Impact Loads Section -->
      <div id="impact_loads" class="load-input-section">
        <h3>Impact Loads</h3>
        <div id="impact_note" class="impact-note">
          Upper layer rupture impact load = 33.5 kN/m.<br />
          Second layer rupture impact load = 8.8 kN/m.<br />
          Upper layer pullout impact load = 19 kN/m - resisted over full
          length<br />
          Second layer pullout impact load = 8.8 kN/m - resisted over full
          length.
        </div>

        {% for type in ["rupture", "pullout"] %} {% for layer in ["upper",
        "second"] %}
        <div class="input-group">
          <label for="{{ type }}_impact_{{ layer }}"
            >{{ type.capitalize() }} Impact Load - {{ layer.capitalize() }}
            Layer (kN/m):</label
          >
          <input
            type="number"
            id="{{ type }}_impact_{{ layer }}"
            name="{{ type }}_impact_{{ layer }}"
          />
        </div>
        {% endfor %} {% endfor %}
      </div>
    </div>
  </div>
</form>

<script src="{{ url_for('static', filename='js/externalloads.js') }}"></script>
{% endblock %}
