/**
 * Layout Visualization Module
 * Handles reinforcement layout canvas drawing and interactions
 * Production-ready IIFE module with comprehensive error handling
 */
(function() {
    'use strict';

    // Check for DrawingUtils dependency
    if (typeof window.DrawingUtils === 'undefined') {
        console.error('DrawingUtils is required but not loaded');
        return;
    }

    /**
     * Initialize Layout Visualization
     * @returns {boolean} Success status
     */
    function initializeLayoutVisualization() {
        try {
            // Only run on reinforcement layout page
            const reinforcementLayoutTable = document.getElementById('reinforcementLayoutTable');
            if (!reinforcementLayoutTable) {
                return false;
            }

            // Retrieve data from localStorage with error handling
            let geometryData, externalloads_data, reinforcementLayoutData;

            try {
                geometryData = JSON.parse(localStorage.getItem('geometryData')) || {};
                externalloads_data = JSON.parse(localStorage.getItem('externalloads_data')) || {};
                reinforcementLayoutData = JSON.parse(localStorage.getItem('reinforcementLayoutData')) || [];
            } catch (error) {
                console.error('Error parsing localStorage data:', error);
                geometryData = {};
                externalloads_data = {};
                reinforcementLayoutData = [];
            }

            /**
             * Get current geometry values from form fields or localStorage
             * @returns {Object} Current geometry data
             */
            function getCurrentGeometryData() {
                const defaultData = {
                    wallHeight: 5.0,
                    embedmentDepth: 1.0,
                    wallLength: 6.0,
                    wallBatter: 0.0,
                    backslopeAngle: 0.0,
                    backslopeRise: 2.0
                };

                try {
                    // Try to read current form values first
                    const fields = {
                        wallHeight: document.getElementById('wall-height'),
                        embedmentDepth: document.getElementById('embedment-depth'),
                        wallLength: document.getElementById('wall-length'),
                        wallBatter: document.getElementById('wall-batter'),
                        backslopeAngle: document.getElementById('backslope-angle'),
                        backslopeRise: document.getElementById('backslope-rise')
                    };

                    const currentData = { ...defaultData };

                    Object.keys(fields).forEach(key => {
                        const field = fields[key];
                        if (field && field.value.trim()) {
                            const value = parseFloat(field.value.trim());
                            if (!isNaN(value)) {
                                currentData[key] = value;
                            }
                        }
                    });

                    // Fallback to localStorage if no form data
                    const hasFormData = document.getElementById('wall-height') !== null;
                    if (!hasFormData && geometryData && Object.keys(geometryData).length > 0) {
                        currentData.wallHeight = geometryData.wallHeight || geometryData['wall-height'] || defaultData.wallHeight;
                        currentData.embedmentDepth = geometryData.embedmentDepth || geometryData['embedment-depth'] || defaultData.embedmentDepth;
                        currentData.wallLength = geometryData.wallLength || geometryData['wall-length'] || defaultData.wallLength;
                        currentData.wallBatter = geometryData.wallBatter || geometryData['wall-batter'] || defaultData.wallBatter;
                        currentData.backslopeAngle = geometryData.backslopeAngle || geometryData['backslope-angle'] || defaultData.backslopeAngle;
                        currentData.backslopeRise = geometryData.backslopeRise || geometryData['backslope-rise'] || defaultData.backslopeRise;
                    }

                    return currentData;
                } catch (error) {
                    console.error('Error getting geometry data:', error);
                    return defaultData;
                }
            }

            // Extract load data with defaults
            const dead_loads = externalloads_data.dead_loads || [0, 0, 0];
            const live_loads = externalloads_data.live_loads || [0, 0, 0];
            const vertical_strip_load = externalloads_data.vertical_strip_load || [0, 0, 0];
            const horizontal_strip_load = externalloads_data.horizontal_strip_load || [0, 0, 0];
            const strip_load_width = externalloads_data.strip_load_width || [0, 0, 0];
            const strip_load_distance = externalloads_data.strip_load_distance || [0, 0, 0];
            const earthquake_acceleration = externalloads_data.earthquake_acceleration || 0;

            // Canvas setup
            const canvas = document.getElementById('geometry2-canvas');
            if (!canvas) {
                console.error('Canvas not found');
                return false;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Failed to get canvas context');
                return false;
            }

            // Canvas state variables
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let isDown = false;
            let lastX, lastY;

            canvas.width = 1000;
            canvas.height = 600;

            /**
             * Main drawing function for layout visualization
             * @param {number} wallHeight - Wall height in meters
             * @param {number} embedmentDepth - Embedment depth in meters
             * @param {number} wallLength - Wall length in meters
             * @param {number} wallBatter - Wall batter in degrees
             * @param {number} backslopeAngle - Backslope angle in degrees
             * @param {number} backslopeRise - Backslope rise in meters
             * @param {Array} dead_loads - Dead load values
             * @param {Array} live_loads - Live load values
             * @param {Array} vertical_strip_load - Vertical strip load values
             * @param {Array} horizontal_strip_load - Horizontal strip load values
             * @param {Array} strip_load_width - Strip load width values
             * @param {Array} strip_load_distance - Strip load distance values
             * @param {number} earthquake_acceleration - Earthquake acceleration
             * @param {Array} reinforcementLayout - Reinforcement layout data
             * @returns {void}
             */
            function drawGRSWall(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise,
                               dead_loads, live_loads, vertical_strip_load, horizontal_strip_load, strip_load_width,
                               strip_load_distance, earthquake_acceleration, reinforcementLayout) {
                try {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    ctx.save();
                    ctx.translate(translateX, translateY);
                    ctx.scale(scale, scale);

                    // Use DrawingUtils for consistent geometry calculations
                    const geometry = window.DrawingUtils.calculateGeometry(
                        wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise
                    );

                    // Calculate backslope if needed
                    let slope = null;
                    if (backslopeAngle > 0) {
                        slope = window.DrawingUtils.calculateBackslope(geometry, backslopeAngle, backslopeRise);
                    }

                    // Draw basic wall structure using DrawingUtils
                    window.DrawingUtils.drawBasicWall(ctx, geometry, slope);

                    // Draw dimensions and labels
                    window.DrawingUtils.drawDimensions(ctx, geometry, wallHeight, wallLength, embedmentDepth);
                    window.DrawingUtils.drawLabels(ctx, geometry, wallLength);

                    // Draw external loads using DrawingUtils
                    const loadData = {
                        dead_loads: dead_loads,
                        live_loads: live_loads,
                        vertical_strip_load: vertical_strip_load,
                        horizontal_strip_load: horizontal_strip_load,
                        strip_load_width: strip_load_width,
                        strip_load_distance: strip_load_distance,
                        earthquake_acceleration: earthquake_acceleration
                    };

                    window.DrawingUtils.drawExternalLoads(ctx, geometry, slope, loadData);

                    // Draw reinforcement layout with color coding
                    drawReinforcementLayout(ctx, geometry, reinforcementLayout);

                    ctx.restore();
                } catch (error) {
                    console.error('Error drawing GRS wall:', error);
                    ctx.restore();
                }
            }

            /**
             * Draw reinforcement layout with color coding
             * @param {CanvasRenderingContext2D} ctx - Canvas context
             * @param {Object} geometry - Geometry data from DrawingUtils
             * @param {Array} reinforcementLayout - Reinforcement layout data
             * @returns {void}
             */
            function drawReinforcementLayout(ctx, geometry, reinforcementLayout) {
                if (!reinforcementLayout || reinforcementLayout.length === 0) return;

                const gradeColors = {};
                const getRandomColor = () => `rgb(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)})`;

                reinforcementLayout.forEach(data => {
                    try {
                        const { location, length, reinforcement_type } = data;
                        if (!location || !length || !reinforcement_type) return;

                        const locationFromBottom = location * geometry.baseScale;
                        const lengthOfReinforcement = length * geometry.baseScale;

                        if (!gradeColors[reinforcement_type]) {
                            gradeColors[reinforcement_type] = getRandomColor();
                        }
                        const color = gradeColors[reinforcement_type];

                        const yPos = geometry.reinforcedFill.y1 - locationFromBottom;
                        const startX = geometry.reinforcedFill.x1 + Math.tan((geometry.wallBatter * Math.PI) / 180) * locationFromBottom;
                        const endX = startX + lengthOfReinforcement;

                        // Draw reinforcement line
                        ctx.strokeStyle = color;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(startX, yPos);
                        ctx.lineTo(endX, yPos);
                        ctx.stroke();

                        // Add label
                        ctx.fillStyle = color;
                        ctx.font = "12px Arial";
                        ctx.fillText(`L = ${(lengthOfReinforcement / geometry.baseScale).toFixed(2)} m, ${reinforcement_type}`,
                                   startX + lengthOfReinforcement / 2 - 30, yPos - 10);
                    } catch (error) {
                        console.error('Error drawing reinforcement:', error);
                    }
                });
            }







            /**
             * Helper function to redraw canvas with current values
             * @returns {void}
             */
            function redrawCanvas() {
                try {
                    const currentGeometry = getCurrentGeometryData();

                    drawGRSWall(
                        currentGeometry.wallHeight,
                        currentGeometry.embedmentDepth,
                        currentGeometry.wallLength,
                        currentGeometry.wallBatter,
                        currentGeometry.backslopeAngle,
                        currentGeometry.backslopeRise,
                        dead_loads, live_loads,
                        vertical_strip_load, horizontal_strip_load, strip_load_width,
                        strip_load_distance, earthquake_acceleration, reinforcementLayoutData
                    );
                } catch (error) {
                    console.error('Error redrawing canvas:', error);
                }
            }



            // Set up zoom controls with error handling
            const zoomInButton = document.getElementById('zoom-in-button');
            const zoomOutButton = document.getElementById('zoom-out-button');
            if (zoomInButton && zoomOutButton) {
                zoomInButton.addEventListener('click', function() {
                    try {
                        const cursorX = canvas.width / 2;
                        const cursorY = canvas.height / 2;
                        const oldScale = scale;
                        scale *= 1.1;
                        translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
                        translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
                        redrawCanvas();
                    } catch (error) {
                        console.error('Error zooming in:', error);
                    }
                });

                zoomOutButton.addEventListener('click', function() {
                    try {
                        const cursorX = canvas.width / 2;
                        const cursorY = canvas.height / 2;
                        const oldScale = scale;
                        scale *= 0.9;
                        translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
                        translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
                        redrawCanvas();
                    } catch (error) {
                        console.error('Error zooming out:', error);
                    }
                });
            }

            // Set up fit-to-window button
            const fitButton = document.getElementById('fit-button');
            if (fitButton) {
                fitButton.addEventListener('click', function() {
                    try {
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        redrawCanvas();
                    } catch (error) {
                        console.error('Error fitting to window:', error);
                    }
                });
            }

            // Set up mouse wheel zoom
            canvas.addEventListener('wheel', function(e) {
                try {
                    e.preventDefault();
                    const rect = canvas.getBoundingClientRect();
                    const cursorX = e.clientX - rect.left;
                    const cursorY = e.clientY - rect.top;

                    const oldScale = scale;
                    scale *= e.deltaY > 0 ? 0.9 : 1.1;

                    translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
                    translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);

                    redrawCanvas();
                } catch (error) {
                    console.error('Error handling wheel zoom:', error);
                }
            });

            // Set up panning functionality
            canvas.addEventListener('mousedown', function(e) {
                try {
                    isDown = true;
                    canvas.style.cursor = 'grabbing';
                    const rect = canvas.getBoundingClientRect();
                    lastX = e.clientX - rect.left;
                    lastY = e.clientY - rect.top;
                } catch (error) {
                    console.error('Error handling mouse down:', error);
                }
            });

            canvas.addEventListener('mouseup', function() {
                try {
                    isDown = false;
                    canvas.style.cursor = 'grab';
                } catch (error) {
                    console.error('Error handling mouse up:', error);
                }
            });

            canvas.addEventListener('mousemove', function(e) {
                try {
                    if (isDown) {
                        const rect = canvas.getBoundingClientRect();
                        const currentX = e.clientX - rect.left;
                        const currentY = e.clientY - rect.top;

                        translateX += currentX - lastX;
                        translateY += currentY - lastY;

                        lastX = currentX;
                        lastY = currentY;

                        redrawCanvas();
                    }
                } catch (error) {
                    console.error('Error handling mouse move:', error);
                }
            });

    // Enhanced redraw function that can accept updated layout data
    window.redrawCanvas = function(updatedReinforcementLayout) {
        console.log("🎨 redrawCanvas called with updated layout:", updatedReinforcementLayout);

        // ALWAYS reload fresh data from localStorage to get latest geometry and external loads
        let freshGeometryData, freshExternalLoadsData, freshLayoutData;

        try {
            freshGeometryData = JSON.parse(localStorage.getItem('geometryData')) || {};
            freshExternalLoadsData = JSON.parse(localStorage.getItem('externalloads_data')) || {};
            freshLayoutData = updatedReinforcementLayout || JSON.parse(localStorage.getItem('reinforcementLayoutData')) || [];

            console.log("🎨 Fresh data loaded:", {
                geometry: freshGeometryData,
                externalLoads: freshExternalLoadsData,
                layout: freshLayoutData
            });
        } catch (error) {
            console.error('Error loading fresh data:', error);
            // Fallback to existing data
            freshGeometryData = geometryData;
            freshExternalLoadsData = externalloads_data;
            freshLayoutData = updatedReinforcementLayout || reinforcementLayoutData;
        }

        // Update global variables with fresh data
        geometryData = freshGeometryData;
        externalloads_data = freshExternalLoadsData;
        if (updatedReinforcementLayout) {
            reinforcementLayoutData = updatedReinforcementLayout;
        } else {
            reinforcementLayoutData = freshLayoutData;
        }

        // Extract fresh values (using template placeholder defaults)
        const freshWallHeight = geometryData.wallHeight || geometryData['wall-height'] || 5;
        const freshEmbedmentDepth = geometryData.embedmentDepth || geometryData['embedment-depth'] || 1;
        const freshWallLength = geometryData.wallLength || geometryData['wall-length'] || 6;
        const freshWallBatter = geometryData.wallBatter || geometryData['wall-batter'] || 0;
        const freshBackslopeAngle = geometryData.backslopeAngle || geometryData['backslope-angle'] || 0;
        const freshBackslopeRise = geometryData.backslopeRise || geometryData['backslope-rise'] || 2;

        const freshDeadLoads = externalloads_data.dead_loads || [0, 0, 0];
        const freshLiveLoads = externalloads_data.live_loads || [0, 0, 0];
        const freshVerticalStripLoad = externalloads_data.vertical_strip_load || [0, 0, 0];
        const freshHorizontalStripLoad = externalloads_data.horizontal_strip_load || [0, 0, 0];
        const freshStripLoadWidth = externalloads_data.strip_load_width || [0, 0, 0];
        const freshStripLoadDistance = externalloads_data.strip_load_distance || [0, 0, 0];
        const freshEarthquakeAcceleration = externalloads_data.earthquake_acceleration || 0;
        // Note: freshSeismicForce and freshImpactLoads removed as they were unused

        console.log("🎨 Drawing with fresh values:", {
            wallHeight: freshWallHeight,
            embedmentDepth: freshEmbedmentDepth,
            wallLength: freshWallLength,
            layoutData: reinforcementLayoutData
        });

        drawGRSWall(
            freshWallHeight, freshEmbedmentDepth, freshWallLength, freshWallBatter,
            freshBackslopeAngle, freshBackslopeRise, freshDeadLoads, freshLiveLoads,
            freshVerticalStripLoad, freshHorizontalStripLoad, freshStripLoadWidth,
            freshStripLoadDistance, freshEarthquakeAcceleration, reinforcementLayoutData
        );
    };

    // Function to update reinforcement layout data from current form values
    function updateReinforcementLayoutFromForm() {
        const table = document.getElementById('reinforcementLayoutTable');
        if (!table) return;

        const updatedLayout = [];
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach((row) => {
            const locationInput = row.querySelector('input[name="location"]');
            const lengthInput = row.querySelector('input[name="length"]');
            const typeSelect = row.querySelector('select[name="reinforcement_type"]');

            if (locationInput && lengthInput && typeSelect) {
                const location = parseFloat(locationInput.value);
                const length = parseFloat(lengthInput.value);
                const type = typeSelect.value;

                if (!isNaN(location) && !isNaN(length) && type) {
                    updatedLayout.push({
                        location: location,
                        length: length,
                        reinforcement_type: type
                    });
                }
            }
        });

        // Update the global reinforcement layout data
        reinforcementLayoutData = updatedLayout;
        console.log("Updated reinforcement layout data:", reinforcementLayoutData);

        // Redraw with updated data
        window.redrawCanvas(reinforcementLayoutData);
    }

    // Make the update function available globally for jQuery integration
    window.updateReinforcementLayoutFromForm = updateReinforcementLayoutFromForm;

    // Initial draw
    redrawCanvas();

    // Set up screenshot button - only for reinforcement layout page
    const screenshotButton = document.getElementById('screenshot-button');
    const layoutTable = document.getElementById('reinforcementLayoutTable');
    if (screenshotButton && layoutTable) {
        // Clear ALL existing event listeners by cloning the button
        const newScreenshotButton = screenshotButton.cloneNode(true);
        screenshotButton.parentNode.replaceChild(newScreenshotButton, screenshotButton);

        // Add only our handler
        newScreenshotButton.addEventListener('click', handleLayoutScreenshot);
        console.log("Attached layout screenshot button listener (cleaned)");
    } else {
        console.log("Screenshot button setup failed:", {
            screenshotButton: !!screenshotButton,
            layoutTable: !!layoutTable
        });
    }

    function handleLayoutScreenshot() {
        console.log("Layout screenshot button clicked");

        // Only handle if we're on reinforcement layout page
        const layoutTable = document.getElementById('reinforcementLayoutTable');
        if (!layoutTable) {
            console.log("Layout table not found - not on reinforcement layout page");
            return;
        }
        const canvas = document.getElementById('geometry2-canvas');
        if (!canvas) {
            console.error("Canvas not found!");
            if (typeof showErrorPopup === "function") {
                showErrorPopup("Canvas not found!");
            } else {
                alert("Canvas not found!");
            }
            return;
        }

        console.log("Taking screenshot of reinforcement layout canvas");
        const dataURL = canvas.toDataURL('image/png');

        // Store screenshot on server for report generation
        storeScreenshotOnServer(dataURL);

        // Download the image (same as geometry and external loads)
        const link = document.createElement('a');
        link.href = dataURL;
        link.download = 'grs_wall_reinforcement_layout.png';
        link.click();
        console.log("Reinforcement layout screenshot downloaded");
    }

    // Function to store screenshot on server for report generation
    function storeScreenshotOnServer(dataURL) {
        fetch('/store-screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                screenshot: dataURL
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Screenshot stored successfully for report generation:', data.screenshot_id);
            } else {
                console.error('Failed to store screenshot:', data.message);
            }
        })
        .catch(error => {
            console.error('Error storing screenshot:', error);
        });
    }

    // Function to automatically capture and store screenshot for report
    function captureLayoutScreenshotForReport() {
        const canvas = document.getElementById('geometry2-canvas');
        if (canvas) {
            console.log("Auto-capturing layout screenshot for report");
            const dataURL = canvas.toDataURL('image/png');
            storeScreenshotOnServer(dataURL);
        }
    }

    // Make the auto-capture function available globally
    window.captureLayoutScreenshotForReport = captureLayoutScreenshotForReport;

    // Auto-capture screenshot when visualization is updated (for report generation)
    function autoCapture() {
        // Wait a bit for the canvas to be fully rendered
        setTimeout(() => {
            const canvas = document.getElementById('geometry2-canvas');
            if (canvas) {
                console.log("Auto-capturing screenshot for report generation");
                captureLayoutScreenshotForReport();
            }
        }, 1000);
    }

    // Hook into redrawCanvas to auto-capture screenshots
    const originalRedrawCanvas = window.redrawCanvas;
    if (originalRedrawCanvas) {
        window.redrawCanvas = function() {
            originalRedrawCanvas.apply(this, arguments);
            autoCapture();
        };
    }
};

// Initialize on DOM ready and make available for AJAX reloads
document.addEventListener('DOMContentLoaded', function() {
    console.log("🎨 DOM ready - calling initializeLayoutVisualization");
    window.initializeLayoutVisualization();
});

// Also make it available immediately for synchronous calls
console.log("🎨 LayoutVisualization.js loaded - function available globally");

