/**
 * Layout Visualization Module
 * Handles reinforcement layout canvas drawing and interactions
 */

console.log("🎨 LayoutVisualization.js script starting to load...");

// Make the function available globally immediately
window.initializeLayoutVisualization = function initializeLayoutVisualization() {
    console.log('Initializing Layout Visualization...');

    // Only run on reinforcement layout page - check for reinforcement layout table
    const reinforcementLayoutTable = document.getElementById('reinforcementLayoutTable');
    if (!reinforcementLayoutTable) {
        console.log('Reinforcement layout table not found - skipping layout visualization (probably on different page)');
        return;
    }

    console.log('Reinforcement layout table found - proceeding with visualization initialization');

    // Retrieve data from localStorage with error handling
    let geometryData, externalloads_data, reinforcementLayoutData;

    try {
        geometryData = JSON.parse(localStorage.getItem('geometryData')) || {};
        externalloads_data = JSON.parse(localStorage.getItem('externalloads_data')) || {};
        reinforcementLayoutData = JSON.parse(localStorage.getItem('reinforcementLayoutData')) || [];

        console.log('🎨 Loaded geometry data:', geometryData);
        console.log('🎨 Loaded external loads data:', externalloads_data);
        console.log('🎨 Loaded reinforcement layout data:', reinforcementLayoutData);

        // Debug: Check raw localStorage values
        console.log('🎨 Raw localStorage externalloads_data:', localStorage.getItem('externalloads_data'));
    } catch (error) {
        console.error('Error parsing localStorage data:', error);
        geometryData = {};
        externalloads_data = {};
        reinforcementLayoutData = [];
    }

    // Function to get current geometry values from form fields (if available) or localStorage
    function getCurrentGeometryData() {
        let currentData = {
            wallHeight: 5.0,      // template placeholder value
            embedmentDepth: 1.0,  // template placeholder value
            wallLength: 6.0,      // template placeholder value
            wallBatter: 0.0,      // template placeholder value
            backslopeAngle: 0.0,  // template placeholder value
            backslopeRise: 2.0    // template placeholder value
        };

        // Try to read current form values first (for live updates)
        try {
            const wallHeightField = document.getElementById('wall-height');
            const embedmentDepthField = document.getElementById('embedment-depth');
            const wallLengthField = document.getElementById('wall-length');
            const wallBatterField = document.getElementById('wall-batter');
            const backslopeAngleField = document.getElementById('backslope-angle');
            const backslopeRiseField = document.getElementById('backslope-rise');

            if (wallHeightField) {
                // Use current form value if it exists, otherwise use placeholder
                const value = wallHeightField.value.trim();
                currentData.wallHeight = value ? parseFloat(value) : 5.0;
            }
            if (embedmentDepthField) {
                const value = embedmentDepthField.value.trim();
                currentData.embedmentDepth = value ? parseFloat(value) : 1.0;
            }
            if (wallLengthField) {
                const value = wallLengthField.value.trim();
                currentData.wallLength = value ? parseFloat(value) : 6.0;
            }
            if (wallBatterField) {
                const value = wallBatterField.value.trim();
                currentData.wallBatter = value ? parseFloat(value) : 0.0;
            }
            if (backslopeAngleField) {
                const value = backslopeAngleField.value.trim();
                currentData.backslopeAngle = value ? parseFloat(value) : 0.0;
            }
            if (backslopeRiseField) {
                const value = backslopeRiseField.value.trim();
                currentData.backslopeRise = value ? parseFloat(value) : 2.0;
            }

            console.log('🎨 Read current geometry form values:', currentData);
        } catch (error) {
            console.log('🎨 Could not read geometry form values (probably not on geometry page):', error);
        }

        // If no form values were found, fall back to localStorage data
        const hasFormData = document.getElementById('wall-height') !== null;

        if (!hasFormData && geometryData && Object.keys(geometryData).length > 0) {
            console.log('🎨 Using localStorage geometry data as fallback');
            currentData.wallHeight = geometryData.wallHeight || geometryData['wall-height'] || 5.0;
            currentData.embedmentDepth = geometryData.embedmentDepth || geometryData['embedment-depth'] || 1.0;
            currentData.wallLength = geometryData.wallLength || geometryData['wall-length'] || 6.0;
            currentData.wallBatter = geometryData.wallBatter || geometryData['wall-batter'] || 0.0;
            currentData.backslopeAngle = geometryData.backslopeAngle || geometryData['backslope-angle'] || 0.0;
            currentData.backslopeRise = geometryData.backslopeRise || geometryData['backslope-rise'] || 2.0;
        } else if (!hasFormData) {
            console.log('🎨 No localStorage geometry data found - using template placeholder values for visualization only (NOT setting localStorage)');
            // Keep the default placeholder values already set in currentData
            // DO NOT set localStorage here - just use placeholders for visualization
        }

        console.log('🎨 Final geometry data for visualization:', currentData);
        return currentData;
    }

    // Note: Geometry data is accessed dynamically in functions as needed

    const dead_loads = externalloads_data.dead_loads || [0, 0, 0];
    const live_loads = externalloads_data.live_loads || [0, 0, 0];
    const vertical_strip_load = externalloads_data.vertical_strip_load || [0, 0, 0];
    const horizontal_strip_load = externalloads_data.horizontal_strip_load || [0, 0, 0];
    const strip_load_width = externalloads_data.strip_load_width || [0, 0, 0];
    const strip_load_distance = externalloads_data.strip_load_distance || [0, 0, 0];
    const earthquake_acceleration = externalloads_data.earthquake_acceleration || 0;
    // Note: seismic_force and impact_loads removed as they were unused in layout visualization

    console.log('🎨 Using loads data:', {
        dead_loads: dead_loads,
        live_loads: live_loads,
        deadSum: dead_loads.reduce((a, b) => a + b, 0),
        liveSum: live_loads.reduce((a, b) => a + b, 0)
    });

    console.log('Layout visualization data loaded successfully');

    // Canvas setup
    const canvas = document.getElementById('geometry2-canvas');
    if (!canvas) {
        console.error('Canvas not found');
        return;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('Failed to get canvas context');
        return;
    }

    // Canvas state variables
    let scale = 1;
    let translateX = 0;
    let translateY = 0;
    let isDown = false;
    let lastX, lastY;

    canvas.width = 1000;
    canvas.height = 600;

    // Main drawing function
    function drawGRSWall(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise,
                       dead_loads, live_loads, vertical_strip_load, horizontal_strip_load, strip_load_width,
                       strip_load_distance, earthquake_acceleration, reinforcementLayout) {
        // Note: seismic_force and impact_loads parameters removed as they were unused
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        ctx.save();
        ctx.translate(translateX, translateY);
        ctx.scale(scale, scale);

        const baseScale = 50;
        const baseX = 100, baseY = 400;
        const batterOffset = Math.tan((wallBatter * Math.PI) / 180) * (wallHeight * baseScale);
        const fasciaThickness = 0.2 * baseScale;

        // Define geometry components
        const fascia = {
            x1: baseX - fasciaThickness, y1: baseY,
            x2: baseX - fasciaThickness + batterOffset, y2: baseY - (wallHeight * baseScale),
            x3: baseX + batterOffset + fasciaThickness, y3: baseY - (wallHeight * baseScale),
            x4: baseX, y4: baseY
        };

        const reinforcedFill = {
            x1: baseX, y1: baseY,
            x2: baseX + batterOffset, y2: baseY - (wallHeight * baseScale),
            x3: baseX + (wallLength * baseScale) + batterOffset, y3: baseY - (wallHeight * baseScale),
            x4: baseX + (wallLength * baseScale), y4: baseY
        };

        const retainedFill = {
            x1: baseX + (wallLength * baseScale), y1: baseY,
            x2: baseX + (wallLength * baseScale) + batterOffset, y2: baseY - (wallHeight * baseScale),
            x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y3: baseY - (wallHeight * baseScale),
            x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y4: baseY
        };

        const embedment = {
            x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY,
            x2: baseX - 10 + batterOffset, y2: baseY - (embedmentDepth * baseScale)
        };

        const foundationSoil = {
            x1: embedment.x1, y1: baseY + (0.5*wallHeight * baseScale),
            x2: retainedFill.x4, y2: baseY
        };

        // Helper function to draw an arrow
        function drawArrow(ctx, x1, y1, x2, y2, arrowSize = 0.05) {
            ctx.strokeStyle = "#000";
            ctx.fillStyle = "#000";

            const dx = x2 - x1;
            const dy = y2 - y1;
            const angle = Math.atan2(dy, dx);
            const arrowLength = Math.sqrt(dx * dx + dy * dy) * arrowSize;

            // Draw arrow head at the end
            ctx.save();
            ctx.translate(x2, y2);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(-arrowLength, -arrowLength / 2);
            ctx.lineTo(-arrowLength, arrowLength / 2);
            ctx.closePath();
            ctx.fill();
            ctx.restore();

            // Draw arrow head at the start
            ctx.save();
            ctx.translate(x1, y1);
            ctx.rotate(angle + Math.PI);
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(-arrowLength, -arrowLength / 2);
            ctx.lineTo(-arrowLength, arrowLength / 2);
            ctx.closePath();
            ctx.fill();
            ctx.restore();

            // Draw the arrow line
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // Calculate slope parameters
        const backslopeAngleRadians = (backslopeAngle * Math.PI) / 180;
        const slopeBaseLength = backslopeAngle > 0 ? backslopeRise / Math.tan(backslopeAngleRadians) : 0;
        const slopeStartX = reinforcedFill.x2;
        const slopeStartY = reinforcedFill.y2;
        let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);
        let slopeEndY = slopeStartY - (backslopeRise * baseScale);

        // Draw backslope
        ctx.fillStyle = "#FFA500"; // Soil color
        if (backslopeAngle > 0) {
            if (slopeEndX <= retainedFill.x3) {
                // Case 1: Slope ends before or at retainedFill.x3
                const horizontalEndX = retainedFill.x3;
                const horizontalEndY = slopeEndY;

                // Draw slope lines
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(slopeEndX, slopeEndY);
                ctx.lineTo(horizontalEndX, horizontalEndY);
                ctx.stroke();

                // Fill area (Trapezoidal)
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.lineTo(horizontalEndX, horizontalEndY);
                ctx.lineTo(retainedFill.x3, retainedFill.y3);
                ctx.closePath();
                ctx.fill();

                // Display top rise dimension
                const topRise = backslopeRise;
                ctx.beginPath();
                ctx.moveTo(retainedFill.x3, retainedFill.y3);
                ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise * baseScale);
                ctx.stroke();

                drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - topRise * baseScale, 0.1);
                ctx.font = "18px Arial";
                ctx.fillStyle = "#000";
                ctx.textAlign = "center";
                ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 - 40, retainedFill.y3 - (topRise * baseScale) / 2);
            } else {
                // Case 2: Slope extends beyond retainedFill.x3
                slopeEndX = retainedFill.x3;
                slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));

                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.stroke();

                // Fill area (Triangular)
                ctx.beginPath();
                ctx.moveTo(slopeStartX, slopeStartY);
                ctx.lineTo(slopeEndX, slopeEndY);
                ctx.lineTo(retainedFill.x3, retainedFill.y3);
                ctx.closePath();
                ctx.fill();

                // Display top rise dimension
                const topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan(backslopeAngleRadians) / baseScale;
                ctx.beginPath();
                ctx.moveTo(retainedFill.x3, retainedFill.y3);
                ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise * baseScale);
                ctx.stroke();

                drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - (topRise * baseScale), 0.1);
                ctx.font = "18px Arial";
                ctx.fillStyle = "#000";
                ctx.textAlign = "center";
                ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 - 40, retainedFill.y3 - (topRise * 0.5 * baseScale));
            }
        } else {
            // Case when backslope angle is 0 - backslope rise should also be 0
            const topRise = 0.0; // When angle is 0, rise is also 0
            ctx.strokeStyle = "#000";
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(retainedFill.x3, retainedFill.y3);
            ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise * baseScale);
            ctx.stroke();

            drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - topRise * baseScale, 0.1);
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 - 40, retainedFill.y3 - (topRise * baseScale) / 2);
        }

        // Draw foundation soil and embedment with same color (avoid duplicate color setting)
        ctx.fillStyle = "#A98B6D";
        ctx.fillRect(foundationSoil.x1, foundationSoil.y1, foundationSoil.x2 - foundationSoil.x1, foundationSoil.y2 - foundationSoil.y1);
        ctx.fillRect(embedment.x1, embedment.y1, embedment.x2 - embedment.x1, embedment.y2 - embedment.y1);

        // Draw fascia
        ctx.fillStyle = "#666";
        ctx.beginPath();
        ctx.moveTo(fascia.x1, fascia.y1);
        ctx.lineTo(fascia.x2, fascia.y2);
        ctx.lineTo(fascia.x3, fascia.y3);
        ctx.lineTo(fascia.x4, fascia.y4);
        ctx.fill();

        // Draw reinforced fill
        ctx.fillStyle = "#D6B85A";
        ctx.beginPath();
        ctx.moveTo(reinforcedFill.x1, reinforcedFill.y1);
        ctx.lineTo(reinforcedFill.x2, reinforcedFill.y2);
        ctx.lineTo(reinforcedFill.x3, reinforcedFill.y3);
        ctx.lineTo(reinforcedFill.x4, reinforcedFill.y4);
        ctx.fill();

        // Draw retained fill
        ctx.fillStyle = "#D2B48C";
        ctx.beginPath();
        ctx.moveTo(retainedFill.x1, retainedFill.y1);
        ctx.lineTo(retainedFill.x2, retainedFill.y2);
        ctx.lineTo(retainedFill.x3, retainedFill.y3);
        ctx.lineTo(retainedFill.x4, retainedFill.y4);
        ctx.fill();

        // Draw dimensions with arrows
        ctx.strokeStyle = "#000";
        ctx.lineWidth = 1;

        // Draw wall height dimension
        ctx.beginPath();
        ctx.moveTo(baseX - 20, baseY);
        ctx.lineTo(baseX - 20, baseY - (wallHeight * baseScale));
        ctx.stroke();
        drawArrow(ctx, baseX - 20, baseY, baseX - 20, baseY - (wallHeight * baseScale), 0.05);
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "top";
        ctx.fillText(`${wallHeight} m`, baseX - 40, baseY - (0.5 * wallHeight * baseScale) - 10);

        // Draw wall length dimension
        ctx.beginPath();
        ctx.moveTo(baseX, baseY);
        ctx.lineTo(baseX + (wallLength * baseScale), baseY);
        ctx.stroke();
        drawArrow(ctx, baseX, baseY, baseX + (wallLength * baseScale), baseY, 0.05);
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`${wallLength} m`, baseX + ((wallLength * baseScale) / 2), baseY + 10);

        // Draw labels for different sections
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`Reinforced Fill`, baseX + ((0.5 * wallLength * baseScale)), baseY - (0.5 * wallHeight * baseScale) - 10);
        ctx.fillText(`Retained Fill`, baseX + ((1.5 * wallLength * baseScale)), baseY - (0.5 * wallHeight * baseScale) - 10);
        ctx.fillText(`Foundation Soil`, baseX + ((1.0 * wallLength * baseScale)), baseY + (0.25 * wallHeight * baseScale) - 10);

        // Draw embedment depth dimension
        ctx.beginPath();
        ctx.moveTo(baseX - 40, baseY);
        ctx.lineTo(baseX - 40, baseY - (embedmentDepth * baseScale));
        ctx.stroke();
        drawArrow(ctx, baseX - 40, baseY, baseX - 40, baseY - (embedmentDepth * baseScale), 0.2);
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "top";
        ctx.fillText(`${embedmentDepth} m`, baseX - 50 - fasciaThickness, baseY - (0.5 * embedmentDepth * baseScale) - 10);

        // Function to draw arrows (for dead, live, and strip loads) - same as external loads
        function drawArrow1(ctx, fromX, fromY, toX, toY, size, color, hollow = false) {
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            if (hollow) {
                ctx.stroke(); // Draw only the outline for hollow arrows
            } else {
                ctx.lineTo(toX - size * Math.cos(angle - Math.PI / 6), toY - size * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - size * Math.cos(angle + Math.PI / 6), toY - size * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }
        }

        // Function to draw dead and live loads (downward arrows) - exactly same as external loads
        function drawLoads(ctx, startX, endX, y, loadValue, color, label) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.font = "18px Arial";
            ctx.fillStyle = color;
            ctx.textAlign = "center";
            ctx.textBaseline = "top";

            // Draw arrows to represent uniform surcharge (pointing downward)
            const arrowSpacing = 50; // Adjust spacing as needed
            for (let x = startX; x <= endX; x += arrowSpacing) {
                drawArrow1(ctx, x, y, x, y + 20, 5, color); // Arrow points downward
            }

            // Display load value
            ctx.fillText(`${label}: ${loadValue.toFixed(2)} kPa`, (startX + endX) / 2, y - 25);
        }

        // Function to draw strip loads
        function drawStripLoads(ctx, startX, y, verticalStripLoads, horizontalStripLoads, stripLoadWidths, stripLoadDistances) {
            const stripLoadColor = "purple";
            for (let i = 0; i < verticalStripLoads.length; i++) {
                if (verticalStripLoads[i] > 0 || horizontalStripLoads[i] > 0) {
                    const stripLoadX = startX + (stripLoadDistances[i] * baseScale) - (stripLoadWidths[i] * baseScale / 2);
                    const centerX = stripLoadX + (stripLoadWidths[i] * baseScale / 2);

                    if (stripLoadX <= retainedFill.x3) {
                        // Draw strip load as a footing
                        ctx.fillStyle = stripLoadColor;
                        ctx.fillRect(stripLoadX, y, stripLoadWidths[i] * baseScale, 20);

                        // Draw vertical arrow for load
                        if (verticalStripLoads[i] > 0) {
                            drawArrow1(ctx, centerX, y - 40, centerX, y, 5, stripLoadColor);
                            ctx.font = "14px Arial";
                            ctx.fillStyle = stripLoadColor;
                            ctx.textAlign = "center";
                            ctx.textBaseline = "top";
                            ctx.fillText(`${verticalStripLoads[i].toFixed(2)} kN/m`, centerX, y - 30);
                        }

                        // Draw horizontal arrow for load
                        if (horizontalStripLoads[i] > 0) {
                            drawArrow1(ctx, centerX, y - 5, centerX - 30, y - 5, 5, stripLoadColor);
                            ctx.font = "14px Arial";
                            ctx.fillStyle = stripLoadColor;
                            ctx.textAlign = "right";
                            ctx.textBaseline = "middle";
                            ctx.fillText(`${horizontalStripLoads[i].toFixed(2)} kN/m`, centerX - 35, y);
                        }
                    } else {
                        // Display message if beyond influence zone
                        ctx.font = "14px Arial";
                        ctx.fillStyle = stripLoadColor;
                        ctx.textAlign = "center";
                        ctx.textBaseline = "top";
                        ctx.fillText(`Strip Load ${i + 1}: Beyond Influence Zone`, retainedFill.x3 + 50, y - 40);
                    }
                }
            }
        }

        // Function to draw earthquake acceleration (hollow arrow pointing left) - same as external loads
        function drawEarthquakeAcceleration(ctx, x, y, acceleration) {
            const arrowLength = 50; // Length of the arrow
            const arrowSize = 10; // Size of the arrowhead
            const label = `${acceleration.toFixed(2)}g`;

            // Draw hollow arrow (pointing left)
            ctx.strokeStyle = "#000";
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x - arrowLength, y);
            ctx.lineTo(x - arrowLength + arrowSize, y - arrowSize / 2);
            ctx.moveTo(x - arrowLength, y);
            ctx.lineTo(x - arrowLength + arrowSize, y + arrowSize / 2);
            ctx.stroke();

            // Display acceleration value
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(label, x - arrowLength - 30, y);
        }

        // Main drawing logic for loads (same as external loads visualization)
        const deadLoadSum = dead_loads.reduce((a, b) => a + b, 0);
        const liveLoadSum = live_loads.reduce((a, b) => a + b, 0);

        if (backslopeAngle === 0) {
            // Case when backslope angle is zero
            const loadY = reinforcedFill.y2 - 50; // Adjust Y position as needed
            drawLoads(ctx, reinforcedFill.x2, retainedFill.x3, loadY, deadLoadSum, "red", "Dead Load");
            drawLoads(ctx, reinforcedFill.x2, retainedFill.x3, loadY - 50, liveLoadSum, "blue", "Live Load");
        } else if (slopeEndX < retainedFill.x3) {
            // Case 1: Slope ends before or exactly at retainedFill.x3
            const loadY = slopeEndY - 50; // Adjust Y position as needed
            drawLoads(ctx, slopeEndX, retainedFill.x3, loadY, deadLoadSum, "red", "Dead Load");
            drawLoads(ctx, slopeEndX, retainedFill.x3, loadY - 50, liveLoadSum, "blue", "Live Load");
        } else {
            // Case 2: Slope extends beyond retainedFill.x3
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.textBaseline = "top";
            ctx.fillText("Dead and Live Loads: Beyond Influence Zone", (reinforcedFill.x2 + retainedFill.x3) / 2, reinforcedFill.y2 - 50);
        }

        // Draw strip loads
        const stripLoadY = (backslopeAngle === 0 ? reinforcedFill.y2 : slopeEndY) - 20;
        drawStripLoads(ctx, reinforcedFill.x2, stripLoadY, vertical_strip_load, horizontal_strip_load, strip_load_width, strip_load_distance);

        // Draw earthquake acceleration
        drawEarthquakeAcceleration(ctx, (retainedFill.x1 + retainedFill.x3) / 2, (retainedFill.y1 + retainedFill.y3 + 60) / 2, earthquake_acceleration);

        // Draw reinforcements with color coding
        const gradeColors = {};
        const getRandomColor = () => `rgb(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)})`;
        
        if (reinforcementLayout && reinforcementLayout.length > 0) {
            reinforcementLayout.forEach(data => {
                const { location, length, reinforcement_type } = data;
                if (!location || !length || !reinforcement_type) return;
                
                const locationFromBottom = location * baseScale;
                const lengthOfReinforcement = length * baseScale;
                
                if (!gradeColors[reinforcement_type]) {
                    gradeColors[reinforcement_type] = getRandomColor();
                }
                const color = gradeColors[reinforcement_type];
                
                const yPos = reinforcedFill.y1 - locationFromBottom;
                const startX = reinforcedFill.x1 + Math.tan((wallBatter * Math.PI) / 180) * locationFromBottom;
                const endX = reinforcedFill.x1 + Math.tan((wallBatter * Math.PI) / 180) * locationFromBottom + lengthOfReinforcement;
                
                // Draw reinforcement line
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(startX, yPos);
                ctx.lineTo(endX, yPos);
                ctx.stroke();
                
                // Add label
                ctx.fillStyle = color;
                ctx.font = "12px Arial";
                ctx.fillText(`L = ${(lengthOfReinforcement / baseScale).toFixed(2)} m, ${reinforcement_type}`, 
                           startX + lengthOfReinforcement / 2 - 30, yPos - 10);
            });
        }
        
        ctx.restore();
    }

    // Helper function to redraw with current values
    function redrawCanvas() {
        // Get fresh geometry data each time we redraw
        const currentGeometry = getCurrentGeometryData();

        drawGRSWall(
            currentGeometry.wallHeight,
            currentGeometry.embedmentDepth,
            currentGeometry.wallLength,
            currentGeometry.wallBatter,
            currentGeometry.backslopeAngle,
            currentGeometry.backslopeRise,
            dead_loads, live_loads,
            vertical_strip_load, horizontal_strip_load, strip_load_width,
            strip_load_distance, earthquake_acceleration, reinforcementLayoutData
        );
    }



    // Set up zoom controls
    const zoomInButton = document.getElementById('zoom-in-button');
    const zoomOutButton = document.getElementById('zoom-out-button');
    if (zoomInButton && zoomOutButton) {
        zoomInButton.addEventListener('click', function() {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 1.1;
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });

        zoomOutButton.addEventListener('click', function() {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 0.9;
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });
    }

    // Set up fit-to-window button
    const fitButton = document.getElementById('fit-button');
    if (fitButton) {
        fitButton.addEventListener('click', function() {
            scale = 1;
            translateX = 0;
            translateY = 0;
            redrawCanvas();
        });
    }

    // Set up mouse wheel zoom
    canvas.addEventListener('wheel', function(e) {
        e.preventDefault();
        const rect = canvas.getBoundingClientRect();
        const cursorX = e.clientX - rect.left;
        const cursorY = e.clientY - rect.top;
        
        const oldScale = scale;
        scale *= e.deltaY > 0 ? 0.9 : 1.1;
        
        translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
        translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
        
        redrawCanvas();
    });

    // Set up panning functionality
    canvas.addEventListener('mousedown', function(e) {
        isDown = true;
        canvas.style.cursor = 'grabbing';
        const rect = canvas.getBoundingClientRect();
        lastX = e.clientX - rect.left;
        lastY = e.clientY - rect.top;
    });

    canvas.addEventListener('mouseup', function() {
        isDown = false;
        canvas.style.cursor = 'grab';
    });

    canvas.addEventListener('mousemove', function(e) {
        if (isDown) {
            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;
            
            translateX += currentX - lastX;
            translateY += currentY - lastY;
            
            lastX = currentX;
            lastY = currentY;
            
            redrawCanvas();
        }
    });

    // Enhanced redraw function that can accept updated layout data
    window.redrawCanvas = function(updatedReinforcementLayout) {
        console.log("🎨 redrawCanvas called with updated layout:", updatedReinforcementLayout);

        // ALWAYS reload fresh data from localStorage to get latest geometry and external loads
        let freshGeometryData, freshExternalLoadsData, freshLayoutData;

        try {
            freshGeometryData = JSON.parse(localStorage.getItem('geometryData')) || {};
            freshExternalLoadsData = JSON.parse(localStorage.getItem('externalloads_data')) || {};
            freshLayoutData = updatedReinforcementLayout || JSON.parse(localStorage.getItem('reinforcementLayoutData')) || [];

            console.log("🎨 Fresh data loaded:", {
                geometry: freshGeometryData,
                externalLoads: freshExternalLoadsData,
                layout: freshLayoutData
            });
        } catch (error) {
            console.error('Error loading fresh data:', error);
            // Fallback to existing data
            freshGeometryData = geometryData;
            freshExternalLoadsData = externalloads_data;
            freshLayoutData = updatedReinforcementLayout || reinforcementLayoutData;
        }

        // Update global variables with fresh data
        geometryData = freshGeometryData;
        externalloads_data = freshExternalLoadsData;
        if (updatedReinforcementLayout) {
            reinforcementLayoutData = updatedReinforcementLayout;
        } else {
            reinforcementLayoutData = freshLayoutData;
        }

        // Extract fresh values (using template placeholder defaults)
        const freshWallHeight = geometryData.wallHeight || geometryData['wall-height'] || 5;
        const freshEmbedmentDepth = geometryData.embedmentDepth || geometryData['embedment-depth'] || 1;
        const freshWallLength = geometryData.wallLength || geometryData['wall-length'] || 6;
        const freshWallBatter = geometryData.wallBatter || geometryData['wall-batter'] || 0;
        const freshBackslopeAngle = geometryData.backslopeAngle || geometryData['backslope-angle'] || 0;
        const freshBackslopeRise = geometryData.backslopeRise || geometryData['backslope-rise'] || 2;

        const freshDeadLoads = externalloads_data.dead_loads || [0, 0, 0];
        const freshLiveLoads = externalloads_data.live_loads || [0, 0, 0];
        const freshVerticalStripLoad = externalloads_data.vertical_strip_load || [0, 0, 0];
        const freshHorizontalStripLoad = externalloads_data.horizontal_strip_load || [0, 0, 0];
        const freshStripLoadWidth = externalloads_data.strip_load_width || [0, 0, 0];
        const freshStripLoadDistance = externalloads_data.strip_load_distance || [0, 0, 0];
        const freshEarthquakeAcceleration = externalloads_data.earthquake_acceleration || 0;
        // Note: freshSeismicForce and freshImpactLoads removed as they were unused

        console.log("🎨 Drawing with fresh values:", {
            wallHeight: freshWallHeight,
            embedmentDepth: freshEmbedmentDepth,
            wallLength: freshWallLength,
            layoutData: reinforcementLayoutData
        });

        drawGRSWall(
            freshWallHeight, freshEmbedmentDepth, freshWallLength, freshWallBatter,
            freshBackslopeAngle, freshBackslopeRise, freshDeadLoads, freshLiveLoads,
            freshVerticalStripLoad, freshHorizontalStripLoad, freshStripLoadWidth,
            freshStripLoadDistance, freshEarthquakeAcceleration, reinforcementLayoutData
        );
    };

    // Function to update reinforcement layout data from current form values
    function updateReinforcementLayoutFromForm() {
        const table = document.getElementById('reinforcementLayoutTable');
        if (!table) return;

        const updatedLayout = [];
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach((row) => {
            const locationInput = row.querySelector('input[name="location"]');
            const lengthInput = row.querySelector('input[name="length"]');
            const typeSelect = row.querySelector('select[name="reinforcement_type"]');

            if (locationInput && lengthInput && typeSelect) {
                const location = parseFloat(locationInput.value);
                const length = parseFloat(lengthInput.value);
                const type = typeSelect.value;

                if (!isNaN(location) && !isNaN(length) && type) {
                    updatedLayout.push({
                        location: location,
                        length: length,
                        reinforcement_type: type
                    });
                }
            }
        });

        // Update the global reinforcement layout data
        reinforcementLayoutData = updatedLayout;
        console.log("Updated reinforcement layout data:", reinforcementLayoutData);

        // Redraw with updated data
        window.redrawCanvas(reinforcementLayoutData);
    }

    // Make the update function available globally for jQuery integration
    window.updateReinforcementLayoutFromForm = updateReinforcementLayoutFromForm;

    // Initial draw
    redrawCanvas();

    // Set up screenshot button - only for reinforcement layout page
    const screenshotButton = document.getElementById('screenshot-button');
    const layoutTable = document.getElementById('reinforcementLayoutTable');
    if (screenshotButton && layoutTable) {
        // Clear ALL existing event listeners by cloning the button
        const newScreenshotButton = screenshotButton.cloneNode(true);
        screenshotButton.parentNode.replaceChild(newScreenshotButton, screenshotButton);

        // Add only our handler
        newScreenshotButton.addEventListener('click', handleLayoutScreenshot);
        console.log("Attached layout screenshot button listener (cleaned)");
    } else {
        console.log("Screenshot button setup failed:", {
            screenshotButton: !!screenshotButton,
            layoutTable: !!layoutTable
        });
    }

    function handleLayoutScreenshot() {
        console.log("Layout screenshot button clicked");

        // Only handle if we're on reinforcement layout page
        const layoutTable = document.getElementById('reinforcementLayoutTable');
        if (!layoutTable) {
            console.log("Layout table not found - not on reinforcement layout page");
            return;
        }
        const canvas = document.getElementById('geometry2-canvas');
        if (!canvas) {
            console.error("Canvas not found!");
            if (typeof showErrorPopup === "function") {
                showErrorPopup("Canvas not found!");
            } else {
                alert("Canvas not found!");
            }
            return;
        }

        console.log("Taking screenshot of reinforcement layout canvas");
        const dataURL = canvas.toDataURL('image/png');

        // Store screenshot on server for report generation
        storeScreenshotOnServer(dataURL);

        // Download the image (same as geometry and external loads)
        const link = document.createElement('a');
        link.href = dataURL;
        link.download = 'grs_wall_reinforcement_layout.png';
        link.click();
        console.log("Reinforcement layout screenshot downloaded");
    }

    // Function to store screenshot on server for report generation
    function storeScreenshotOnServer(dataURL) {
        fetch('/store-screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                screenshot: dataURL
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Screenshot stored successfully for report generation:', data.screenshot_id);
            } else {
                console.error('Failed to store screenshot:', data.message);
            }
        })
        .catch(error => {
            console.error('Error storing screenshot:', error);
        });
    }

    // Function to automatically capture and store screenshot for report
    function captureLayoutScreenshotForReport() {
        const canvas = document.getElementById('geometry2-canvas');
        if (canvas) {
            console.log("Auto-capturing layout screenshot for report");
            const dataURL = canvas.toDataURL('image/png');
            storeScreenshotOnServer(dataURL);
        }
    }

    // Make the auto-capture function available globally
    window.captureLayoutScreenshotForReport = captureLayoutScreenshotForReport;

    // Auto-capture screenshot when visualization is updated (for report generation)
    function autoCapture() {
        // Wait a bit for the canvas to be fully rendered
        setTimeout(() => {
            const canvas = document.getElementById('geometry2-canvas');
            if (canvas) {
                console.log("Auto-capturing screenshot for report generation");
                captureLayoutScreenshotForReport();
            }
        }, 1000);
    }

    // Hook into redrawCanvas to auto-capture screenshots
    const originalRedrawCanvas = window.redrawCanvas;
    if (originalRedrawCanvas) {
        window.redrawCanvas = function() {
            originalRedrawCanvas.apply(this, arguments);
            autoCapture();
        };
    }
};

// Initialize on DOM ready and make available for AJAX reloads
document.addEventListener('DOMContentLoaded', function() {
    console.log("🎨 DOM ready - calling initializeLayoutVisualization");
    window.initializeLayoutVisualization();
});

// Also make it available immediately for synchronous calls
console.log("🎨 LayoutVisualization.js loaded - function available globally");

