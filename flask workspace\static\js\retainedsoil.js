document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById("retainedsoil-form");

    // Load saved data (if available)
    if (localStorage.getItem("retainedsoil_density")) {
        document.getElementById("retainedsoil_density").value = localStorage.getItem("retainedsoil_density");
    }
    if (localStorage.getItem("retainedfriction_angle")) {
        document.getElementById("retainedfriction_angle").value = localStorage.getItem("retainedfriction_angle");
    }
    if (localStorage.getItem("retainedcohesion")) {
        document.getElementById("retainedcohesion").value = localStorage.getItem("retainedcohesion");
    }

    // Save data when input changes
    form.addEventListener("input", function() {
        localStorage.setItem("retainedsoil_density", document.getElementById("retainedsoil_density").value);
        localStorage.setItem("retainedfriction_angle", document.getElementById("retainedfriction_angle").value);
        localStorage.setItem("retainedcohesion", document.getElementById("retainedcohesion").value);
    });

    // Save form data asynchronously with AJAX
    form.addEventListener("submit", function(e) {
        e.preventDefault();  // Prevent normal form submission

        const formData = new FormData(form);

        // Send the data to the server using AJAX
        fetch('/retainedsoil', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);  // Show a success message
            // Optionally, you can clear localStorage after successful save
            // localStorage.removeItem("retainedsoil_density");
            // localStorage.removeItem("retainedfriction_angle");
            // localStorage.removeItem("retainedcohesion");
        })
        .catch(error => {
            console.error('Error:', error);
            alert('There was an error with the submission.');
        });
    });
});