from PySide6.QtWidgets import (QFileDialog,QMessageBox)

def save_data(self=None):
    options = QFileDialog.Options()
    file_name, _ = QFileDialog.getSaveFileName(self, "Save File", "", "All Files (*);;Text Files (*.txt)", options=options)
    
    if file_name:
        try:
            with open(file_name, 'w') as file:
                # Basic Project Info
                file.write(f"Project Name: {self.projectname_input.text()}\n")
                file.write(f"Project ID: {self.projectid_input.text()}\n")
                file.write(f"Designer: {self.designer_input.text()}\n")
                file.write(f"Client: {self.client_input.text()}\n")
                file.write(f"Description: {self.description_input.text()}\n")
                file.write(f"Date: {self.date_input.text()}\n")
                file.write(f"Revision: {self.revision_input.text()}\n\n")
                
                # Wall Geometry Details
                file.write("Wall Geometry Details:\n")
                file.write(f"  Wall Height: {self.wall_height_input.text()}\n")
                file.write(f"  Embedment Depth: {self.embedment_depth_input.text()}\n")
                file.write(f"  Wall Length: {self.wall_length_input.text()}\n")
                file.write(f"  Wall Batter: {self.wall_batter_input.text()}\n")
                file.write(f"  Wall Back Slope Angle: {self.wall_backslopeangle_input.text()}\n")
                file.write(f"  Wall Back Slope Rise: {self.wall_backsloperise_input.text()}\n\n")
                
                # Soil Properties
                file.write("Soil Properties:\n")
                file.write("  Reinforced Soil:\n")
                file.write(f"    Density: {self.reinforced_soil_density_input.text()}\n")
                file.write(f"    Angle: {self.reinforced_angle_input.text()}\n")
                file.write(f"    Cohesion: {self.reinforced_cohesion_input.text()}\n")
                
                file.write("  Retained Soil:\n")
                file.write(f"    Density: {self.retained_soil_density_input.text()}\n")
                file.write(f"    Angle: {self.retained_angle_input.text()}\n")
                file.write(f"    Cohesion: {self.retained_cohesion_input.text()}\n")
                
                file.write("  Foundation Soil:\n")
                file.write(f"    Density: {self.foundation_soil_density_input.text()}\n")
                file.write(f"    Angle: {self.foundation_angle_input.text()}\n")
                file.write(f"    Cohesion: {self.foundation_cohesion_input.text()}\n")
                file.write(f"    Eccentricity: {self.foundation_eccentricity_input.text()}\n")
                file.write(f"    Eccentricity (Seismic): {self.foundation_eccentricityseismic_input.text()}\n")
                file.write(f"    Water Table Depth: {self.watertabledepth_input.text()}\n\n")
                
                # Load Details
                file.write("Loads:\n")
                file.write(f"  Dead Load 1: {self.dead_load1_input.text()}\n")
                file.write(f"  Dead Load 2: {self.dead_load2_input.text()}\n")
                file.write(f"  Dead Load 3: {self.dead_load3_input.text()}\n")
                file.write(f"  Live Load 1: {self.live_load1_input.text()}\n")
                file.write(f"  Live Load 2: {self.live_load2_input.text()}\n")
                file.write(f"  Live Load 3: {self.live_load3_input.text()}\n")
                
                file.write("  Strip Loads:\n")
                for i in range(1, 4):
                    file.write(f"    Vertical Strip Load {i}: {getattr(self, f'Vertical_Strip_Load{i}_input').text()}\n")
                    file.write(f"    Horizontal Strip Load {i}: {getattr(self, f'Horizontal_Strip_Load{i}_input').text()}\n")
                    file.write(f"    Width {i}: {getattr(self, f'Strip_Load{i}_width_input').text()}\n")
                    file.write(f"    Distance {i}: {getattr(self, f'Strip_Load{i}_distance_input').text()}\n")
                
                file.write(f"  Seismic horizontal acceleration: {self.earthquake_load_input.text()}\n\n")
                                    # Check if the "Use Direct kh" checkbox is checked
                if self.use_direct_kh_checkbox.isChecked():
                    # Write the seismic earth pressure load only if the checkbox is checked
                    kh_value = self.kh_input.text()
                    file.write(f"  Seismic earth pressure load (GLE method): {kh_value}\n\n")
                else:  # If no value exists, uncheck the checkbox and set the field to two empty lines
                    file.write(f"  Seismic earth pressure load (GLE method): None\n\n")

                file.write(f"  ruptureimpact_load upper layer: {self.impact_load1_input.text()}\n")
                file.write(f"  ruptureimpact_load second layer: {self.impact_load2_input.text()}\n")
                file.write(f"  pulloutimpact_load upper layer: {self.impact_load3_input.text()}\n")
                file.write(f"  pulloutimpact_load second layer: {self.impact_load4_input.text()}\n")

                
                
                # Reinforcement Properties Table
                file.write("Reinforcement Properties:\n")
                for row in range(self.reinforcement_properties_table.rowCount()):
                    data = [self.reinforcement_properties_table.item(row, col).text() if self.reinforcement_properties_table.item(row, col) else ""
                            for col in range(self.reinforcement_properties_table.columnCount())]
                    file.write("  " + ", ".join(data) + "\n")
                

                
                # Reinforcement Table
                file.write("Reinforcement Layers:\n")
                for row in range(self.reinforcement_table.rowCount()):
                    data = []

                    for col in range(self.reinforcement_table.columnCount()):
                        if col == 3:  # 4th column (index 3) has the ComboBox
                            reinforcement_type_item = self.reinforcement_table.cellWidget(row, col)
                            reinforcement_type = reinforcement_type_item.currentText() if reinforcement_type_item else ""
                            data.append(reinforcement_type)  # Append ComboBox value
                        else:
                            item = self.reinforcement_table.item(row, col)
                            data.append(item.text() if item else "")

                    file.write("  " + ", ".join(data) + "\n")


                QMessageBox.information(self, "Save File", "Data saved successfully!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save file: {e}")