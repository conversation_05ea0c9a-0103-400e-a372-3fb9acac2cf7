{% extends "base.html" %}

{% block content %}
<form id="externalloadsform" method="POST">
    <div class="container">
        <h3>External Loads Input</h3>

        <div class="button-group" style="display: flex; gap: 10px; justify-content: center; margin-bottom: 20px;">
            <button type="button" class="tab-button" onclick="showSection('dead_loads')">Dead Loads</button>
            <button type="button" class="tab-button" onclick="showSection('live_loads')">Live Loads</button>
            <button type="button" class="tab-button" onclick="showSection('strip_loads')">Strip Loads</button>
            <button type="button" class="tab-button" onclick="showSection('earthquake_loads')">Earthquake Loads</button>
            <button type="button" class="tab-button" onclick="showSection('impact_loads')">Impact Loads</button>
        </div>

        <div class="load-sections">
            <div id="dead_loads" class="load-input-section">
                <h3>Dead Loads</h3>
                {% for i in range(1, 4) %}
                <div class="input-group">
                    <label for="dead_load{{ i }}">Dead Load {{ i }} (kN/m²):</label>
                    <input type="number" id="dead_load{{ i }}" name="dead_load{{ i }}" value="{{ dead_loads[i-1] }}" step="0.01">
                </div>
                {% endfor %}
            </div>

            <div id="live_loads" class="load-input-section">
                <h3>Live Loads</h3>
                {% for i in range(1, 4) %}
                <div class="input-group">
                    <label for="live_load{{ i }}">Live Load {{ i }} (kN/m²):</label>
                    <input type="number" id="live_load{{ i }}" name="live_load{{ i }}" value="{{ live_loads[i-1] }}" step="0.01">
                </div>
                {% endfor %}
            </div>

            <div id="strip_loads" class="load-input-section">
                <h3>Strip Loads</h3>
                <div class="strip-load-container">
                    {% for i in range(1, 4) %}
                    <div class="strip-load-box">
                        <h4>Strip Load {{ i }}</h4>
                        <div class="input-group">
                            <label for="vertical_strip_load{{ i }}">Vertical Load (kN/m):</label>
                            <input type="number" id="vertical_strip_load{{ i }}" name="vertical_strip_load{{ i }}" value="{{ vertical_strip_load[i-1] }}" step="0.01">
                        </div>
                        <div class="input-group">
                            <label for="horizontal_strip_load{{ i }}">Horizontal Load (kN/m):</label>
                            <input type="number" id="horizontal_strip_load{{ i }}" name="horizontal_strip_load{{ i }}" value="{{ horizontal_strip_load[i-1] }}" step="0.01">
                        </div>
                        <div class="input-group">
                            <label for="strip_load_width{{ i }}">Width (m):</label>
                            <input type="number" id="strip_load_width{{ i }}" name="strip_load_width{{ i }}" value="{{ strip_load_width[i-1] }}" step="0.01">
                        </div>
                        <div class="input-group">
                            <label for="strip_load_distance{{ i }}">Distance from Wall (m):</label>
                            <input type="number" id="strip_load_distance{{ i }}" name="strip_load_distance{{ i }}" value="{{ strip_load_distance[i-1] }}" step="0.01">
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div id="earthquake_loads" class="load-input-section">
                <h3>Earthquake Loads</h3>
                <div class="input-group">
                    <label for="earthquake_acceleration">Acceleration (g):</label>
                    <input type="number" id="earthquake_acceleration" name="earthquake_acceleration" value="{{ earthquake_acceleration }}" step="0.01">
                </div>
                <div class="input-group">
                    <input type="checkbox" id="use_direct_kh" name="use_direct_kh" onclick="toggleKhInput()">
                    <label for="use_direct_kh">Directly Input Seismic Force for External Stability</label>
                </div>
                <div class="input-group">
                    <label for="seismic_force">Seismic Horizontal Earth Pressure Force (P):</label>
                    <input type="number" id="seismic_force" name="seismic_force" value="{{ seismic_force }}" {% if not seismic_force %}disabled{% endif %} step="0.01">
                </div>
            </div>

            <div id="impact_loads" class="load-input-section">
                <h3>Impact Loads</h3>
                <div id="impact_note" class="impact-note">
                    Upper layer rupture impact load = 33.5 kN/m.<br>
                    Second layer rupture impact load = 8.8 kN/m.<br>
                    Upper layer pullout impact load = 19 kN/m - resisted over full length<br>
                    Second layer pullout impact load = 8.8 kN/m - resisted over full length.
                </div>

                {% for type in ["rupture", "pullout"] %}
                {% for layer in ["upper", "second"] %}
                <div class="input-group">
                    <label for="{{ type }}_impact_{{ layer }}">{{ type.capitalize() }} Impact Load - {{ layer.capitalize() }} Layer (kN/m):</label>
                    <input type="number" id="{{ type }}_impact_{{ layer }}" name="{{ type }}_impact_{{ layer }}" value="{{ impact_loads[type][layer] }}" step="0.01">
                </div>
                {% endfor %}
                {% endfor %}
            </div>
        </div>
        <button type="submit" id="save-button">Save External Loads</button>
    </div>
</form>

<script>
    function showSection(sectionId) {
        document.querySelectorAll('.load-input-section').forEach(section => {
            section.style.display = 'none';
        });
        document.getElementById(sectionId).style.display = 'block';
    }

    function toggleKhInput() {
        const khInput = document.getElementById('seismic_force');
        const checkbox = document.getElementById('use_direct_kh');
        khInput.disabled = !checkbox.checked;
    }

    document.addEventListener('DOMContentLoaded', function () {
        const form = document.getElementById('externalloadsform');

        form.addEventListener('submit', function (event) {
            event.preventDefault();
            const formData = new FormData(form);
            fetch('/externalloads', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message || 'External loads data saved successfully!');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving external loads data.');
            });
        });
    });
</script>

<style>
    .tab-button {
        padding: 10px 15px;
        background-color: #007bff;
        color: white;
        border: none;
        cursor: pointer;
        border-radius: 5px;
    }

    .tab-button:hover {
        background-color: #0056b3;
    }

    .load-input-section {
        display: none;
        margin-top: 20px;
    }

    .input-group {
        margin-bottom: 15px;
    }

    .strip-load-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }

    .strip-load-box {
        flex: 1;
        min-width: 200px;
        border: 1px solid #ddd;
        padding: 15px;
        border-radius: 5px;
    }
</style>
{% endblock %}
