/**
 * Main Application JavaScript - Flask Workspace 2
 * Handles global functionality, navigation, and AJAX utilities
 */

// Global application namespace to prevent conflicts
window.GRSApp = window.GRSApp || {};

document.addEventListener('DOMContentLoaded', function () {
    // Initialize core application functionality
    GRSApp.init();
});

// Core application object
GRSApp = {
    // Configuration
    config: {
        ajaxTimeout: 10000,
        flashMessageTimeout: 5000,
        maxFlashMessages: 3
    },

    // Initialize the application
    init: function() {
        this.initializeFlashMessages();
        this.initializeGlobalEventHandlers();
        console.log('GRS Application initialized');
    },

    // Centralized AJAX utility
    ajax: function(options) {
        const defaults = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: this.config.ajaxTimeout
        };

        const config = Object.assign({}, defaults, options);

        // Handle FormData separately
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type']; // Let browser set it
        } else if (typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        return fetch(config.url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                throw error;
            });
    },

    // Flash message handling
    initializeFlashMessages: function() {
        const alerts = document.querySelectorAll('.content-area > .alert');

        // Remove any close buttons that might have been added by other scripts
        alerts.forEach((alert) => {
            const closeButton = alert.querySelector('.close, .btn-close');
            if (closeButton) {
                closeButton.remove();
            }
            // Remove dismissible classes if present
            alert.classList.remove('alert-dismissible');
        });

        // Limit visible flash messages
        alerts.forEach((alert, index) => {
            if (index >= this.config.maxFlashMessages) {
                alert.style.display = 'none';
            }
        });

        // Auto-dismiss flash messages
        alerts.forEach((alert, index) => {
            if (index < this.config.maxFlashMessages) {
                setTimeout(() => {
                    this.dismissAlert(alert);
                }, this.config.flashMessageTimeout + (index * 500));
            }
        });
    },

    dismissAlert: function(alert) {
        if (alert && alert.style.display !== 'none') {
            alert.style.animation = 'flashSlideOut 0.3s ease-in forwards';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }
    },

    // Global event handlers
    initializeGlobalEventHandlers: function() {
        // Handle any global click events that need special processing
        document.addEventListener('click', this.handleGlobalClick.bind(this));
    },

    handleGlobalClick: function(event) {
        // Handle any global click logic here
        // This can be extended for specific needs
    },

    // Form handling utilities - REMOVED: Now handled by formHandler.js
    // This section was consolidated into the centralized formHandler.js system

    // Utility functions
    utils: {
        // Save sidebar scroll position
        saveSidebarScrollPosition: function() {
            const sidebarMenu = document.querySelector('.sidebar-menu');
            const sidebar = document.getElementById('sidebar');
            const scrollableElement = sidebarMenu || sidebar;

            if (scrollableElement) {
                localStorage.setItem('sidebarScrollPosition', scrollableElement.scrollTop);
                console.log('Saved sidebar scroll position:', scrollableElement.scrollTop);
            }
        },

        // Restore sidebar scroll position
        restoreSidebarScrollPosition: function() {
            const sidebarMenu = document.querySelector('.sidebar-menu');
            const sidebar = document.getElementById('sidebar');
            const scrollableElement = sidebarMenu || sidebar;
            const savedPosition = localStorage.getItem('sidebarScrollPosition');

            if (scrollableElement && savedPosition) {
                scrollableElement.scrollTop = parseInt(savedPosition, 10);
                console.log('Restored sidebar scroll position:', savedPosition);
            }
        }
    }
};

// Make utilities available globally for backward compatibility
window.saveSidebarScrollPosition = GRSApp.utils.saveSidebarScrollPosition;
window.restoreSidebarScrollPosition = GRSApp.utils.restoreSidebarScrollPosition;


