{% extends "base.html" %} {% block content %}
<div class="container mt-4">
  <h2>Active User Sessions</h2>

  <!-- Alert container for success/error messages -->
  <div id="alert-container"></div>

  {% if active_sessions %}
  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead class="thead-dark">
        <tr>
          <th>Username</th>
          <th>Login Time</th>
          <th>Last Activity</th>
          <th>IP Address</th>
          <th>User Agent</th>
          <th class="text-center">Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for session_data in active_sessions %}
        <tr id="session-{{ session_data[0] }}">
          <td>{{ session_data[1] }}</td>
          <td>
            {{ session_data[2].strftime('%Y-%m-%d %H:%M:%S') if session_data[2]
            else 'N/A' }}
          </td>
          <td>
            {{ session_data[3].strftime('%Y-%m-%d %H:%M:%S') if session_data[3]
            else 'N/A' }}
          </td>
          <td>{{ session_data[4] or 'Unknown' }}</td>
          <td>
            <span
              class="text-truncate d-inline-block"
              style="max-width: 200px"
              title="{{ session_data[5] or 'Unknown' }}"
            >
              {{ session_data[5] or 'Unknown' }}
            </span>
          </td>
          <td class="text-center">
            <button
              class="btn btn-sm btn-danger force-logout-btn"
              onclick="forceLogout({{ session_data[0] }})"
            >
              Force Logout
            </button>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% else %}
  <div class="alert alert-info">
    <i class="fas fa-info-circle"></i> No active sessions found.
  </div>
  {% endif %}

  <div class="button-container mt-4">
    <a
      href="{{ url_for('admin_dashboard') }}"
      class="btn btn-secondary action-btn"
    >
      <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
  </div>
</div>

<script>
  function showAlert(message, type = "success") {
    // Clear previous alerts
    const alertContainer = document.getElementById("alert-container");
    alertContainer.innerHTML = "";

    // Create new alert without close button
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${type} mt-2`;
    alertDiv.innerHTML = `
      <i class="fas fa-${
        type === "success" ? "check-circle" : "exclamation-triangle"
      }"></i> ${message}
    `;

    alertContainer.appendChild(alertDiv);

    // Auto-hide after 4 seconds
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 4000);
  }

  function forceLogout(sessionId) {
    if (confirm("Are you sure you want to force logout this user?")) {
      // Show loading state
      const button = event.target;
      const originalText = button.innerHTML;
      button.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Terminating...';
      button.disabled = true;

      fetch(`/admin/force_logout/${sessionId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "X-Requested-With": "XMLHttpRequest",
        },
        credentials: "same-origin",
        body: "", // Empty body for POST request
      })
        .then((response) => {
          console.log("Response status:", response.status);
          console.log("Response headers:", response.headers);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          return response.json();
        })
        .then((data) => {
          console.log("Response data:", data);

          if (data.status === "success") {
            // Remove the row from the table
            const rowElement = document.getElementById(`session-${sessionId}`);
            if (rowElement) {
              rowElement.remove();
            }

            // Show success message using the helper function
            showAlert(data.message, "success");
          } else {
            // Show error message using the helper function
            showAlert("Error: " + data.message, "danger");
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
          }
        })
        .catch((error) => {
          console.error("Fetch error:", error);
          showAlert(
            "An error occurred while forcing logout: " + error.message,
            "danger"
          );
          // Restore button state
          button.innerHTML = originalText;
          button.disabled = false;
        });
    }
  }
</script>

<style>
  .button-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 30px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
  }

  .action-btn {
    min-width: 160px !important;
    height: 44px !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    font-size: 14px !important;
  }

  .action-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    text-decoration: none !important;
  }

  .action-btn i {
    font-size: 14px;
  }

  /* Ensure consistent button styling - Override global .btn styles */
  .btn-secondary.action-btn {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
    border: 1px solid #6c757d !important;
  }

  .btn-secondary.action-btn:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    color: #fff !important;
    text-decoration: none !important;
  }

  /* Force Logout button styling */
  .force-logout-btn {
    min-width: 100px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  .force-logout-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
  }

  /* Table alignment improvements */
  .table th:last-child,
  .table td:last-child {
    text-align: center;
    vertical-align: middle;
  }

  .table th {
    vertical-align: middle;
    border-bottom: 2px solid #dee2e6;
  }

  .table td {
    vertical-align: middle;
  }

  @media (max-width: 768px) {
    .button-container {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .action-btn {
      width: 100%;
      min-width: auto;
    }
  }
</style>
{% endblock %}
