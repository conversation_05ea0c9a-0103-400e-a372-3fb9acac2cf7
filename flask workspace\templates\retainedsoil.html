{% extends "base.html" %} {% block content %}
<div id="retainedsoil-form">
  <h1>Retained Soil Inputs</h1>
  <form id="retainedsoil-form">
    <div class="form-group">
      <label for="retainedsoil_density">Retained Soil Density (kN/m³):</label>
      <input
        type="number"
        id="retainedsoil_density"
        name="retainedsoil_density"
        step="0.1"
        required
        value="{{ retainedsoil_density }}"
      />
    </div>

    <div class="form-group">
      <label for="retainedfriction_angle">Friction Angle (°):</label>
      <input
        type="number"
        id="retainedfriction_angle"
        name="retainedfriction_angle"
        step="0.1"
        required
        value="{{ retainedfriction_angle }}"
      />
    </div>

    <div class="form-group">
      <label for="retainedcohesion">Cohesion (kN/m²):</label>
      <input
        type="number"
        id="retainedcohesion"
        name="retainedcohesion"
        step="0.1"
        required
        value="{{ retainedcohesion }}"
      />
    </div>

    <button type="submit">Save Retained Soil Inputs</button>
  </form>
</div>
<script src="{{ url_for('static', filename='js/retainedsoil.js') }}"></script>
{% endblock %}
