<!-- internal_stability_results.html -->
{% extends "base.html" %}
{% block content %}
<h2>Internal Stability Analysis Results</h2>

<!-- Table 1: Static Results -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h3>Static Results</h3>
        <button class="btn btn-sm btn-outline-secondary" onclick="copyTable('staticTable')">Copy</button>
    </div>
    <div class="card-body">
        <table class="table table-striped" id="staticTable">
            <thead>
                <tr>
                    <th>Layer</th>
                    <th>Location from Bottom</th>
                    <th>CDR Internal Sliding</th>
                    <th>CDR Rupture</th>
                    <th>CDR Pullout</th>
                    <th>Tensile Load</th>
                    <th>IRh Critical</th>
                    <th>IRv Critical</th>
                    <th>Pullout Capacity</th>
                    <th>CDR Rupture Impact</th>
                    <th>CDR Pullout Impact</th>
                    <th>CDR Connection</th>
                </tr>
            </thead>
            <tbody>
                {% for result in results['Reinforcement Results'] %}
                <tr>
                    <td>Layer {{ loop.index }}</td>
                    <td>{{ result['Depth from Bottom'] | round(2) }}</td>
                    <td class="{{ 'text-success' if result['CDR Internal Sliding Critical'] > 1 else 'text-danger' }}">
                        {{ result['CDR Internal Sliding Critical'] | round(2) }}
                    </td>
                    <td class="{{ 'text-success' if result['CDR Rupture Critical'] > 1 else 'text-danger' }}">
                        {{ result['CDR Rupture Critical'] | round(2) }}
                    </td>
                    <td class="{{ 'text-success' if result['CDR Pullout Critical'] > 1 else 'text-danger' }}">
                        {{ result['CDR Pullout Critical'] | round(2) }}
                    </td>
                    <td>{{ result['Tensile Load Critical'] | round(2) }}</td>
                    <td>{{ result['IRh Critical'] | round(2) }}</td>
                    <td>{{ result['IRv Critical'] | round(2) }}</td>
                    <td>{{ result['Pullout Capacity Critical'] | round(2) }}</td>
                    <td class="{{ 'text-success' if result['CDR Rupture Impact'] > 1 else 'text-danger' }}">
                        {{ result['CDR Rupture Impact'] | round(2) }}
                    </td>
                    <td class="{{ 'text-success' if result['CDR Pullout Impact'] > 1 else 'text-danger' }}">
                        {{ result['CDR Pullout Impact'] | round(2) }}
                    </td>
                    <td class="{{ 'text-success' if result['CDR Connection Critical'] > 1 else 'text-danger' }}">
                        {{ result['CDR Connection Critical'] | round(2) }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Table 2: Earthquake Results -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h3>Earthquake Results</h3>
        <button class="btn btn-sm btn-outline-secondary" onclick="copyTable('earthquakeTable')">Copy</button>
    </div>
    <div class="card-body">
        <table class="table table-striped" id="earthquakeTable">
            <thead>
                <tr>
                    <th>Layer</th>
                    <th>Location from Bottom</th>
                    <th>CDR Internal Sliding</th>
                    <th>Tensile Load</th>
                    <th>Dynamic Tensile Load</th>
                    <th>Static Tensile Load</th>
                    <th>Static Required Tensile Load</th>
                    <th>Dynamic Required Tensile Load</th>
                    <th>Pullout Capacity</th>
                    <th>CDR Rupture</th>
                    <th>CDR Pullout</th>
                    <th>CDR Connection</th>
                </tr>
            </thead>
            <tbody>
                {% for result in results['EQ Reinforcement Results'] %}
                <tr>
                    <td>Layer {{ loop.index }}</td>
                    <td>{{ result['Depth from Bottom'] | round(2) }}</td>
                    <td class="{{ 'text-success' if result['CDR Internal Sliding'] > 1 else 'text-danger' }}">
                        {{ result['CDR Internal Sliding'] | round(2) }}
                    </td>
                    <td>{{ result['Tensile Load'] | round(2) }}</td>
                    <td>{{ result['Dynamic Tensile Load'] | round(2) }}</td>
                    <td>{{ result['Static Tensile Load'] | round(2) }}</td>
                    <td>{{ result['Static Required Tensile Load'] | round(2) }}</td>
                    <td>{{ result['Dynamic Required Tensile Load'] | round(2) }}</td>
                    <td>{{ result['Pullout Capacity'] | round(2) }}</td>
                    <td class="{{ 'text-success' if result['CDR Rupture'] > 1 else 'text-danger' }}">
                        {{ result['CDR Rupture'] | round(2) }}
                    </td>
                    <td class="{{ 'text-success' if result['CDR Pullout'] > 1 else 'text-danger' }}">
                        {{ result['CDR Pullout'] | round(2) }}
                    </td>
                    <td class="{{ 'text-success' if result['CDR Connection'] > 1 else 'text-danger' }}">
                        {{ result['CDR Connection'] | round(2) }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- CDR Explanation -->
<div class="alert alert-info">
    <strong>Note:</strong> CDR (Capacity Demand Ratio) is defined as the factored resistance divided by the factored force. In limit states, it should be more than one to ensure safety.
</div>

<!-- Back to Home Button -->
<div class="mt-4">
    <a href="{{ url_for('home') }}" class="btn btn-secondary" id="back-to-home">Back to Home</a>
</div>

<script>
function copyTable(tableId) {
    const table = document.getElementById(tableId);
    const range = document.createRange();
    range.selectNode(table);
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand('copy');
    window.getSelection().removeAllRanges();
    alert('Table copied to clipboard!');
}

document.getElementById('back-to-home').addEventListener('click', function(e) {
    e.preventDefault();
    window.location.href = "{{ url_for('home') }}";
    localStorage.setItem('analysisRun', 'true');
});
</script>
{% endblock %}
