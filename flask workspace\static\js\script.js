document.addEventListener('DOMContentLoaded', function () {
    const forms = {
        'project-info-form': '/project_info',
        'geometry-form': '/geometry',
        'reinforcedsoil-form': '/reinforcedsoil',
        'retainedsoil-form': '/retainedsoil',
        'foundationsoil-form': '/foundationsoil',
        'externalloadsform': '/externalloads',
        'reinforcementproperties-form': '/reinforcementproperties'
    };

    const backButtons = {
        'back-button': '/',
        'reinforcedsoil-back-btn': '/geometry',
        'retainedsoil-back-btn': '/reinforcedsoil',
        'foundationsoil-back-btn': '/retainedsoil',
        'external-loads-back-btn': '/foundationsoil',
        'reinforcementproperties-back-btn': '/externalloads'
    };

    function handleFormSubmit(formId, url, visualizationFunction) {
        const form = document.getElementById(formId);
        if (!form) return;

        form.addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(form);

            fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) throw new Error(`Failed to save data for ${formId}`);
                return response.json();
            })
            .then(data => {
                alert(data.message || 'Data saved successfully!');
                if (visualizationFunction) visualizationFunction(formData);
            })
            .catch(error => console.error(`Error in ${formId}:`, error));
        });
    }

    for (const [formId, url] of Object.entries(forms)) {
        handleFormSubmit(formId, url, window[`update${formId.replace(/-form/, '').replace(/(^\w)/, (m) => m.toUpperCase())}Visualization`]);
    }

    for (const [btnId, url] of Object.entries(backButtons)) {
        const button = document.getElementById(btnId);
        if (button) {
            button.addEventListener('click', function () {
                window.location.href = url;
            });
        }
    }
});

function updateReinforcementpropertiesVisualization(formData) {
    console.log('Updating reinforcement properties visualization:', Object.fromEntries(formData));
}
