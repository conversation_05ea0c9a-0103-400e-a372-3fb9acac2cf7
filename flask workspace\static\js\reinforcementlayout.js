document.addEventListener("DOMContentLoaded", function () {
    loadTableData();
  });

  function addRow(data = {}) {
    let table = document
      .getElementById("reinforcementLayoutTable")
      .getElementsByTagName("tbody")[0];
    let rowCount = table.rows.length;
    let newRow = table.insertRow(rowCount);

    // Location from bottom
    let locationCell = newRow.insertCell(0);
    let locationInput = document.createElement("input");
    locationInput.type = "number";
    locationInput.className = "form-control";
    locationInput.value = data.location || "";
    locationCell.appendChild(locationInput);

    // Length
    let lengthCell = newRow.insertCell(1);
    let lengthInput = document.createElement("input");
    lengthInput.type = "number";
    lengthInput.className = "form-control";
    lengthInput.value = data.length || "";
    lengthCell.appendChild(lengthInput);

    // Coverage Ratio
    let coverageCell = newRow.insertCell(2);
    let coverageInput = document.createElement("input");
    coverageInput.type = "number";
    coverageInput.className = "form-control";
    coverageInput.value = data.coverage || "";
    coverageCell.appendChild(coverageInput);

    // Reinforcement Type (Dropdown)
    let typeCell = newRow.insertCell(3);
    let typeSelect = document.createElement("select");
    typeSelect.className = "form-control";
    // Options will be added here dynamically from reinforcementData
    populateReinforcementTypes(typeSelect, data.reinforcementType);
    typeCell.appendChild(typeSelect);

    // Connection Strength RF (Long-Term)
    let longTermCell = newRow.insertCell(4);
    let longTermInput = document.createElement("input");
    longTermInput.type = "number";
    longTermInput.className = "form-control";
    longTermInput.value = data.longTerm || "";
    longTermCell.appendChild(longTermInput);

    // Connection Strength RF (Bio-Chem)
    let bioChemCell = newRow.insertCell(5);
    let bioChemInput = document.createElement("input");
    bioChemInput.type = "number";
    bioChemInput.className = "form-control";
    bioChemInput.value = data.bioChem || "";
    bioChemCell.appendChild(bioChemInput);

    // Remove Button
    let removeCell = newRow.insertCell(6);
    let removeButton = document.createElement("button");
    removeButton.innerHTML = "X";
    removeButton.className = "btn-remove";
    removeButton.onclick = function () {
      removeRow(this);
    };
    removeCell.appendChild(removeButton);
  }

  function removeRow(button) {
    let row = button.parentNode.parentNode;
    let table = row.parentNode;
    if (table.rows.length > 2) {
      row.remove();
    } else {
      document.getElementById("message").innerText =
        "At least two reinforcement types must be present.";
    }
  }

  function populateReinforcementTypes(selectElement, selectedValue) {
    // Retrieve reinforcement data from session storage
    let reinforcementData = JSON.parse(
      sessionStorage.getItem("reinforcementData") || "[]"
    );

    // Add options to the select element
    reinforcementData.forEach(function (item) {
      let option = document.createElement("option");
      option.value = item.name;
      option.text = item.name;
      if (selectedValue === item.name) {
        option.selected = true;
      }
      selectElement.appendChild(option);
    });
  }

  function saveData() {
    let table = document
      .getElementById("reinforcementLayoutTable")
      .getElementsByTagName("tbody")[0];
    let rows = table.rows;
    let data = [];

    for (let row of rows) {
      let location = row.cells[0].querySelector("input").value;
      let length = row.cells[1].querySelector("input").value;
      let coverage = row.cells[2].querySelector("input").value;
      let reinforcementType = row.cells[3].querySelector("select").value;
      let longTerm = row.cells[4].querySelector("input").value;
      let bioChem = row.cells[5].querySelector("input").value;

      data.push({
        location: location,
        length: length,
        coverage: coverage,
        reinforcementType: reinforcementType,
        longTerm: longTerm,
        bioChem: bioChem,
      });
    }
    // Send data to server using fetch or store in session storage
    fetch("/reinforcementlayout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
      .then((response) => response.json())
      .then((data) => {
        document.getElementById("message").innerText = data.message;
      })
      .catch((error) => {
        console.error("Error:", error);
        document.getElementById("message").innerText = "Error saving data.";
      });
  }

  function loadTableData() {
    // Retrieve stored data (e.g., from localStorage or server)
    fetch("/reinforcementlayout/data") // new route to fetch existing data
      .then((response) => response.json())
      .then((data) => {
        if (data && data.length > 0) {
          data.forEach((rowData) => addRow(rowData));
        } else {
          // Load default two rows if no data exists
          addRow();
          addRow();
        }
      })
      .catch((error) => {
        console.error("Error loading data:", error);
        addRow();
        addRow();
      });
  }