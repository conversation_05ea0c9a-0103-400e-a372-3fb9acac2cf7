document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById("geometry-form");

    // Load saved data (if available)
    if (localStorage.getItem("wall_height")) {
        document.getElementById("wall_height").value = localStorage.getItem("wall_height");
    }
    if (localStorage.getItem("embedment_depth")) {
        document.getElementById("embedment_depth").value = localStorage.getItem("embedment_depth");
    }
    if (localStorage.getItem("wall_length")) {
        document.getElementById("wall_length").value = localStorage.getItem("wall_length");
    }
    if (localStorage.getItem("wall_batter")) {
        document.getElementById("wall_batter").value = localStorage.getItem("wall_batter");
    }
    if (localStorage.getItem("backslope_angle")) {
        document.getElementById("backslope_angle").value = localStorage.getItem("backslope_angle");
    }
    if (localStorage.getItem("backslope_rise")) {
        document.getElementById("backslope_rise").value = localStorage.getItem("backslope_rise");
    }

    // Save data when input changes
    form.addEventListener("input", function() {
        localStorage.setItem("wall_height", document.getElementById("wall_height").value);
        localStorage.setItem("embedment_depth", document.getElementById("embedment_depth").value);
        localStorage.setItem("wall_length", document.getElementById("wall_length").value);
        localStorage.setItem("wall_batter", document.getElementById("wall_batter").value);
        localStorage.setItem("backslope_angle", document.getElementById("backslope_angle").value);
        localStorage.setItem("backslope_rise", document.getElementById("backslope_rise").value);
    });

    // Save form data asynchronously with AJAX
    form.addEventListener("submit", function(e) {
        e.preventDefault();  // Prevent normal form submission

        const formData = new FormData(form);

        // Send the data to the server using AJAX
        fetch('/geometry', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);  // Show a success message
            // Optionally, you can clear localStorage after successful save
            // localStorage.removeItem("wall_height");
            // localStorage.removeItem("embedment_depth");
            // localStorage.removeItem("wall_length");
            // localStorage.removeItem("wall_batter");
            // localStorage.removeItem("backslope_angle");
            // localStorage.removeItem("backslope_rise");
        })
        .catch(error => {
            console.error('Error:', error);
            alert('There was an error with the submission.');
        });
    });
});