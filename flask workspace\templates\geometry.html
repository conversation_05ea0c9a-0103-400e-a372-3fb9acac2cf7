{% extends "base.html" %} {% block content %}
<div id="geometry-form">
  <h1>Geometry</h1>
  <form id="geometry-form">
    <div class="form-group">
      <label for="wall_height">Wall Height</label>
      <input
        type="text"
        class="form-control"
        id="wall_height"
        name="wall_height"
        value="{{ data.get('wall_height', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="embedment_depth">Embedment Depth</label>
      <input
        type="text"
        class="form-control"
        id="embedment_depth"
        name="embedment_depth"
        value="{{ data.get('embedment_depth', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="wall_length">Wall Length</label>
      <input
        type="text"
        class="form-control"
        id="wall_length"
        name="wall_length"
        value="{{ data.get('wall_length', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="wall_batter"><PERSON> Batter</label>
      <input
        type="text"
        class="form-control"
        id="wall_batter"
        name="wall_batter"
        value="{{ data.get('wall_batter', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="backslope_angle">Backslope Angle</label>
      <input
        type="text"
        class="form-control"
        id="backslope_angle"
        name="backslope_angle"
        value="{{ data.get('backslope_angle', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="backslope_rise">Backslope Rise</label>
      <input
        type="text"
        class="form-control"
        id="backslope_rise"
        name="backslope_rise"
        value="{{ data.get('backslope_rise', '') }}"
      />
    </div>
    <button type="submit" class="btn btn-primary">Save</button>
  </form>
</div>
<script src="{{ url_for('static', filename='js/geometry.js') }}"></script>
{% endblock %}
