{% extends "base.html" %} {% block content %}
<div class="container" id="reinforcementlayout-form">
  <h2 class="text-center">Reinforcement Layout</h2>

  <div class="table-container">
    <table id="reinforcementLayoutTable" class="table table-bordered">
      <thead class="table-light">
        <tr>
          <th>Location from bottom (m)</th>
          <th>Length (m)</th>
          <th>Coverage Ratio</th>
          <th>Reinforcement Type</th>
          <th>Connection Strength RF (Long-Term)</th>
          <th>Connection Strength RF (Bio-Chem)</th>
          <th>Remove</th>
        </tr>
      </thead>
      <tbody>
        <!-- Rows will be added here by JavaScript -->
      </tbody>
    </table>
  </div>

  <button class="btn-add" onclick="addRow()">Add Reinforcement</button>
  <div id="message" class="mt-3"></div>
  <button class="btn btn-primary" onclick="saveData()">Save Layout</button>
</div>

<script src="{{ url_for('static', filename='js/reinforcementlayout.js') }}"></script>
{% endblock %}
