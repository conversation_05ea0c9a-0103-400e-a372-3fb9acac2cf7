{% extends "base.html" %} {% block content %}
<div id="foundationsoil-form">
  <h1>Foundation Soil Inputs</h1>
  <form id="foundationsoil-form">
    <div class="form-group">
      <label for="foundationsoildensity"
        >Equivalent Foundation Soil Density (γ: kN/m³):</label
      >
      <input
        type="number"
        id="foundationsoildensity"
        name="foundationsoildensity"
        step="0.1"
        required
        value="{{ data.get('foundationsoildensity', '') }}"
      />
    </div>

    <div class="form-group">
      <label for="foundationsoilfriction_angle"
        >Equivalent Angle of Internal Friction (φ°):</label
      >
      <input
        type="number"
        id="foundationsoilfriction_angle"
        name="foundationsoilfriction_angle"
        step="0.1"
        required
        value="{{ data.get('foundationsoilfriction_angle', '') }}"
      />
    </div>

    <div class="form-group">
      <label for="foundationsoilcohesion"
        >Equivalent Cohesion (c: kN/m²):</label
      >
      <input
        type="number"
        id="foundationsoilcohesion"
        name="foundationsoilcohesion"
        step="0.1"
        required
        value="{{ data.get('foundationsoilcohesion', '') }}"
      />
    </div>

    <div class="form-group">
      <label for="eccentricity">Allowable Eccentricity: Static (e/L):</label>
      <input
        type="number"
        id="eccentricity"
        name="eccentricity"
        step="0.1"
        required
        value="{{ data.get('eccentricity', '') }}"
      />
    </div>

    <div class="form-group">
      <label for="eccentricity_seismic"
        >Allowable Eccentricity: Seismic (e/L):</label
      >
      <input
        type="number"
        id="eccentricity_seismic"
        name="eccentricity_seismic"
        step="0.1"
        required
        value="{{ data.get('eccentricity_seismic', '') }}"
      />
    </div>

    <div class="form-group">
      <label for="watertable"
        >Water Table Depth Below Founding Level (m):</label
      >
      <input
        type="number"
        id="watertable"
        name="watertable"
        step="0.1"
        required
        value="{{ data.get('watertable', '') }}"
      />
    </div>

    <button type="submit">Save Foundation Soil Inputs</button>
  </form>

  <div class="note">
    <p>Note: The water table depth affects effective stress calculations.</p>
  </div>
</div>
<script src="{{ url_for('static', filename='js/foundationsoil.js') }}"></script>
{% endblock %}
