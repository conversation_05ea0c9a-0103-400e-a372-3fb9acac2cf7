<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GRS Wall Design Report</title>
    <style>
        /* General Styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 10px;
        }

        .report-container {
            max-width: 100%;
            margin: 0 auto;
            background: #fff;
            padding: 10px;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }

        header {
            text-align: center;
            margin-bottom: 20px;
        }

        header h1 {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .generation-date {
            font-size: 12px;
            color: #777;
        }

        /* Section Styles */
        .section {
            margin-bottom: 20px;
        }

        .section h2 {
            font-size: 20px;
            color: #34495e;
            border-bottom: 2px solid #34495e;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .section h3 {
            font-size: 18px;
            color: #2c3e50;
            margin-top: 15px;
            margin-bottom: 10px;
        }

        /* Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 12px;
        }

        .data-table th,
        .data-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .data-table th {
            background-color: #34495e;
            color: #fff;
            font-weight: bold;
        }

        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .data-table tr:hover {
            background-color: #f1f1f1;
        }

        /* Success and Danger Classes */
        .text-success {
            color: #28a745;
            font-weight: bold;
        }

        .text-danger {
            color: #dc3545;
            font-weight: bold;
        }

        /* Footer Note */
        .footer-note {
            margin-top: 20px;
            padding: 10px;
            background-color: #f1f1f1;
            border-left: 5px solid #34495e;
            font-size: 12px;
        }

        .footer-note p {
            margin: 0;
            color: #555;
        }

        /* Landscape Mode */
        @page {
            size: A4 landscape;
            margin: 10mm;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <header>
            <h1>GRS Wall Design Report</h1>
            <p class="generation-date">Generated on: {{ generation_date }}</p>
        </header>

        <!-- Project Information -->
        <section class="section">
            <h2>Project Information</h2>
            <table class="data-table">
                <tr><th>Project Name</th><td>{{ project_info.project_name }}</td></tr>
                <tr><th>Project ID</th><td>{{ project_info.project_id }}</td></tr>
                <tr><th>Designer</th><td>{{ project_info.designer }}</td></tr>
                <tr><th>Client</th><td>{{ project_info.client }}</td></tr>
                <tr><th>Date</th><td>{{ project_info.date }}</td></tr>
                <tr><th>Description</th><td>{{ project_info.description }}</td></tr>
                <tr><th>Revision</th><td>{{ project_info.revision }}</td></tr>
            </table>
        </section>

        <!-- Wall Geometry -->
        <section class="section">
            <h2>Wall Geometry</h2>
            <table class="data-table">
                <tr><th>Wall Height</th><td>{{ geometry.wall_height }} m</td></tr>
                <tr><th>Wall Length</th><td>{{ geometry.wall_length }} m</td></tr>
                <tr><th>Wall Batter</th><td>{{ geometry.wall_batter }}°</td></tr>
                <tr><th>Backslope Angle</th><td>{{ geometry.backslope_angle }}°</td></tr>
                <tr><th>Backslope Rise</th><td>{{ geometry.backslope_rise }}</td></tr>
                <tr><th>Embedment Depth</th><td>{{ geometry.embedment_depth }} m</td></tr>
            </table>
        </section>

        <!-- Soil Properties -->
        <section class="section">
            <h2>Soil Properties</h2>
            <h3>Reinforced Soil</h3>
            <table class="data-table">
                <tr><th>Density</th><td>{{ soil_properties.reinforced_soil.density }} kN/m³</td></tr>
                <tr><th>Friction Angle</th><td>{{ soil_properties.reinforced_soil.friction_angle }}°</td></tr>
                <tr><th>Cohesion</th><td>{{ soil_properties.reinforced_soil.cohesion }} kPa</td></tr>
            </table>

            <h3>Retained Soil</h3>
            <table class="data-table">
                <tr><th>Density</th><td>{{ soil_properties.retained_soil.density }} kN/m³</td></tr>
                <tr><th>Friction Angle</th><td>{{ soil_properties.retained_soil.friction_angle }}°</td></tr>
                <tr><th>Cohesion</th><td>{{ soil_properties.retained_soil.cohesion }} kPa</td></tr>
            </table>

            <h3>Foundation Soil</h3>
            <table class="data-table">
                <tr><th>Density</th><td>{{ soil_properties.foundation_soil.density }} kN/m³</td></tr>
                <tr><th>Friction Angle</th><td>{{ soil_properties.foundation_soil.friction_angle }}°</td></tr>
                <tr><th>Cohesion</th><td>{{ soil_properties.foundation_soil.cohesion }} kPa</td></tr>
                <tr><th>Eccentricity</th><td>{{ soil_properties.foundation_soil.eccentricity }}</td></tr>
                <tr><th>Eccentricity (Seismic)</th><td>{{ soil_properties.foundation_soil.eccentricity_seismic }}</td></tr>
                <tr><th>Water Table</th><td>{{ soil_properties.foundation_soil.watertable }}</td></tr>
            </table>
        </section>

        <!-- External Loads -->
        <section class="section">
            <h2>External Loads</h2>
            <h3>Dead Loads</h3>
            <table class="data-table">
                <tr><th>Load 1</th><td>{{ external_loads.get('dead_loads', [])[0] }}</td></tr>
                <tr><th>Load 2</th><td>{{ external_loads.get('dead_loads', [])[1] }}</td></tr>
                <tr><th>Load 3</th><td>{{ external_loads.get('dead_loads', [])[2] }}</td></tr>
            </table>

            <h3>Live Loads</h3>
            <table class="data-table">
                <tr><th>Load 1</th><td>{{ external_loads.get('live_loads', [])[0] }}</td></tr>
                <tr><th>Load 2</th><td>{{ external_loads.get('live_loads', [])[1] }}</td></tr>
                <tr><th>Load 3</th><td>{{ external_loads.get('live_loads', [])[2] }}</td></tr>
            </table>

            <h3>Strip Loads</h3>
            <table class="data-table">
                <tr><th>Vertical Strip Load 1</th><td>{{ external_loads.get('vertical_strip_load', [])[0] }}</td></tr>
                <tr><th>Vertical Strip Load 2</th><td>{{ external_loads.get('vertical_strip_load', [])[1] }}</td></tr>
                <tr><th>Vertical Strip Load 3</th><td>{{ external_loads.get('vertical_strip_load', [])[2] }}</td></tr>
                <tr><th>Horizontal Strip Load 1</th><td>{{ external_loads.get('horizontal_strip_load', [])[0] }}</td></tr>
                <tr><th>Horizontal Strip Load 2</th><td>{{ external_loads.get('horizontal_strip_load', [])[1] }}</td></tr>
                <tr><th>Horizontal Strip Load 3</th><td>{{ external_loads.get('horizontal_strip_load', [])[2] }}</td></tr>
                <tr><th>Strip Load Width 1</th><td>{{ external_loads.get('strip_load_width', [])[0] }}</td></tr>
                <tr><th>Strip Load Width 2</th><td>{{ external_loads.get('strip_load_width', [])[1] }}</td></tr>
                <tr><th>Strip Load Width 3</th><td>{{ external_loads.get('strip_load_width', [])[2] }}</td></tr>
                <tr><th>Strip Load Distance 1</th><td>{{ external_loads.get('strip_load_distance', [])[0] }}</td></tr>
                <tr><th>Strip Load Distance 2</th><td>{{ external_loads.get('strip_load_distance', [])[1] }}</td></tr>
                <tr><th>Strip Load Distance 3</th><td>{{ external_loads.get('strip_load_distance', [])[2] }}</td></tr>
            </table>

            <h3>Earthquake Acceleration</h3>
            <table class="data-table">
                <tr><th>Acceleration</th><td>{{ external_loads.get('earthquake_acceleration', '') }}</td></tr>
                <tr><th>Seismic Force</th><td>{{ external_loads.get('seismic_force', '') }}</td></tr>
            </table>

            <h3>Impact Loads</h3>
            <table class="data-table">
                <tr><th>Rupture Impact Upper</th><td>{{ external_loads.get('impact_loads', {}).get('rupture', {}).get('upper', '') }}</td></tr>
                <tr><th>Rupture Impact Second</th><td>{{ external_loads.get('impact_loads', {}).get('rupture', {}).get('second', '') }}</td></tr>
                <tr><th>Pullout Impact Upper</th><td>{{ external_loads.get('impact_loads', {}).get('pullout', {}).get('upper', '') }}</td></tr>
                <tr><th>Pullout Impact Second</th><td>{{ external_loads.get('impact_loads', {}).get('pullout', {}).get('second', '') }}</td></tr>
            </table>
        </section>

        <!-- Reinforcement Properties -->
        <section class="section">
            <h2>Reinforcement Properties</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Type ID</th>
                        <th>Name</th>
                        <th>Tult</th>
                        <th>Rfid</th>
                        <th>Rfw</th>
                        <th>Rfcr</th>
                        <th>Fs</th>
                        <th>Pullout Angle</th>
                        <th>Sliding Angle</th>
                        <th>Scale Factor</th>
                    </tr>
                </thead>
                <tbody>
                    {% for reinforcement in reinforcement_data %}
                    <tr>
                        <td>{{ reinforcement.type_id }}</td>
                        <td>{{ reinforcement.name }}</td>
                        <td>{{ reinforcement.tult }}</td>
                        <td>{{ reinforcement.rfid }}</td>
                        <td>{{ reinforcement.rfw }}</td>
                        <td>{{ reinforcement.rfcr }}</td>
                        <td>{{ reinforcement.fs }}</td>
                        <td>{{ reinforcement.pullout_angle }}</td>
                        <td>{{ reinforcement.sliding_angle }}</td>
                        <td>{{ reinforcement.scale_factor }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </section>

        <!-- Reinforcement Layout -->
        <section class="section">
            <h2>Reinforcement Layout</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Connection Strength (Biochem)</th>
                        <th>Connection Strength (Long-term)</th>
                        <th>Coverage Ratio</th>
                        <th>Length</th>
                        <th>Location</th>
                        <th>Reinforcement Type</th>
                    </tr>
                </thead>
                <tbody>
                    {% for layout in reinforcement_layout %}
                    <tr>
                        <td>{{ layout.connection_strength_biochem }}</td>
                        <td>{{ layout.connection_strength_longterm }}</td>
                        <td>{{ layout.coverage_ratio }}</td>
                        <td>{{ layout.length }}</td>
                        <td>{{ layout.location }}</td>
                        <td>{{ layout.reinforcement_type }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </section>

        <!-- External Stability Analysis Results -->
        <section class="section">
            <h2>External Stability Analysis Results</h2>
            <h3>Sliding Check</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Load Case</th>
                        <th>Horizontal Load</th>
                        <th>Vertical Load</th>
                        <th>Sliding Resistance</th>
                        <th>CDR Sliding</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in ['Maximum', 'Minimum', 'Critical'] %}
                    <tr>
                        <td>{{ case }}</td>
                        <td>{{ external_stability.get('Horizontal Load for sliding check', {}).get(case, 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Vertical Load for sliding check', {}).get(case, 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Sliding Resistance for sliding check', {}).get(case, 0) | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if external_stability.get('CDR Sliding for sliding check', {}).get(case, 0) > 1 else 'text-danger' }}">
                            {{ external_stability.get('CDR Sliding for sliding check', {}).get(case, 0) | default('N/A') | round(2) }}
                        </td>
                    </tr>
                    {% endfor %}
                    <tr>
                        <td>Earthquake</td>
                        <td>{{ external_stability.get('Earthquake case Values for sliding check', {}).get('EQRh', 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Earthquake case Values for sliding check', {}).get('EQRv', 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Earthquake case Values for sliding check', {}).get('EQ Sliding Resistance', 0) | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if external_stability.get('Earthquake case Values for sliding check', {}).get('EQ CDR Sliding', 0) > 1 else 'text-danger' }}">
                            {{ external_stability.get('Earthquake case Values for sliding check', {}).get('EQ CDR Sliding', 0) | default('N/A') | round(2) }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <h3>Bearing Capacity Check</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Load Case</th>
                        <th>Vertical Load</th>
                        <th>Overturning Moment</th>
                        <th>Resistance Moment</th>
                        <th>CDR Bearing</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in ['Maximum', 'Minimum', 'Critical', 'Service'] %}
                    <tr>
                        <td>{{ case }}</td>
                        <td>{{ external_stability.get('Rv for bearing check', {}).get('Rv ' + case, 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Overturning moment for bearing capacity check', {}).get(case, 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Moment Resistance for bearing capacity check', {}).get(case, 0) | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if external_stability.get('CDR bearing capacity check', {}).get(case, 0) > 1 else 'text-danger' }}">
                            {{ external_stability.get('CDR bearing capacity check', {}).get(case, 0) | default('N/A') | round(2) }}
                        </td>
                    </tr>
                    {% endfor %}
                    <tr>
                        <td>Earthquake</td>
                        <td>{{ external_stability.get('Earthquake case for bearing check', {}).get('EQRvbearing', 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Earthquake case for bearing check', {}).get('EQMobearing', 0) | default('N/A') | round(2) }}</td>
                        <td>{{ external_stability.get('Earthquake case for bearing check', {}).get('EQMrbearing', 0) | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if external_stability.get('Earthquake case for bearing check', {}).get('CDR Bearing EQ', 0) > 1 else 'text-danger' }}">
                            {{ external_stability.get('Earthquake case for bearing check', {}).get('CDR Bearing EQ', 0) | default('N/A') | round(2) }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- Internal Stability Analysis Results -->
        <section class="section">
            <h2>Internal Stability Analysis Results</h2>
            <h3>Static Results</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Layer</th>
                        <th>Location from Bottom</th>
                        <th>CDR Internal Sliding</th>
                        <th>CDR Rupture</th>
                        <th>CDR Pullout</th>
                        <th>Tensile Load</th>
                        <th>IRh Critical</th>
                        <th>IRv Critical</th>
                        <th>Pullout Capacity</th>
                        <th>CDR Rupture Impact</th>
                        <th>CDR Pullout Impact</th>
                        <th>CDR Connection</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in internal_stability['Reinforcement Results'] %}
                    <tr>
                        <td>Layer {{ loop.index }}</td>
                        <td>{{ result['Depth from Bottom'] | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if result['CDR Internal Sliding Critical'] > 1 else 'text-danger' }}">
                            {{ result['CDR Internal Sliding Critical'] | default('N/A') | round(2) }}
                        </td>
                        <td class="{{ 'text-success' if result['CDR Rupture Critical'] > 1 else 'text-danger' }}">
                            {{ result['CDR Rupture Critical'] | default('N/A') | round(2) }}
                        </td>
                        <td class="{{ 'text-success' if result['CDR Pullout Critical'] > 1 else 'text-danger' }}">
                            {{ result['CDR Pullout Critical'] | default('N/A') | round(2) }}
                        </td>
                        <td>{{ result['Tensile Load Critical'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['IRh Critical'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['IRv Critical'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['Pullout Capacity Critical'] | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if result['CDR Rupture Impact'] > 1 else 'text-danger' }}">
                            {{ result['CDR Rupture Impact'] | default('N/A') | round(2) }}
                        </td>
                        <td class="{{ 'text-success' if result['CDR Pullout Impact'] > 1 else 'text-danger' }}">
                            {{ result['CDR Pullout Impact'] | default('N/A') | round(2) }}
                        </td>
                        <td class="{{ 'text-success' if result['CDR Connection Critical'] > 1 else 'text-danger' }}">
                            {{ result['CDR Connection Critical'] | default('N/A') | round(2) }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <h3>Earthquake Results</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Layer</th>
                        <th>Location from Bottom</th>
                        <th>CDR Internal Sliding</th>
                        <th>Tensile Load</th>
                        <th>Dynamic Tensile Load</th>
                        <th>Static Tensile Load</th>
                        <th>Static Required Tensile Load</th>
                        <th>Dynamic Required Tensile Load</th>
                        <th>Pullout Capacity</th>
                        <th>CDR Rupture</th>
                        <th>CDR Pullout</th>
                        <th>CDR Connection</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in internal_stability['EQ Reinforcement Results'] %}
                    <tr>
                        <td>Layer {{ loop.index }}</td>
                        <td>{{ result['Depth from Bottom'] | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if result['CDR Internal Sliding'] > 1 else 'text-danger' }}">
                            {{ result['CDR Internal Sliding'] | default('N/A') | round(2) }}
                        </td>
                        <td>{{ result['Tensile Load'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['Dynamic Tensile Load'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['Static Tensile Load'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['Static Required Tensile Load'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['Dynamic Required Tensile Load'] | default('N/A') | round(2) }}</td>
                        <td>{{ result['Pullout Capacity'] | default('N/A') | round(2) }}</td>
                        <td class="{{ 'text-success' if result['CDR Rupture'] > 1 else 'text-danger' }}">
                            {{ result['CDR Rupture'] | default('N/A') | round(2) }}
                        </td>
                        <td class="{{ 'text-success' if result['CDR Pullout'] > 1 else 'text-danger' }}">
                            {{ result['CDR Pullout'] | default('N/A') | round(2) }}
                        </td>
                        <td class="{{ 'text-success' if result['CDR Connection'] > 1 else 'text-danger' }}">
                            {{ result['CDR Connection'] | default('N/A') | round(2) }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </section>

        <!-- Footer Note -->
        <footer class="footer-note">
            <p><strong>Note:</strong> CDR (Capacity Demand Ratio) is defined as the factored resistance divided by the factored force. In limit states, it should be more than one to ensure safety.</p>
        </footer>
    </div>
</body>
</html>