<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GRS Wall Designer</title>
    <!-- Add Bootstrap CSS -->
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Add Font Awesome for icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Your existing CSS files -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/geometry.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/home.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/project_info.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/reinforcedsoil.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/retainedsoil.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/externalloads.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/reinforcementproperties.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/reinforcementlayout.css') }}"
    />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
      <a class="navbar-brand" href="{{ url_for('home') }}">GRS Wall Designer</a>
      <button
        class="navbar-toggler"
        type="button"
        data-toggle="collapse"
        data-target="#navbarNav"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('home') }}">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('project_info') }}"
              >Project Info</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('geometry') }}">Geometry</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('reinforcedsoil') }}"
              >Reinforced Soil</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('retainedsoil') }}"
              >Retained Soil</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('foundationsoil') }}"
              >Foundation Soil</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('externalloads') }}"
              >External Loads</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('reinforcementproperties') }}"
              >Reinforcement Properties</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('reinforcementlayout') }}"
              >Reinforcement Layout</a
            >
          </li>
        </ul>
      </div>
    </nav>

    <main class="container mt-4">{% block content %}{% endblock %}</main>

    <footer class="footer mt-auto py-3 bg-light">
      <div class="container text-center">
        <span class="text-muted"
          >© 2025 GRS Wall Designer. All rights reserved.</span
        >
      </div>
    </footer>

    <!-- Add Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <!-- Your existing JS file -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
  </body>
</html>
