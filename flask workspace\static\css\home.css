/* Existing styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

nav {
    background-color: #333;
    color: #fff;
    padding: 1rem;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin-right: 1rem;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
}

main {
    padding: 2rem;
}

h1 {
    color: #4a4a4a;
    font-size: 24px;
    margin-bottom: 20px;
}

.hero {
    background-color: #798387;
    color: white;
    text-align: center;
    padding: 4rem 2rem;
    margin-bottom: 2rem;
}

.hero h1 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero .tagline {
    font-size: 1.4rem;
    font-weight: 300;
    margin-bottom: 2rem;
}

.features {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.feature {
    flex-basis: 30%;
    text-align: center;
    padding: 2rem;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 2rem;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.feature i {
    font-size: 3rem;
    color: #1a5f7a;
    margin-bottom: 1rem;
}

.feature h2 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature p {
    color: #666;
}

.cta {
    background-color: #f0f8ff;
    text-align: center;
    padding: 4rem 2rem;
    margin-top: 3rem;
    border-radius: 8px;
}

.cta h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.cta p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-button {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    font-size: 1.1rem;
    transition: background-color 0.3s ease;
}

.cta-button:hover {
    background-color: #45a049;
}

@media (max-width: 768px) {
    .features {
        flex-direction: column;
    }
    
    .feature {
        flex-basis: 100%;
        margin-bottom: 2rem;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero .tagline {
        font-size: 1.2rem;
    }

    .cta h2 {
        font-size: 1.8rem;
    }

    .cta p {
        font-size: 1rem;
    }
}
