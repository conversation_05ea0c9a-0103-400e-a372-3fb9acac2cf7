/* General Styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f9f9;
    margin: 0;
    padding: 20px;
}

.report-container {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.generation-date {
    font-size: 14px;
    color: #777;
}

/* Section Styles */
.section {
    margin-bottom: 30px;
}

.section h2 {
    font-size: 24px;
    color: #34495e;
    border-bottom: 2px solid #34495e;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.section h3 {
    font-size: 20px;
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 10px;
}

/* Table Styles */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.data-table th,
.data-table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
}

.data-table th {
    background-color: #34495e;
    color: #fff;
    font-weight: bold;
}

.data-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.data-table tr:hover {
    background-color: #f1f1f1;
}

/* Success and Danger Classes */
.text-success {
    color: #28a745;
    font-weight: bold;
}

.text-danger {
    color: #dc3545;
    font-weight: bold;
}

/* Footer Note */
.footer-note {
    margin-top: 30px;
    padding: 15px;
    background-color: #f1f1f1;
    border-left: 5px solid #34495e;
}

.footer-note p {
    margin: 0;
    font-size: 14px;
    color: #555;
}