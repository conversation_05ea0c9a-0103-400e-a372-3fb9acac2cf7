{% extends "base.html" %}

{% block content %}
<div id="reinforcementlayout-form">
    <h2>Reinforcement Layout</h2>
    <div class="table-container">
        <table id="reinforcementLayoutTable" class="table table-bordered">
            <thead class="table-light">
                <tr>
                    <th>#</th>
                    <th>Location from bottom (m)</th>
                    <th>Length (m)</th>
                    <th>Coverage Ratio</th>
                    <th>Reinforcement Type</th>
                    <th>Connection Strength RF (Long-Term)</th>
                    <th>Connection Strength RF (Bio-Chem)</th>
                    <th>Remove</th>
                </tr>
            </thead>
            <tbody>
                <!-- Rows will be dynamically added here -->
            </tbody>
        </table>
    </div>
    <button type="button" id="addReinforcementLayoutRowBtn" class="btn btn-add">Add Row</button>
    <button type="button" id="saveReinforcementLayoutBtn" class="btn btn-primary">Save Layout</button>
</div>

<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script>
    $(document).ready(function() {
        let rowCounter = 0; // Initialize at the top

        // Function to add a new row to the reinforcement layout table
        function addReinforcementLayoutRow() {
            rowCounter++; // Increment rowCounter to ensure unique IDs

            let reinforcementOptions = ''; // Build the options dynamically

            {% for item in reinforcement_data %}
                reinforcementOptions += `<option value="{{ item['name'] }}">{{ item['name'] }}</option>`;
            {% endfor %}

            const newRow = `
                <tr id="row_${rowCounter}">
                    <td>${rowCounter}</td>
                    <td><input type="number" name="location_${rowCounter}" required></td>
                    <td><input type="number" name="length_${rowCounter}" required></td>
                    <td><input type="number" name="coverage_ratio_${rowCounter}" required></td>
                    <td>
                        <select name="reinforcement_type_${rowCounter}" required>
                            ${reinforcementOptions}
                        </select>
                    </td>
                    <td><input type="number" name="connection_strength_longterm_${rowCounter}" required></td>
                    <td><input type="number" name="connection_strength_biochem_${rowCounter}" required></td>
                    <td><button type="button" class="btn btn-danger btn-remove-layout-row" data-row="${rowCounter}">Remove</button></td>
                </tr>
            `;
            $("#reinforcementLayoutTable tbody").append(newRow);
            saveLayoutToLocalStorage();
        }

        // Function to remove a row
        function removeReinforcementLayoutRow(rowId) {
            $("#row_" + rowId).remove();
            saveLayoutToLocalStorage();
        }

        // Function to save layout to localStorage
        function saveLayoutToLocalStorage() {
            const layoutData = [];

            // Iterate through each row in the table body
            $("#reinforcementLayoutTable tbody tr").each(function() {
                const row = $(this);
                const location = row.find("input[name^='location']").val();
                const length = row.find("input[name^='length']").val();
                const coverage_ratio = row.find("input[name^='coverage_ratio']").val();
                const reinforcement_type = row.find("select[name^='reinforcement_type']").val();
                const connection_strength_longterm = row.find("input[name^='connection_strength_longterm']").val();
                const connection_strength_biochem = row.find("input[name^='connection_strength_biochem']").val();

                // Create an object with the data from the row
                const rowData = {
                    location: location,
                    length: length,
                    coverage_ratio: coverage_ratio,
                    reinforcement_type: reinforcement_type,
                    connection_strength_longterm: connection_strength_longterm,
                    connection_strength_biochem: connection_strength_biochem
                };

                // Add the row data to the layoutData array
                layoutData.push(rowData);
            });
            console.log("Saving to localStorage:", layoutData);  // Debugging
            localStorage.setItem('reinforcementLayoutData', JSON.stringify(layoutData));
        }

        // Function to load layout from localStorage
        function loadLayoutFromLocalStorage() {
            console.log("Loading from localStorage");  // Debugging
            const storedLayoutData = localStorage.getItem('reinforcementLayoutData');

            if (storedLayoutData) {
                const layoutData = JSON.parse(storedLayoutData);
                 console.log("Loaded from localStorage:", layoutData);  // Debugging

                // Clear existing rows
                $("#reinforcementLayoutTable tbody").empty();

                // Reset rowCounter based on loaded data
                rowCounter = 0;

                // Rebuild the table with data from localStorage
                layoutData.forEach(rowData => {
                    addReinforcementLayoutRowWithData(rowData);
                });
            }
             else {
                // If there's no data in local storage, add an initial row
                addReinforcementLayoutRow();
             }
        }
        
        // Function to add a new row with data
        function addReinforcementLayoutRowWithData(rowData) {
            rowCounter++;

            let reinforcementOptions = ''; // Build the options dynamically
            {% for item in reinforcement_data %}
                reinforcementOptions += `<option value="{{ item['name'] }}" ${rowData.reinforcement_type === "{{ item['name'] }}" ? 'selected' : ''}>{{ item['name'] }}</option>`;
            {% endfor %}

            const newRow = `
                <tr id="row_${rowCounter}">
                    <td>${rowCounter}</td>
                    <td><input type="number" name="location_${rowCounter}" value="${rowData.location}" required></td>
                    <td><input type="number" name="length_${rowCounter}" value="${rowData.length}" required></td>
                    <td><input type="number" name="coverage_ratio_${rowCounter}" value="${rowData.coverage_ratio}" required></td>
                    <td>
                        <select name="reinforcement_type_${rowCounter}" required>
                            ${reinforcementOptions}
                        </select>
                    </td>
                    <td><input type="number" name="connection_strength_longterm_${rowCounter}" value="${rowData.connection_strength_longterm}" required></td>
                    <td><input type="number" name="connection_strength_biochem_${rowCounter}" value="${rowData.connection_strength_biochem}" required></td>
                    <td><button type="button" class="btn btn-danger btn-remove-layout-row" data-row="${rowCounter}">Remove</button></td>
                </tr>
            `;
            $("#reinforcementLayoutTable tbody").append(newRow);
        }

        // Load layout from localStorage on page load
        loadLayoutFromLocalStorage();
        
         //Event handler for input change, save data in localStorage
         $("#reinforcementLayoutTable tbody").on('change', 'input, select', function() {
            saveLayoutToLocalStorage();
         });

        // Event handler for the "Add Row" button
        $("#addReinforcementLayoutRowBtn").click(function() {
            addReinforcementLayoutRow();
        });

        // Event handler for removing a row
        $("#reinforcementLayoutTable tbody").on('click', '.btn-remove-layout-row', function() {
            const rowId = $(this).data('row');
            removeReinforcementLayoutRow(rowId);
        });

        // Event handler for saving the layout (to server)
        $("#saveReinforcementLayoutBtn").click(function() {
            const layoutData = [];

            // Iterate through each row in the table body
            $("#reinforcementLayoutTable tbody tr").each(function() {
                const row = $(this);
                const location = row.find("input[name^='location']").val();
                const length = row.find("input[name^='length']").val();
                const coverage_ratio = row.find("input[name^='coverage_ratio']").val();
                const reinforcement_type = row.find("select[name^='reinforcement_type']").val();
                const connection_strength_longterm = row.find("input[name^='connection_strength_longterm']").val();
                const connection_strength_biochem = row.find("input[name^='connection_strength_biochem']").val();

                // Create an object with the data from the row
                const rowData = {
                    location: location,
                    length: length,
                    coverage_ratio: coverage_ratio,
                    reinforcement_type: reinforcement_type,
                    connection_strength_longterm: connection_strength_longterm,
                    connection_strength_biochem: connection_strength_biochem
                };

                // Add the row data to the layoutData array
                layoutData.push(rowData);
            });

            // Send the layoutData to the server using AJAX
            $.ajax({
                type: "POST",
                url: "/reinforcementlayout",
                contentType: "application/json",
                data: JSON.stringify(layoutData),
                success: function(response) {
                    alert("Reinforcement layout saved successfully!");
                },
                error: function(error) {
                    alert("Error saving reinforcement layout.");
                }
            });
        });
    });
</script>
{% endblock %}
