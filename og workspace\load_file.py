from PySide6.QtWidgets import (QFileDialog,QMessageBox,QTableWidgetItem,QComboBox)

def load_data(self=None):
    options = QFileDialog.Options()
    file_name, _ = QFileDialog.getOpenFileName(self, "Open File", "", "All Files (*);;Text Files (*.txt)", options=options)

    if file_name:
        try:
            with open(file_name, 'r') as file:
                lines = file.readlines()

                # Debugging: Print loaded lines
                print("Loaded lines:", lines)

                # ===========================
                # Initialize reinforcement types (Manually populate or extract)
                # ===========================
                self.reinforcement_types = ["GGU-1", "GGU-2", "GGU-3"]  # Example list, replace with actual data



                def get_line_data(index, label):
                    if index < len(lines):
                        return lines[index].split(":", 1)[1].strip()
                    else:
                        QMessageBox.warning(self, "Load File", f"Missing data for {label}.")
                        return ""

                # ===========================
                # Basic Project Information
                # ===========================
                index_basic_info = 0
                self.projectname_input.setText(get_line_data(index_basic_info, "Project Name"))
                self.projectid_input.setText(get_line_data(index_basic_info + 1, "Project ID"))
                self.designer_input.setText(get_line_data(index_basic_info + 2, "Designer"))
                self.client_input.setText(get_line_data(index_basic_info + 3, "Client"))
                self.description_input.setText(get_line_data(index_basic_info + 4, "Description"))
                self.date_input.setText(get_line_data(index_basic_info + 5, "Date"))
                self.revision_input.setText(get_line_data(index_basic_info + 6, "Revision"))

                # ===========================
                # Wall Geometry Details
                # ===========================
                index_wall_geometry = index_basic_info + 8  # Skip the "Revision" line
                self.wall_height_input.setText(get_line_data(index_wall_geometry + 1, "Wall Height"))
                self.embedment_depth_input.setText(get_line_data(index_wall_geometry + 2, "Embedment Depth"))
                self.wall_length_input.setText(get_line_data(index_wall_geometry + 3, "Wall Length"))
                self.wall_batter_input.setText(get_line_data(index_wall_geometry + 4, "Wall Batter"))
                self.wall_backslopeangle_input.setText(get_line_data(index_wall_geometry + 5, "Wall Back Slope Angle"))
                self.wall_backsloperise_input.setText(get_line_data(index_wall_geometry + 6, "Wall Back Slope Rise"))

                # ===========================
                # Soil Properties
                # ===========================
                index_soil_properties = index_wall_geometry + 9  # Skip over the Wall Geometry block
                self.reinforced_soil_density_input.setText(get_line_data(index_soil_properties + 1, "Reinforced Soil Density"))
                self.reinforced_angle_input.setText(get_line_data(index_soil_properties + 2, "Reinforced Soil Angle"))
                self.reinforced_cohesion_input.setText(get_line_data(index_soil_properties + 3, "Reinforced Soil Cohesion"))

                self.retained_soil_density_input.setText(get_line_data(index_soil_properties + 5, "Retained Soil Density"))
                self.retained_angle_input.setText(get_line_data(index_soil_properties + 6, "Retained Soil Angle"))
                self.retained_cohesion_input.setText(get_line_data(index_soil_properties + 7, "Retained Soil Cohesion"))

                self.foundation_soil_density_input.setText(get_line_data(index_soil_properties + 9, "Foundation Soil Density"))
                self.foundation_angle_input.setText(get_line_data(index_soil_properties + 10, "Foundation Soil Angle"))
                self.foundation_cohesion_input.setText(get_line_data(index_soil_properties + 11, "Foundation Soil Cohesion"))
                self.foundation_eccentricity_input.setText(get_line_data(index_soil_properties + 12, "Foundation Soil Eccentricity"))
                self.foundation_eccentricityseismic_input.setText(get_line_data(index_soil_properties + 13, "Foundation Soil Eccentricity (Seismic)"))
                self.watertabledepth_input.setText(get_line_data(index_soil_properties + 14, "Water Table Depth"))

                # ===========================
                # Loads
                # ===========================
                index_loads = index_soil_properties + 16  # Skip the "Soil Properties" section
                self.dead_load1_input.setText(get_line_data(index_loads + 1, "Dead Load 1"))
                self.dead_load2_input.setText(get_line_data(index_loads + 2, "Dead Load 2"))
                self.dead_load3_input.setText(get_line_data(index_loads + 3, "Dead Load 3"))
                self.live_load1_input.setText(get_line_data(index_loads + 4, "Live Load 1"))
                self.live_load2_input.setText(get_line_data(index_loads + 5, "Live Load 2"))
                self.live_load3_input.setText(get_line_data(index_loads + 6, "Live Load 3"))

                # ===========================
                # Strip Loads
                # ===========================
                index_strip_loads = index_loads + 8  # Skip the "Loads" section
                for i in range(1, 4):
                    self.__dict__[f'Vertical_Strip_Load{i}_input'].setText(get_line_data(index_strip_loads + (i-1)*4, f"Vertical Strip Load {i}"))
                    self.__dict__[f'Horizontal_Strip_Load{i}_input'].setText(get_line_data(index_strip_loads + (i-1)*4 + 1, f"Horizontal Strip Load {i}"))
                    self.__dict__[f'Strip_Load{i}_width_input'].setText(get_line_data(index_strip_loads + (i-1)*4 + 2, f"Width {i}"))
                    self.__dict__[f'Strip_Load{i}_distance_input'].setText(get_line_data(index_strip_loads + (i-1)*4 + 3, f"Distance {i}"))

                self.earthquake_load_input.setText(get_line_data(index_strip_loads + 12, "Earthquake Load"))
                                    # Check if "Use Direct kh" checkbox is required and set its value
                kh_value = get_line_data(index_strip_loads + 14, "Seismic earth pressure load (GLE method)")
                if kh_value and kh_value != "None":  # Check if the value exists and is not a placeholder
                    self.use_direct_kh_checkbox.setChecked(True)
                    self.kh_input.setText(kh_value)
                else:  # If no valid value exists, uncheck the checkbox and clear the field
                    self.use_direct_kh_checkbox.setChecked(False)
                    self.kh_input.clear()

                self.impact_load1_input.setText(get_line_data(index_strip_loads + 16, "ruptureimpact_load upper layer"))
                self.impact_load2_input.setText(get_line_data(index_strip_loads + 17, "ruptureimpact_load second layer"))
                self.impact_load3_input.setText(get_line_data(index_strip_loads + 18, "pulloutimpact_load upper layer"))
                self.impact_load4_input.setText(get_line_data(index_strip_loads + 19, "pulloutimpact_load second layer"))


            # ===========================

                    
            # ===========================
            # Reinforcement Properties
            # ===========================
            index_reinforcement_properties = index_strip_loads + 21  # Skip to Reinforcement Properties section
            reinforcement_properties = []  
            self.reinforcement_properties_table.setRowCount(0)  # Reset table before adding data

            while index_reinforcement_properties < len(lines):
                if "Reinforcement Layers" in lines[index_reinforcement_properties]:
                    break  # Stop when reaching "Reinforcement Layers"
                
                row_data = lines[index_reinforcement_properties].strip().split(", ")
                if any(row_data):  # Only process non-empty lines
                    row_position = self.reinforcement_properties_table.rowCount()
                    self.reinforcement_properties_table.insertRow(row_position)
                    for col, data in enumerate(row_data):
                        self.reinforcement_properties_table.setItem(row_position, col, QTableWidgetItem(data))
                    reinforcement_properties.append(row_data)  # Store the row data
                index_reinforcement_properties += 1

            # Load reinforcement types (assuming 2nd column contains type info)
            self.reinforcement_types = [row[1] for row in reinforcement_properties if len(row) > 1]
            print("Loaded reinforcement types:", self.reinforcement_types)

            # ===========================
            # Reinforcement Layers
            # ===========================
            index_reinforcement_layers = index_reinforcement_properties + 1  # Start after "Reinforcement Layers" keyword
            reinforcement_layers = []  
            self.reinforcement_table.setRowCount(0)  # Reset the details table

            while index_reinforcement_layers < len(lines):
                row_data = lines[index_reinforcement_layers].strip().split(", ")
                
                if any(row_data):  # Ensure line is not empty
                    reinforcement_layers.append(row_data)  
                    row_position = self.reinforcement_table.rowCount()
                    self.reinforcement_table.insertRow(row_position)
                    
                    for col, cell_data in enumerate(row_data):
                        if col == 3:  # Assuming 4th column requires ComboBox
                            combo = QComboBox()
                            combo.addItems(self.reinforcement_types)  # Use the previously loaded types
                            combo.setCurrentText(cell_data.strip())  # Set to match data
                            self.reinforcement_table.setCellWidget(row_position, col, combo)
                        else:
                            item = QTableWidgetItem(cell_data.strip())
                            self.reinforcement_table.setItem(row_position, col, item)
                
                index_reinforcement_layers += 1
                num_reinforcements = len(reinforcement_layers)

            print(f"Number of reinforcement layers loaded: {len(reinforcement_layers)}")


            # ===========================
            # Handle the Reinforcement Table Rows
            # ===========================

            # Store existing data before changing row count
            current_row_count = self.reinforcement_table.rowCount()
            current_data = []

            # Store existing data in the table
            for row in range(current_row_count):
                row_data = []
                for col in range(self.reinforcement_table.columnCount()):
                    item = self.reinforcement_table.item(row, col)
                    if item:
                        row_data.append(item.text())
                    else:
                        row_data.append("")
                current_data.append(row_data)

            # Adjust the row count to match the number of reinforcement layers
            if num_reinforcements > current_row_count:
                # Increase the row count without clearing data
                self.reinforcement_table.setRowCount(num_reinforcements)

            elif num_reinforcements < current_row_count:
                # Decrease the row count
                self.reinforcement_table.setRowCount(num_reinforcements)

            # Repopulate the table with the saved data for rows not modified
            for i in range(min(current_row_count, num_reinforcements)):
                for j in range(len(current_data[i])):
                    self.reinforcement_table.setItem(i, j, QTableWidgetItem(current_data[i][j]))

            # Populate new rows (if any) with reinforcement layer data
            for i in range(min(current_row_count, num_reinforcements), num_reinforcements):
                if i < len(reinforcement_layers):
                    row_data = reinforcement_layers[i]
                    if len(row_data) >= 4:
                        # Set depth, length, coverage ratio
                        self.reinforcement_table.setItem(i, 0, QTableWidgetItem(row_data[0]))  # Depth
                        self.reinforcement_table.setItem(i, 1, QTableWidgetItem(row_data[1]))  # Length
                        self.reinforcement_table.setItem(i, 2, QTableWidgetItem(row_data[2]))  # Coverage Ratio
                        self.reinforcement_table.setItem(i, 4, QTableWidgetItem(row_data[4]))  # RFCRCON
                        self.reinforcement_table.setItem(i, 5, QTableWidgetItem(row_data[5]))  # FRBIOCON


                        # Reinforcement Type ComboBox
                        reinforcement_type_item = QComboBox()
                        if self.reinforcement_types:
                            reinforcement_type_item.addItems(self.reinforcement_types)  # Populate combo box with types
                        else:
                            print("No reinforcement types available.")
                        
                        # Set reinforcement type (from data)
                        reinforcement_type = row_data[3]
                        if reinforcement_type in self.reinforcement_types:
                            reinforcement_type_item.setCurrentText(reinforcement_type)  # Set to the correct type
                        else:
                            reinforcement_type_item.setCurrentIndex(0)  # Default to first if not found

                        # Set the ComboBox in the 4th column
                        self.reinforcement_table.setCellWidget(i, 3, reinforcement_type_item)




            QMessageBox.information(self, "Load File", "Data loaded successfully!")


#                   QMessageBox.information(self, "Load File", "Data loaded successfully!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load file: {e}")


