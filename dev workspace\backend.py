import math 
from flask import Flask, request, jsonify, session

def calculate_pressure(session):
        try:

            # Read standard numerical inputs from the form
            wall_height = float(session.get('wall_height', 0) or 0)
            embedment_depth = float(session.get('embedment_depth', 0) or 0)
            wall_length = float(session.get('wall_length', 0) or 0)
            wall_batter1 = float(session.get('wall_batter', 0) or 0)
            top_slope = float(session.get('backslope_angle', 0) or 0)
            top_rise = float(session.get('backslope_rise', 0) or 0)

            # Soil properties
            reinforced_soil_density = float(session.get('soil_density', 0) or 0)
            reinforced_angle = float(session.get('friction_angle', 0) or 0)
            reinforced_cohesion = float(session.get('cohesion', 0) or 0)

            retained_soil_density = float(session.get('retainedsoil_density', 0) or 0)
            retained_angle = float(session.get('retainedfriction_angle', 0) or 0)
            retained_cohesion = float(session.get('retainedcohesion', 0) or 0)

            foundation_soil_density = float(session.get('foundationsoildensity', 0) or 0)
            foundation_angle = float(session.get('foundationsoilfriction_angle', 0) or 0)
            foundation_cohesion = float(session.get('foundationsoilcohesion', 0) or 0)
            AllowableEccentricity = float(session.get('eccentricity', 0) or 0)
            AllowableEccentricityseismic1 = float(session.get('eccentricity_seismic', 0) or 0)
            watertabledepth = float(session.get('watertable', 0) or 0)



            # Load Inputs
            externalloads_data = session.get('externalloads_data', {})

            dead_loads = externalloads_data.get('dead_loads', [0, 0, 0])
            live_loads = externalloads_data.get('live_loads', [0, 0, 0])
            vertical_strip_loads = externalloads_data.get('vertical_strip_load', [0, 0, 0])
            horizontal_strip_loads = externalloads_data.get('horizontal_strip_load', [0, 0, 0])
            strip_load_widths = externalloads_data.get('strip_load_width', [0, 0, 0])
            strip_load_distances = externalloads_data.get('strip_load_distance', [0, 0, 0])

            dead_load1, dead_load2, dead_load3 = dead_loads
            live_load1, live_load2, live_load3 = live_loads
            vertical_strip_load1, vertical_strip_load2, vertical_strip_load3 = vertical_strip_loads
            horizontal_strip_load1, horizontal_strip_load2, horizontal_strip_load3 = horizontal_strip_loads
            strip_load1_width, strip_load2_width, strip_load3_width = strip_load_widths
            strip_load1_distance, strip_load2_distance, strip_load3_distance = strip_load_distances

            # Impact Loads
            impact_loads = externalloads_data.get('impact_loads', {})
            ruptureimpact_load1 = impact_loads.get('rupture', {}).get('upper', 0)
            ruptureimpact_load2 = impact_loads.get('rupture', {}).get('second', 0)
            pulloutimpact_load1 = impact_loads.get('pullout', {}).get('upper', 0)
            pulloutimpact_load2 = impact_loads.get('pullout', {}).get('second', 0)

            Horizontal_seismic_coeffiecient = externalloads_data.get('earthquake_acceleration', 0)




           # kh_value = float(self.kh_input.text()) # PAE SEISIMIC 
            # Create a dictionary of all input values
            input_values2 = {
                'wall_height': wall_height,
                'embedment_depth': embedment_depth,
                'wall_length': wall_length,
                'wall_batter': wall_batter1,
                'backslope_angle': top_slope,
                'backslope_rise': top_rise,
                # ... (add all other inputs)
            }

            # Log or return the input values
            print("Input values used for calculation:", input_values2)            

    
            if wall_batter1 < 10:
                wall_batter = 0
            else:
                wall_batter = wall_batter1

             ###################################################################################
            # EXTERNAL STBILITY

                    # Calculate Inclination and Xs
            Inclination = math.degrees(math.atan(top_rise / (2 * wall_height)))
            teta =	90+wall_batter

            if top_slope == 0:
                Xs =0
            else:
                Xs = top_rise / math.tan(math.radians(top_slope))

            if top_slope < 0:
                # Display error message in GUI
                # show_error_message("Wall batter cannot be less than 0.")
                beta = None  # You may set beta to None or handle the error as needed
                h = None
                lengthofdlll = None
            elif top_slope == 0:
                beta = 0
                h=0
                lengthofdlll=wall_length       
            elif top_slope > 0:
                if Xs >= 2 * wall_height:
                    beta = top_slope
                    h =  wall_length * math.tan(math.radians(top_slope))
                    lengthofdlll=0 
                else:  # This implicitly means Xs < 2 * wall_height
                    beta = Inclination
                    if Xs > wall_length:
                        h =  wall_length * math.tan(math.radians(top_slope))
                        lengthofdlll=0
                    else:
                        h =  top_rise
                        lengthofdlll= wall_length-Xs 
            else:
                beta = None  # Handle any unexpected cases
                h = None
                lengthofdlll = None
            # External earth pressure calculations: Earth pressure behind reinforced soil block
           
           # karetained = (1- math.sin(math.radians(retained_angle)))/(1 + math.sin(math.radians(retained_angle)))
            
            
            delta=beta

            tauu=(1+(((math.sin(math.radians(retained_angle+delta))*math.sin(math.radians(retained_angle-beta)))/(math.sin(math.radians(teta+beta))*math.sin(math.radians(teta-delta))))**0.5))**2
            karetained= math.sin(math.radians(retained_angle+teta)) * math.sin(math.radians(retained_angle+teta))/(tauu*math.sin(math.radians(teta))*math.sin(math.radians(teta))*math.sin(math.radians(teta-delta)))

            Wallacceleration = (1.45-Horizontal_seismic_coeffiecient)*Horizontal_seismic_coeffiecient
            # for external in IS kh=pga; internal (1.5-a)*a
            # Wallacceleration = Horizontal_seismic_coeffiecient 
            betaseis=top_slope
            alpha=teta
            kv=0
            deltaseis=min(retained_angle, reinforced_angle)
            #delta=beta
            #chi=math.degrees(math.atan(Wallacceleration/(1-kv)))
            
            chi=math.degrees(math.atan(Horizontal_seismic_coeffiecient/(1-kv)))

          
            aa=math.cos(math.radians(retained_angle-chi-90+alpha))*math.cos(math.radians(retained_angle-chi-90+alpha))
            bb=math.cos(math.radians(chi))*math.cos(math.radians(90-alpha))*math.cos(math.radians(90-alpha))*math.cos(math.radians(deltaseis+90-alpha+chi))
            cc=math.sin(math.radians(retained_angle+deltaseis))*math.sin(math.radians(retained_angle-chi-betaseis))
            dd=math.cos(math.radians(deltaseis+90-alpha+chi))*math.cos(math.radians(betaseis-90+alpha))
            ee=(cc/dd)**0.5
            ff=(1+ee)**2
            EQKaretained=aa/(bb*ff)


            phi1 = math.radians(reinforced_angle)
            beta1 = math.radians(beta)
            delta1 = math.radians(delta)
            iota1 = math.radians(wall_batter)

            # Define the cotangent function
            def cot(x):
                return 1 / math.tan(x)

            # Calculate the terms step-by-step
            term1 = -math.tan(phi1 - beta1)
            term2 = math.sqrt(
                math.tan(phi1 - beta1) *
                ((math.tan(phi1 - beta1) + cot(phi1 + iota1)) *
                (1 + math.tan(delta1 - iota1) * cot(phi1 + iota1)))
            )
            term3 = 1 + (math.tan(delta1 - iota1) )*(math.tan(phi1 - beta1) +cot(phi1 + iota1))
            tan_theta_f_minus_phi = (term1 + term2)/term3

            # Calculate theta_f
            theta_f = math.atan(tan_theta_f_minus_phi) + phi1

            # Convert theta_f from radians back to degrees
            theta_f = math.degrees(theta_f)
           # Debugging statements
            # Debugging output to check all input values
            print(f"Wall Height: {wall_height} m")
            print(f"Embedment Depth: {embedment_depth} m")
            print(f"Wall Length: {wall_length} m")
            print(f"Wall Batter: {wall_batter1} degrees")
            print(f"Top Slope Angle: {top_slope} degrees")
            print(f"Top Slope Rise: {top_rise} m")

            print(f"Reinforced Soil Density: {reinforced_soil_density} kN/m³")
            print(f"Reinforced Soil Angle: {reinforced_angle} degrees")
            print(f"Reinforced Soil Cohesion: {reinforced_cohesion} kPa")

            print(f"Retained Soil Density: {retained_soil_density} kN/m³")
            print(f"Retained Soil Angle: {retained_angle} degrees")
            print(f"Retained Soil Cohesion: {retained_cohesion} kPa")

            print(f"Foundation Soil Density: {foundation_soil_density} kN/m³")
            print(f"Foundation Soil Angle: {foundation_angle} degrees")
            print(f"Foundation Soil Cohesion: {foundation_cohesion} kPa")
            print(f"Allowable Eccentricity: {AllowableEccentricity} m")
            print(f"Allowable Eccentricity (Seismic): {AllowableEccentricityseismic1} m")
            print(f"Water Table Depth: {watertabledepth} m")

            print(f"Dead Load 1: {dead_load1} kN")
            print(f"Dead Load 2: {dead_load2} kN")
            print(f"Dead Load 3: {dead_load3} kN")
            print(f"Live Load 1: {live_load1} kN")
            print(f"Live Load 2: {live_load2} kN")
            print(f"Live Load 3: {live_load3} kN")

            print(f"Vertical Strip Load 1: {vertical_strip_load1} kN/m")
            print(f"Horizontal Strip Load 1: {horizontal_strip_load1} kN/m")
            print(f"Strip Load 1 Width: {strip_load1_width} m")
            print(f"Strip Load 1 Distance: {strip_load1_distance} m")

            print(f"Vertical Strip Load 2: {vertical_strip_load2} kN/m")
            print(f"Horizontal Strip Load 2: {horizontal_strip_load2} kN/m")
            print(f"Strip Load 2 Width: {strip_load2_width} m")
            print(f"Strip Load 2 Distance: {strip_load2_distance} m")

            print(f"Vertical Strip Load 3: {vertical_strip_load3} kN/m")
            print(f"Horizontal Strip Load 3: {horizontal_strip_load3} kN/m")
            print(f"Strip Load 3 Width: {strip_load3_width} m")
            print(f"Strip Load 3 Distance: {strip_load3_distance} m")

            print(f"Horizontal Seismic Coefficient: {Horizontal_seismic_coeffiecient}")

            print(f"H: {wall_height}")
            print(f"h: {h}")
            print(f"bater: {wall_batter}")
            print(f"theta_f: {theta_f}")
            print(f"Karetained: {karetained}")
            print(f"EQKaretained: {EQKaretained}")
            print(f"theta_f: {theta_f}")

            #Nominal Loads..................................................

            # Self-weight of the reinforced soil wall 
            V1 =  wall_height * reinforced_soil_density * wall_length
    
            # Vertical Load due to Sloping Surcharge
            
            if top_slope < 0:
                Vslp1 = None
            elif top_slope == 0:
                Vslp1 = 0
            elif top_slope > 0:
                if Xs > wall_length:
                    Vslp1 = 0.5 * reinforced_soil_density * wall_length * h
                else:  # top_slope > 0 and Xs < wall_length
                    Vslp1 = (0.5 * reinforced_soil_density * Xs * top_rise) + (reinforced_soil_density * lengthofdlll * top_rise)
            else:
                Vslp1 = None

            
            # Vertical Load due to Dead Load 
            Vds1 = (dead_load1)*lengthofdlll
            Vds2 = (dead_load2)*lengthofdlll
            Vds3 = (dead_load3)*lengthofdlll

  

            # Vertical Load due to Live Load 

            Vls1 = (live_load1 + live_load2 + live_load3)*lengthofdlll
    
            # Vertical Load due to Strip Load 
            if ((0.5*strip_load1_width)+strip_load1_distance) > wall_length:
                vertical_strip_load11 = 0
                vertical_strip_load22 = 0
                vertical_strip_load33 = 0
            else:
                vertical_strip_load11 = vertical_strip_load1
                vertical_strip_load22 = vertical_strip_load2
                vertical_strip_load33 = vertical_strip_load3


            Vss1 = ((vertical_strip_load11*strip_load1_width) + (vertical_strip_load22*strip_load2_width) + (vertical_strip_load33*strip_load3_width))



           # Horizontal Earth pressure due to Retained fill
            Pretainedfill = 0.5* karetained * retained_soil_density * (wall_height+h) * (wall_height+h)*math.cos(math.radians(delta-wall_batter)) 


           # Horizontal Earth pressure due to Dead load

            if Xs > 2*wall_height:
                dead_load11 = 0
                dead_load22 = 0
                dead_load33 = 0
                live_load11 = 0
                live_load22 = 0
                live_load33 = 0
            else:
                dead_load11 = dead_load1
                dead_load22 = dead_load2
                dead_load33 = dead_load3
                live_load11 = live_load1
                live_load22 = live_load2
                live_load33 = live_load3                


            Pdeadload1 = karetained * (dead_load11 ) * (wall_height+h)*math.cos(math.radians(delta-wall_batter)) 
            Pdeadload2 = karetained * (dead_load22 ) * (wall_height+h)*math.cos(math.radians(delta-wall_batter)) 
            Pdeadload3 = karetained * (dead_load33) * (wall_height+h)*math.cos(math.radians(delta-wall_batter)) 


            # Horizontal Earth pressure due to Live load       
            Pliveload = karetained * (live_load11 + live_load22 + live_load33) * (wall_height+h) * math.cos(math.radians(delta-wall_batter)) 
 

            # Total Horizontal load 

            Rhstrengh1maximum = (Pretainedfill*1.5) + ((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5)) + (Pliveload*1.75)
            Rhstrengh1minimum = (Pretainedfill*0.9) + ((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9)) + (Pliveload*1.75)
            Rhcritical = max(Rhstrengh1maximum,Rhstrengh1minimum) 

            
            # Total vertical load
            Rvstrengh1maximum = (V1*1.35) + ((Vds1*1.35)+(Vds2*1.5)+(Vds3*1.5)) + (Vls1*0) + (Vss1*1.25) + (Vslp1*1.35) + (((Pretainedfill*1.5) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            Rvstrengh1minimum = V1 + ((Vds1*1)+(Vds2*0.65)+(Vds3*0.75)) + (Vls1*0) + (Vss1*0.9) + Vslp1 + (((Pretainedfill*0.9) + (((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            #Rvservice = V1 + Vds1 + (Vls1*0) + Vss1 + Vslp1 + ((Pretainedfill + Pdeadload + Pliveload)*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            Rvcritical = V1 + ((Vds1*1)+(Vds2*0.65)+(Vds3*0.75)) + (Vls1*0) + (Vss1*0.9) + Vslp1 + (((Pretainedfill*1.5) + ((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5)) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))


            #### Sliding Check
            Slidingresistancereinforcedfillstrengh1maximum = (Rvstrengh1maximum * math.tan(math.radians(reinforced_angle)) /1.0)+( reinforced_cohesion* wall_length/1)
            Slidingresistancereinforcedfillstrengh1minimum = (Rvstrengh1minimum * math.tan(math.radians(reinforced_angle)) /1.0)+( reinforced_cohesion* wall_length/1)
            Slidingresistancereinforcedfillcritical = (Rvcritical * math.tan(math.radians(reinforced_angle)) /1.0)+( reinforced_cohesion* wall_length/1.6)
            #Slidingresistancereinforcedfillservice= (Rvservice * math.tan(math.radians(reinforced_angle)) /1.0)+( reinforced_cohesion* wall_length/1.6)
            
            Slidingresistancefoundationstrengh1maximum = (Rvstrengh1maximum * math.tan(math.radians(foundation_angle)) /1.0)+( foundation_cohesion* wall_length/1)
            Slidingresistancefoundationstrengh1minimum = (Rvstrengh1minimum * math.tan(math.radians(foundation_angle)) /1.0)+( foundation_cohesion* wall_length/1)
            Slidingresistancefoundationcritical = (Rvcritical * math.tan(math.radians(foundation_angle)) /1.0)+( foundation_cohesion* wall_length/1)
            #Slidingresistancefoundationservice = (Rvservice * math.tan(math.radians(foundation_angle)) /1.0)+( foundation_cohesion* wall_length/1.6)
            
            Slidingresistancestrengh1maximum = min(Slidingresistancereinforcedfillstrengh1maximum, Slidingresistancefoundationstrengh1maximum)
            Slidingresistancestrengh1minimum = min(Slidingresistancereinforcedfillstrengh1minimum, Slidingresistancefoundationstrengh1minimum)
            Slidingresistancecritical = min(Slidingresistancereinforcedfillcritical, Slidingresistancefoundationcritical)
            #Slidingresistanceservice = min(Slidingresistancereinforcedfillservice, Slidingresistancefoundationservice)

            #sliding resistancereinforcement if first reinforcmeent at 0 level add equations

            CDRslidingstrengh1maximum = Slidingresistancestrengh1maximum * 1/ (Rhstrengh1maximum)
            CDRslidingstrengh1minimum = Slidingresistancestrengh1minimum * 1 / (Rhstrengh1minimum)
            CDRslidingcritical = Slidingresistancecritical * 1 / (Rhcritical)
            #CDRslidingservice = Slidingresistanceservice * 1 / (Rhservice)


                   

   
            
            #### Overturning Check


             # Self-weight of the reinforced soil wall 
            M1 =  wall_height * reinforced_soil_density * wall_length * wall_length / 2


            # Vertical Load due to Sloping Surcharge
            
            if top_slope < 0:
                Mslp1 = None
            elif top_slope == 0:
                Mslp1 = 0
            elif top_slope > 0:
                if Xs > wall_length:
                    Mslp1 = 0.5 * reinforced_soil_density * wall_length * h * 2 * wall_length/3
                else:  # top_slope > 0 and Xs < wall_length
                    Mslp1 = (0.5 * reinforced_soil_density * Xs * top_rise*2*Xs/3) + (reinforced_soil_density * lengthofdlll * top_rise*(Xs+(0.5*lengthofdlll)))
            else:
                Mslp1 = None


            
            # Vertical Load due to Dead Load 
            Mds1 = (dead_load1)*lengthofdlll *(Xs+(lengthofdlll / 2))
            Mds2 = (dead_load2)*lengthofdlll *(Xs+(lengthofdlll / 2))
            Mds3 = (dead_load3)*lengthofdlll *(Xs+(lengthofdlll / 2))


            # Vertical Load due to Live Load 

            Mls1 = (live_load1 + live_load2 + live_load3)*lengthofdlll * (Xs+(lengthofdlll / 2))
 

            # Vertical Load due to Strip Load 
            Mss1 = ((vertical_strip_load11*strip_load1_width*strip_load1_distance) + (vertical_strip_load22*strip_load2_width*strip_load2_distance) + (vertical_strip_load33*strip_load3_width*strip_load3_distance))

            
            Mostrengh1maximumforECC = (Pretainedfill *1.5* (wall_height+h) / 3) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))* (wall_height+h) / 2) + (Pliveload *1.75* (wall_height+h) / 2)
            Mostrengh1minimumforECC = (Pretainedfill *0.9* (wall_height+h) / 3) + (((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9))* (wall_height+h) / 2) + (Pliveload *1.75* (wall_height+h) / 2)
           # MoserviceforECC = (Pretainedfill * (wall_height+h) / 3) + (Pdeadload * (wall_height+h) / 2) + (Pliveload * (wall_height+h) / 2)
            MocriticalforECC = max(Mostrengh1maximumforECC,Mostrengh1minimumforECC)

            Mrstrengh1maximumforECC = (M1*1.35) + ((Mds1*1.35)+(Mds2*1.5)+(Mds3*1.5))  + (Mls1*0) + (Mss1*1.25) + (Mslp1*1.35) + (((Pretainedfill*1.5) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)
            Mrstrengh1minimumforECC = M1 + ((Mds1*1)+(Mds2*0.65)+(Mds3*0.75))  + (Mls1*0) + (Mss1*0.9) + Mslp1 + (((Pretainedfill*0.9) + (((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)
           # MrserviceforECC = M1 + Mds1 + Mls1 + Mss1 + Mslp1 + ((Pretainedfill + Pdeadload + Pliveload)*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)
            MrcriticalforECC = (M1*1) + ((Mds1*1)+(Mds2*0.65)+(Mds3*0.75)) + (Mls1*0) + (Mss1*0.9) + (Mslp1*1) + (((Pretainedfill*1.5) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)

            Rvstrengh1maximumforECC = (V1*1.35) + ((Vds1*1.35)+(Vds2*1.5)+(Vds3*1.5)) + (Vls1*0) + (Vss1*1.25) + (Vslp1*1.35) + (((Pretainedfill*1.5) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            Rvstrengh1minimumforECC = V1 + ((Vds1*1)+(Vds2*0.65)+(Vds3*0.75)) + (Vls1*0) + (Vss1*0.9) + (Vslp1) + (((Pretainedfill*0.9) + (((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            RvcriticalforECC = V1 + ((Vds1*1)+(Vds2*0.65)+(Vds3*0.75))+ (Vls1*0) + (Vss1*0.9) + Vslp1 + (((Pretainedfill*1.5) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))    
            RvserviceforECC = V1 +((Vds1*1)+(Vds2*1)+(Vds3*1)) + (Vls1*0) + Vss1 + Vslp1 + ((Pretainedfill + ((Pdeadload1)+(Pdeadload2)+(Pdeadload3)) + Pliveload)*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            
            
      

            EmaxforECC = (wall_length/2)- ((Mrstrengh1maximumforECC-Mostrengh1maximumforECC)/(Rvstrengh1maximumforECC))
            EminforECC = (wall_length/2)- ((Mrstrengh1minimumforECC-Mostrengh1minimumforECC)/(Rvstrengh1minimumforECC))
            EcriticalforECC = (wall_length/2)- ((MrcriticalforECC-MocriticalforECC)/(RvcriticalforECC))
            #Eservice = (wall_length/2)- ((Mrservice-Moservice)/(Rvserviceforbearing))


            AllowableEccentricity1 = AllowableEccentricity*wall_length
  


           
            Mostrengh1maximum = (Pretainedfill *1.5* (wall_height+h) / 3) + (((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))* (wall_height+h) / 2) + (Pliveload *1.75* (wall_height+h) / 2)
            Mostrengh1minimum = (Pretainedfill *0.9* (wall_height+h) / 3) + (((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9))* (wall_height+h) / 2) + (Pliveload *1.75* (wall_height+h) / 2)
            Moservice = (Pretainedfill * (wall_height+h) / 3) + (((Pdeadload1)+(Pdeadload2)+(Pdeadload3)) * (wall_height+h) / 2) + (Pliveload * (wall_height+h) / 2)
            Mocritical = max(Mostrengh1maximum,Mostrengh1minimum)

 

            Mrstrengh1maximum = (M1*1.35) + ((Mds1*1.35)+(Mds2*1.5)+(Mds3*1.5)) + (Mls1*1.75) + (Mss1*1.25) + (Mslp1*1.35) + (((Pretainedfill*1.5) + ((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5)) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)
            Mrstrengh1minimum = M1 + ((Mds1*1)+(Mds2*0.65)+(Mds3*0.75)) + (Mls1*1.75) + (Mss1*0.9) + Mslp1 + (((Pretainedfill*0.9) + ((Pdeadload1*0.9)+(Pdeadload2*0.9)+(Pdeadload3*0.9))+ (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)
            Mrservice = M1 + ((Mds1)+(Mds2)+(Mds3))  + Mls1 + Mss1 + Mslp1 + ((Pretainedfill +((Pdeadload1*1)+(Pdeadload2*1)+(Pdeadload3*1)) + Pliveload)*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)
            Mrcritical = (M1*1) + ((Mds1*1)+(Mds2*0.65)+(Mds3*0.75))  + (Mls1*1.75) + (Mss1*0.9) + (Mslp1*1) + (((Pretainedfill*1.5) + ((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5)) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter)))*wall_length)



            Rvstrengh1maximumforbearing = (V1*1.35) + ((Vds1*1.35)+(Vds2*1.5)+(Vds3*1.5)) + (Vls1*1.75) + (Vss1*1.25) + (Vslp1*1.35) + (((Pretainedfill*1.5) + ((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5))  + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            Rvstrengh1minimumforbearing = V1 + ((Vds1*1)+(Vds2*0.65)+(Vds3*0.75))  + (Vls1*1.75) + (Vss1*0.9) + Vslp1 + (((Pretainedfill*0.9) + ((Pdeadload1*0.9)+(Pdeadload2*1.5)+(Pdeadload3*0.9))  + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
            Rvcriticalforbearing = V1 + ((Vds1*1)+(Vds2*0.65)+(Vds3*0.75))  + (Vls1*1.75) + (Vss1*0.9) + Vslp1 + (((Pretainedfill*1.5) + ((Pdeadload1*1.5)+(Pdeadload2*1.5)+(Pdeadload3*1.5)) + (Pliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))    
            Rvserviceforbearing = V1 + ((Vds1)+(Vds2)+(Vds3))  + (Vls1*1) + Vss1 + Vslp1 + ((Pretainedfill +((Pdeadload1*1)+(Pdeadload2*1)+(Pdeadload3*1))  + Pliveload)*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))


            Emax = (wall_length/2)- ((Mrstrengh1maximum-Mostrengh1maximum)/(Rvstrengh1maximumforbearing))
            Emin = (wall_length/2)- ((Mrstrengh1minimum-Mostrengh1minimum)/(Rvstrengh1minimumforbearing))
            Ecritical = (wall_length/2)- ((Mrcritical-Mocritical)/(Rvcriticalforbearing))
            Eservice = (wall_length/2)- ((Mrservice-Moservice)/(Rvserviceforbearing))


            # Bearing Pressure
            qrstrengh1maximum = Rvstrengh1maximumforbearing/(wall_length-(2*Emax))
            qrstrengh1minimum = Rvstrengh1minimumforbearing/(wall_length-(2*Emin))
            qrcritical = Rvcriticalforbearing/(wall_length-(2*Ecritical))
            qrservice = Rvserviceforbearing/(wall_length-(2*Eservice))

            

    

            #### Bearing capcity Check
            Nphi = math.tan(math.radians(45 + (foundation_angle / 2))) ** 2
            Nq = math.exp(math.pi * math.tan(math.radians(foundation_angle))) * Nphi
            Nc = (Nq - 1) / math.tan(math.radians(foundation_angle))
            Ny = 2 * (Nq + 1) * math.tan(math.radians(foundation_angle))

            if watertabledepth == 0:
                wtrcrc = 0.5
            elif watertabledepth >= wall_length:
                wtrcrc = 1
            else:
                # Linear interpolation for in-between values
                wtrcrc = 0.5 + (watertabledepth / wall_length) * (1 - 0.5)

            qnetstrengh1maximum = (foundation_cohesion * Nc) + (0*(foundation_soil_density*embedment_depth)*(Nq-1)) + (0.5*foundation_soil_density*(wall_length-(2*Emax ))*Ny*wtrcrc)
            qnetstrengh1minimum = (foundation_cohesion * Nc) + (0*(foundation_soil_density*embedment_depth)*(Nq-1)) + (0.5*foundation_soil_density*(wall_length-(2*Emin))*Ny*wtrcrc)
            qnetcritical = (foundation_cohesion * Nc) + (0*(foundation_soil_density*embedment_depth)*(Nq-1)) + (0.5*foundation_soil_density*(wall_length-(2*Ecritical))*Ny*wtrcrc)
            qnetservice = (foundation_cohesion * Nc) + (0*(foundation_soil_density*embedment_depth)*(Nq-1)) + (0.5*foundation_soil_density*(wall_length-(2*Eservice))*Ny*wtrcrc)

            Allowablestaticqrstrengh1maximum =(qnetstrengh1maximum /1.4)+(foundation_soil_density*embedment_depth)
            Allowablestaticqrstrengh1minimum=(qnetstrengh1minimum/1.4)+(foundation_soil_density*embedment_depth)
            Allowablestaticqrcritical=(qnetcritical/1.4)+(foundation_soil_density*embedment_depth)
            Allowablestaticqrservice=(qnetservice/1.4)+(foundation_soil_density*embedment_depth)

            CDRBearingstrengh1maximum = 0.65*Allowablestaticqrstrengh1maximum  / qrstrengh1maximum 
            CDRBearingstrengh1minimum = 0.65*Allowablestaticqrstrengh1minimum / qrstrengh1minimum
            CDRBearingcritical = 0.65*Allowablestaticqrcritical / qrcritical
            CDRBearingservice = 0.65*Allowablestaticqrservice / qrservice






##################################################################################################################
                    ##########INTERNAL STABILITY##############

             # Internal earth pressure calculations: Earth pressure behind reinforced soil block
 ######TIEBACK
            if wall_batter==0:
                kareinforced = (1- math.sin(math.radians(reinforced_angle)))/(1 + math.sin(math.radians(reinforced_angle)))
            else:
                tauu1=(1+(((math.sin(math.radians(reinforced_angle+0))*math.sin(math.radians(reinforced_angle-0)))/(math.sin(math.radians(teta+0))*math.sin(math.radians(teta-0))))**0.5))**2
                kareinforced= math.sin(math.radians(reinforced_angle+teta)) * math.sin(math.radians(reinforced_angle+teta))/(tauu1*math.sin(math.radians(teta))*math.sin(math.radians(teta))*math.sin(math.radians(teta-delta)))

            print(f"kareinforced: {kareinforced}")

            # Retrieve reinforcement layout data
            reinforcement_layout = session.get('reinforcement_layout_data', [])

            # Retrieve reinforcement properties data
            reinforcement_properties = session.get('reinforcement_data', [])

             # Reinforcement calculations
            reinforcement_results = []
            # num_layers = len(reinforcement_layout)
            
          #  sumofpulloutlengths=[0]* ((self.reinforcement_table.rowCount())+1)
            sumofpulloutlengths = [0] * (len(reinforcement_layout) + 1)
# wall_batter
# theta_f
            for i, row in enumerate(reinforcement_layout):
                depthfrombottom = float(row.get("location", 0) or 0)
                length_of_Reinforcement = float(row.get("length", 0) or 0)
                Coverage_Ratio = float(row.get("coverage_ratio", 0) or 0)
                connectioncreep = float(row.get("connection_strength_longterm", 0) or 0)
                connectionbiochem = float(row.get("connection_strength_biochem", 0) or 0)
                reinforcement_type = row.get("reinforcement_type", "").strip()

                print(f"Row {i}: depth={depthfrombottom}, length={length_of_Reinforcement}, coverage={Coverage_Ratio}, type={reinforcement_type}")
             
                # Initialize variables to store property values
                tensile_strength = 0.0
                rfid = 0.0
                rfw = 0.0
                rfcreep = 0.0
                fs = 0.0
                Pull_Out_Interface_Angle = 0.0
                Direct_Sliding_Interface_Angle = 0.0
                scale_factor = 0.0

                # Find matching reinforcement type properties
                for prop in reinforcement_properties:
                    if prop.get("name", "").strip() == reinforcement_type:
                        tensile_strength = float(prop.get("tult", 0) or 0)
                        rfid = float(prop.get("rfid", 0) or 0)
                        rfw = float(prop.get("rfw", 0) or 0)
                        rfcreep = float(prop.get("rfcr", 0) or 0)
                        fs = float(prop.get("fs", 0) or 0)
                        Pull_Out_Interface_Angle = float(prop.get("pullout_angle", 0) or 0)
                        Direct_Sliding_Interface_Angle = float(prop.get("sliding_angle", 0) or 0)
                        scale_factor = float(prop.get("scale_factor", 0) or 0)

                        print(f"Match found for reinforcement type: {reinforcement_type} with properties -> "
                            f"Tensile Strength: {tensile_strength}, RFid: {rfid}, RFw: {rfw}, RF Creep: {rfcreep}, FS: {fs}, "
                            f"Pullout Angle: {Pull_Out_Interface_Angle}, Sliding Angle: {Direct_Sliding_Interface_Angle}, Scale Factor: {scale_factor}")
                        break
                    else:
                        print(f"No match found for type: {reinforcement_type}.")



                depth_current = depthfrombottom
                num_layers = len(reinforcement_layout)

                if num_layers == 1:
                    Spacing = wall_height  # If there's only one layer, it spans the entire wall height
                elif i == 0:  # Bottommost layer
                    depth_next = float(reinforcement_layout[i + 1].get("location", 0) or 0)
                    Spacing = depth_current + ((depth_next - depth_current) / 2)
                elif i == num_layers - 1:  # Topmost layer
                    depth_prev = float(reinforcement_layout[i - 1].get("location", 0) or 0)
                    Spacing = ((wall_height - depth_current) + ((depth_current - depth_prev) / 2))
                else:  # Intermediate layers
                    depth_prev = float(reinforcement_layout[i - 1].get("location", 0) or 0)
                    depth_next = float(reinforcement_layout[i + 1].get("location", 0) or 0)
                    Spacing = ((depth_current - depth_prev) / 2) + ((depth_next - depth_current) / 2)

                print(f"Calculated Spacing for layer at depth {depth_current}: {Spacing}")
                
                if top_slope < 0:
                    # Display error message in GUI
                    # show_error_message("Wall batter cannot be less than 0.")
                    Ilengthofdlll = None
                elif top_slope == 0:
                    Ilengthofdlll=length_of_Reinforcement       
                elif top_slope > 0:
                    if Xs >= 2 * wall_height:
                        Ilengthofdlll=0 
                    else:  # This implicitly means Xs < 2 * wall_height
                        beta = Inclination
                        if Xs > length_of_Reinforcement:
                            Ilengthofdlll=0
                        else:
                            Ilengthofdlll= length_of_Reinforcement-Xs 
                else:
                    Ilengthofdlll = None    
              
                # Self-weight of the reinforced soil wall 
                IV1 =  (wall_height-depthfrombottom) * reinforced_soil_density * length_of_Reinforcement

            
                # Vertical Load due to Dead Load 
                IVds1 = (dead_load1 )*Ilengthofdlll
                IVds2 = ( dead_load2 )*Ilengthofdlll
                IVds3 = ( dead_load3)*Ilengthofdlll


                # Vertical Load due to Live Load 

                IVls1 = (live_load1 + live_load2 + live_load3)*Ilengthofdlll


                # Vertical Load due to Sloping Surcharge
                
                if top_slope < 0:
                    IVslp1 = None
                elif top_slope == 0:
                    IVslp1 = 0
                elif top_slope > 0:
                    if Xs > wall_length:
                        IVslp1 = 0.5 * reinforced_soil_density * wall_length * h
                    else:  # top_slope > 0 and Xs < wall_length
                        IVslp1 = (0.5 * reinforced_soil_density * Xs * top_rise) + (reinforced_soil_density * lengthofdlll * top_rise)
                else:
                    IVslp1 = None



                # Vertical Load due to Strip Load 
                IVss1 = ((vertical_strip_load1*strip_load1_width) + (vertical_strip_load2*strip_load2_width) + (vertical_strip_load3*strip_load3_width))


                
              
                # Earth pressure due to Retained fill
                IPretainedfill = 0.5* karetained * retained_soil_density * (wall_height+h-depthfrombottom) * (wall_height+h-depthfrombottom)*math.cos(math.radians(delta-wall_batter)) 

                if Xs > 2*wall_height:
                    dead_load11 = 0
                    dead_load22 = 0
                    dead_load33 = 0
                    live_load11 = 0
                    live_load22 = 0
                    live_load33 = 0
                else:
                    dead_load11 = dead_load1
                    dead_load22 = dead_load2
                    dead_load33 = dead_load3
                    live_load11 = live_load1
                    live_load22 = live_load2
                    live_load33 = live_load3                

            # Earth pressure due to Dead load
                IPdeadload1 = karetained * (dead_load11 ) * (wall_height+h-depthfrombottom) *math.cos(math.radians(delta-wall_batter)) 
                IPdeadload2 = karetained * (dead_load22 ) * (wall_height+h-depthfrombottom) *math.cos(math.radians(delta-wall_batter)) 
                IPdeadload3 = karetained * ( dead_load33) * (wall_height+h-depthfrombottom) *math.cos(math.radians(delta-wall_batter)) 


            # Earth pressure due to Live load       
                IPliveload = karetained * (live_load1 + live_load2 + live_load3) * (wall_height+h-depthfrombottom)*math.cos(math.radians(delta-wall_batter)) 

                # Total Horizontal load 

                IRhstrengh1maximum = (IPretainedfill*1.5) + ((IPdeadload1*1.5)+(IPdeadload2*1.5)+(IPdeadload3*1.5)) + (IPliveload*1.75)
                IRhstrengh1minimum = (IPretainedfill*0.9) + ((IPdeadload1*0.9)+(IPdeadload2*0.9)+(IPdeadload3*0.9)) + (IPliveload*1.75)
                IRhcritical = max(IRhstrengh1maximum,IRhstrengh1minimum) 

                
                # Total vertical load
                IRvstrengh1maximum = (IV1*1.35) + ((IVds1*1.35)+(IVds2*1.5)+(IVds3*1.5)) + (IVls1*0) + (IVss1*1.25) + (IVslp1*1.35) + (((IPretainedfill*1.5) + (((IPdeadload1*1.5)+(IPdeadload2*1.5)+(IPdeadload3*1.5))) + (IPliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
                IRvstrengh1minimum = IV1 + ((IVds1*1)+(IVds2*0.65)+(IVds3*0.75)) + (IVls1*0) + (IVss1*0.9) + IVslp1 + (((IPretainedfill*0.9) + (((IPdeadload1*0.9)+(IPdeadload2*0.9)+(IPdeadload3*0.9))) + (IPliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
                #Rvservice = V1 + Vds1 + (Vls1*0) + Vss1 + Vslp1 + ((Pretainedfill + Pdeadload + Pliveload)*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))
                IRvcritical = IV1 + ((IVds1*1)+(IVds2*0.65)+(IVds3*0.75)) + (IVls1*0) + (IVss1*0.9) + IVslp1 + (((IPretainedfill*1.5) + ((IPdeadload1*1.5)+(IPdeadload2*1.5)+(IPdeadload3*1.5)) + (IPliveload*1.75))*(math.sin(math.radians(delta-wall_batter)) /math.cos(math.radians(delta-wall_batter))))


                            #### Sliding Check
                ISlidingresistancereinforcedfillstrengh1maximum = (IRvstrengh1maximum * math.tan(math.radians(Direct_Sliding_Interface_Angle)) * Coverage_Ratio)+(IRvstrengh1maximum * math.tan(math.radians(reinforced_angle)) * (1-Coverage_Ratio))
                ISlidingresistancereinforcedfillstrengh1minimum = (IRvstrengh1minimum * math.tan(math.radians(Direct_Sliding_Interface_Angle))* Coverage_Ratio) + (IRvstrengh1minimum * math.tan(math.radians(reinforced_angle))* (1-Coverage_Ratio)) 
                ISlidingresistancereinforcedfillcritical = (IRvcritical * math.tan(math.radians(Direct_Sliding_Interface_Angle))* Coverage_Ratio ) + (IRvcritical * math.tan(math.radians(reinforced_angle))* (1-Coverage_Ratio))
                            #Slidingresistancereinforcedfillservice= (Rvservice * math.tan(math.radians(reinforced_angle)) /1.0)+( reinforced_cohesion* wall_length/1.6)
                            
                ISlidingresistancefoundationstrengh1maximum = (IRvstrengh1maximum * math.tan(math.radians(Direct_Sliding_Interface_Angle)) * Coverage_Ratio)+(IRvstrengh1maximum * math.tan(math.radians(reinforced_angle)) * (1-Coverage_Ratio))
                ISlidingresistancefoundationstrengh1minimum = (IRvstrengh1minimum * math.tan(math.radians(Direct_Sliding_Interface_Angle))* Coverage_Ratio) + (IRvstrengh1minimum * math.tan(math.radians(reinforced_angle))* (1-Coverage_Ratio)) 
                ISlidingresistancefoundationcritical = (IRvcritical * math.tan(math.radians(Direct_Sliding_Interface_Angle))* Coverage_Ratio ) + (IRvcritical * math.tan(math.radians(reinforced_angle))* (1-Coverage_Ratio))
                            #Slidingresistancefoundationservice = (Rvservice * math.tan(math.radians(foundation_angle)) /1.0)+( foundation_cohesion* wall_length/1.6)
                            
                ISlidingresistancestrengh1maximum = min(ISlidingresistancereinforcedfillstrengh1maximum, ISlidingresistancefoundationstrengh1maximum)
                ISlidingresistancestrengh1minimum = min(Slidingresistancereinforcedfillstrengh1minimum, ISlidingresistancefoundationstrengh1minimum)
                ISlidingresistancecritical = min(ISlidingresistancereinforcedfillcritical, ISlidingresistancefoundationcritical)
                            #Slidingresistanceservice = min(Slidingresistancereinforcedfillservice, Slidingresistancefoundationservice)

                            #sliding resistancereinforcement if first reinforcmeent at 0 level add equations

                CDRInternalslidingstrengh1maximum = ISlidingresistancestrengh1maximum * 1/ (IRhstrengh1maximum)
                CDRInternalslidingstrengh1minimum = ISlidingresistancestrengh1minimum * 1 / (IRhstrengh1minimum)
                CDRInternalslidingcritical = ISlidingresistancecritical * 1 / (IRhcritical)
                            #CDRslidingservice = Slidingresistanceservice * 1 / (Rhservice)
                print(f"CDRInternalslidingcritical")

                Sequivalent=0.5*0.7*wall_height*math.tan(math.radians(top_rise))

                if top_slope < 0:
                    Iqrstrengh1maximum = None
                    Iqrstrengh1minimum = None
                    Iqrcritical = None
                elif top_slope == 0:
                    Iqrstrengh1maximum = ((wall_height - depthfrombottom)*1.35*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*1.5) +((live_load1 + live_load2 + live_load3)*1.35) + ((Sequivalent*1.35)*reinforced_soil_density)
                    Iqrstrengh1minimum = ((wall_height - depthfrombottom)*1*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*0.75) +((live_load1 + live_load2 + live_load3)*1) + ((Sequivalent*1)*reinforced_soil_density)
                    Iqrcritical = max(Iqrstrengh1maximum,Iqrstrengh1minimum)
                elif top_slope > 0:
                    if Xs > ((wall_height * math.tan(math.radians(90-theta_f)))-(wall_height * math.tan(math.radians(wall_batter)))):
                        Iqrstrengh1maximum = ((wall_height - depthfrombottom)*1.35*reinforced_soil_density) + ((Sequivalent*1.35)*reinforced_soil_density)
                        Iqrstrengh1minimum = ((wall_height - depthfrombottom)*1*reinforced_soil_density) + ((Sequivalent*1)*reinforced_soil_density)
                        Iqrcritical = max(Iqrstrengh1maximum,Iqrstrengh1minimum)
                    else:  # top_slope > 0 and Xs < wall_length
                        Iqrstrengh1maximum = ((wall_height - depthfrombottom)*1.35*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*1.5) +((live_load1 + live_load2 + live_load3)*1.35) + ((Sequivalent*1.35)*reinforced_soil_density)
                        Iqrstrengh1minimum = ((wall_height - depthfrombottom)*1*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*0.75) +((live_load1 + live_load2 + live_load3)*1) + ((Sequivalent*1)*reinforced_soil_density)
                        Iqrcritical = max(Iqrstrengh1maximum,Iqrstrengh1minimum)
                else:
                    Iqrstrengh1maximum = None
                    Iqrstrengh1minimum = None
                    Iqrcritical = None
          
                  


                             

                if (wall_height - depthfrombottom) < (2 * strip_load1_distance - strip_load1_width):
                    Dj1 = strip_load1_width + (wall_height - depthfrombottom)
                else:
                    Dj1 = strip_load1_distance + ((strip_load1_width + (wall_height - depthfrombottom)) / 2)
               
                if (wall_height - depthfrombottom) < (2 * strip_load2_distance - strip_load2_width):
                    Dj2 = strip_load2_width + (wall_height - depthfrombottom)
                else:
                    Dj2 = strip_load2_distance + ((strip_load2_width + (wall_height - depthfrombottom)) / 2)
               
                if (wall_height - depthfrombottom) < (2 * strip_load3_distance - strip_load3_width):
                    Dj3 = strip_load3_width + (wall_height - depthfrombottom)
                else:
                    Dj3 = strip_load3_distance + ((strip_load3_width + (wall_height - depthfrombottom)) / 2)




              
                Tensileloadstrengh1maximum = (kareinforced * Iqrstrengh1maximum * Spacing) + (kareinforced * Spacing * 1.25*vertical_strip_load1/Dj1) + (kareinforced * Spacing * 1.25*vertical_strip_load2/Dj2) + (kareinforced * Spacing * 1.25*vertical_strip_load3/Dj3)
                Tensileloadstrengh1minimum= (kareinforced * Iqrstrengh1minimum * Spacing) + (kareinforced * Spacing * 0.9*vertical_strip_load1/Dj1) + (kareinforced * Spacing * 0.9*vertical_strip_load2/Dj2) + (kareinforced * Spacing * 0.9*vertical_strip_load3/Dj3)
                Tensileloadcritical = (kareinforced * Iqrcritical * Spacing) + (kareinforced * Spacing * 1.25*vertical_strip_load1/Dj1) + (kareinforced * Spacing * 1.25*vertical_strip_load2/Dj2) + (kareinforced * Spacing * 1.25*vertical_strip_load3/Dj3)





                ##### RUPTURE CHECK
                CDRrupturestrengh1maximum = 0.9*Coverage_Ratio*tensile_strength/(rfid * rfw * rfcreep *fs* Tensileloadstrengh1maximum*1)
                CDRrupturestrengh1minimum = 0.9*Coverage_Ratio*tensile_strength/(rfid * rfw * rfcreep *fs* Tensileloadstrengh1minimum*1)
                CDRrupturecritical = 0.9*Coverage_Ratio*tensile_strength/(rfid * rfw * rfcreep *fs* Tensileloadcritical*1)


                CDRconnectionstrengh1maximum = 0.9*Coverage_Ratio*tensile_strength*connectioncreep/(connectionbiochem * Tensileloadstrengh1maximum)
                CDRconnectionstrengh1minimum = 0.9*Coverage_Ratio*tensile_strength*connectioncreep/(connectionbiochem * Tensileloadstrengh1minimum)
                CDRconnectioncritical = 0.9*Coverage_Ratio*tensile_strength*connectioncreep/(connectionbiochem * Tensileloadcritical)

                if i == len(reinforcement_layout) - 1:
                    CDRruptureImpact = 1.2 * Coverage_Ratio * tensile_strength / (rfid * rfw * rfcreep * fs * (Tensileloadstrengh1maximum + ruptureimpact_load1) * 1)
                elif i == len(reinforcement_layout) - 2:
                    CDRruptureImpact = 1.2 * Coverage_Ratio * tensile_strength / (rfid * rfw * rfcreep * fs * (Tensileloadstrengh1maximum + ruptureimpact_load2) * 1)
                else:
                    CDRruptureImpact = "Not Applicable"  # Set as a string



                # Pulloutlength = Length_of_Reinforcement - (depthfrombottom * math.tan(math.radians((45)-(reinforced_angle/2))))
               
                
                batterlength = depthfrombottom * math.tan(math.radians(wall_batter))
                activelength = (depthfrombottom * math.tan(math.radians(90-theta_f))) - batterlength 
                Pulloutlength = length_of_Reinforcement - activelength



                sumofpulloutlengths[i+1]=Pulloutlength+sumofpulloutlengths[i]
                totalsumofpulloutlengths=sumofpulloutlengths[i+1]

                Ihp = (activelength + (0.5*Pulloutlength))*math.tan(math.radians(top_slope))

                if top_slope < 0:
                    Ihp = None
                elif top_slope == 0:
                    Ihp = 0
                elif top_slope > 0:
                    if Ihp > top_rise:
                        Ihp=top_rise
                    else:  # top_slope > 0 and Xs < wall_length
                        Ihp = (activelength + (0.5*Pulloutlength))*math.tan(math.radians(top_slope))
                else:
                    Ihp = None
                
                if Ilengthofdlll<Pulloutlength:
                    VERTSTRESSstrengh1maximum=((dead_load1 + dead_load2 + dead_load3)*1*Ilengthofdlll/Pulloutlength)+((wall_height+Ihp-depthfrombottom)*1.0*reinforced_soil_density)
                    VERTSTRESSstrengh1minimum = ((dead_load1 + dead_load2 + dead_load3)*1*Ilengthofdlll/Pulloutlength)+((wall_height+Ihp-depthfrombottom)*reinforced_soil_density)
                    VERTSTRESScritical = min(VERTSTRESSstrengh1maximum,VERTSTRESSstrengh1minimum)
                else:
                    VERTSTRESSstrengh1maximum=((dead_load1 + dead_load2 + dead_load3)*1*1)+((wall_height+Ihp-depthfrombottom)*1*reinforced_soil_density)
                    VERTSTRESSstrengh1minimum = ((dead_load1 + dead_load2 + dead_load3)*1*1)+((wall_height+Ihp-depthfrombottom)*reinforced_soil_density)
                    VERTSTRESScritical = min(VERTSTRESSstrengh1maximum,VERTSTRESSstrengh1minimum)

                #nominalvertstress

                
                pullout_capacitystrengh1maximum = 2*scale_factor*Pulloutlength*Coverage_Ratio*VERTSTRESSstrengh1maximum*math.tan(math.radians(Pull_Out_Interface_Angle))
                pullout_capacitystrengh1minimum = 2*scale_factor*Pulloutlength*Coverage_Ratio*VERTSTRESSstrengh1minimum*math.tan(math.radians(Pull_Out_Interface_Angle))
                pullout_capacitycritical = 2*scale_factor*Pulloutlength*Coverage_Ratio*VERTSTRESScritical*math.tan(math.radians(Pull_Out_Interface_Angle))
                

                ##### PULLOUT CHECK
                CDRpulloutstrengh1maximum = 0.9*pullout_capacitystrengh1maximum/(Tensileloadstrengh1maximum)
                CDRpulloutstrengh1minimum = 0.9*pullout_capacitystrengh1minimum/(Tensileloadstrengh1minimum)
                CDRpulloutcritical = 0.9*pullout_capacitycritical/(Tensileloadcritical)
          
                if i == len(reinforcement_layout) - 1:
                    impact_vertical_stress = ((dead_load1 + dead_load2 + dead_load3) * 1 * 1) + ((wall_height + Ihp - depthfrombottom) * 1 * reinforced_soil_density) + (live_load1 + live_load2 + live_load3)
                    impact_pullout_capacity = 2 * scale_factor * length_of_Reinforcement * Coverage_Ratio * impact_vertical_stress * math.tan(math.radians(Pull_Out_Interface_Angle))
                    CDRPulloutImpact = 1.0 * impact_pullout_capacity / (Tensileloadstrengh1maximum + pulloutimpact_load1)
                elif i == len(reinforcement_layout) - 2:
                    impact_vertical_stress = ((dead_load1 + dead_load2 + dead_load3) * 1 * 1) + ((wall_height + Ihp - depthfrombottom) * 1 * reinforced_soil_density) + (live_load1 + live_load2 + live_load3)
                    impact_pullout_capacity = 2 * scale_factor * length_of_Reinforcement * Coverage_Ratio * impact_vertical_stress * math.tan(math.radians(Pull_Out_Interface_Angle))
                    CDRPulloutImpact = 1.0 * impact_pullout_capacity / (Tensileloadstrengh1maximum + pulloutimpact_load2)
                else:
                    CDRPulloutImpact = "Not Applicable"  # Set as a string

                        



              #  print(f"depthfrombottom: {depthfrombottom}")
               
                print(f"spacing: {Spacing}")
                print(f"depth_current: {depth_current}")

                print(f"Tensileloadstrengh1maximum: {Tensileloadstrengh1maximum}")
                print(f"Tensileloadstrengh1minimum: {Tensileloadstrengh1minimum}")
                print(f"Pulloutlength: {Pulloutlength}")

                print(f"pullout_capacitystrengh1maximum: {pullout_capacitystrengh1maximum}")
                print(f"pullout_capacitystrengh1minimum: {pullout_capacitystrengh1minimum }")

            
            
#                reinforcement_results.append((depthfrombottom, IRhcritical, IRvcritical,  ISlidingresistancecritical, CDRInternalslidingcritical, Tensileloadcritical, pullout_capacitycritical,CDRrupturecritical, CDRpulloutcritical, CDRruptureImpact, CDRPulloutImpact, CDRconnectioncritical))

                reinforcement_results.append({
                    "Depth from Bottom": depthfrombottom,
                    "IRh Critical": IRhcritical,
                    "IRv Critical": IRvcritical,
                    "Internal Sliding Resistance Critical": ISlidingresistancecritical,
                    "CDR Internal Sliding Critical": CDRInternalslidingcritical,
                    "Tensile Load Critical": Tensileloadcritical,
                    "Pullout Capacity Critical": pullout_capacitycritical,
                    "CDR Rupture Critical": CDRrupturecritical,
                    "CDR Pullout Critical": CDRpulloutcritical,
                    "CDR Rupture Impact": CDRruptureImpact,
                    "CDR Pullout Impact": CDRPulloutImpact,
                    "CDR Connection Critical": CDRconnectioncritical
                })


             ###################################################################################
            # EARTHQUAKE EXTERNAL STBILITY

            if top_slope < 0:
                EQVslp1 = None
            elif top_slope == 0:
                EQVslp1 = 0
            elif top_slope > 0:
                if Xs > length_of_Reinforcement:
                    EQVslp1 = 0.5 * reinforced_soil_density * wall_length * h 
                else:  # top_slope > 0 and Xs < wall_length
                    EQVslp1 = (0.5 * reinforced_soil_density * Xs * top_rise*2*Xs/3) + (reinforced_soil_density * lengthofdlll * top_rise*(Xs+(0.5*lengthofdlll)))
            else:
                EQVslp1 = None

            # Self-weight of the reinforced soil wall 
            EQV1 =  wall_height * reinforced_soil_density * wall_length

            # Vertical Load due to Sloping Surcharge
            
            EQH2 = wall_height + (math.tan(math.radians(top_slope)*0.5*wall_height))/(1-(0.5*math.tan(math.radians(top_slope))))
            if top_slope < 0:
                EQH2 = None
            elif top_slope == 0:
                EQH2 = wall_height
            elif top_slope > 0:
                if EQH2 > wall_height+top_rise:
                    EQH2 = wall_height + top_rise
                else:  # top_slope > 0 and Xs < wall_length
                    EQH2 = wall_height + (math.tan(math.radians(top_slope)*0.5*wall_height))/(1-(0.5*math.tan(math.radians(top_slope))))
            else:
                EQH2 = None




            # Vertical Load due to Dead Load 
            EQVds1 = (dead_load1 )*lengthofdlll 
            EQVds2 = (dead_load2 )*lengthofdlll 
            EQVds3 = (dead_load3)*lengthofdlll 


            # Vertical Load due to Live Load 

            EQVls1 = (live_load1 + live_load2 + live_load3)*lengthofdlll 


            # Vertical Load due to Strip Load 
            EQVss1 = ((vertical_strip_load1*strip_load1_width) + (vertical_strip_load2*strip_load2_width) + (vertical_strip_load3*strip_load3_width))




            # External earth pressure calculations: Earth pressure behind reinforced soil block
           # karetained = (1- math.sin(math.radians(retained_angle)))/(1 + math.sin(math.radians(retained_angle)))

            deltaKaretained=EQKaretained-karetained
           # static Earth pressure due to Retained fill
            EQseismicPretainedfill = 0.5* EQKaretained * retained_soil_density * (wall_height+h) * (wall_height+h) * math.cos(math.radians(deltaseis-wall_batter))
            if Xs > 2*wall_height:
                    dead_load11 = 0
                    dead_load22 = 0
                    dead_load33 = 0
                    live_load11 = 0
                    live_load22 = 0
                    live_load33 = 0
            else:
                    dead_load11 = dead_load1
                    dead_load22 = dead_load2
                    dead_load33 = dead_load3
                    live_load11 = live_load1
                    live_load22 = live_load2
                    live_load33 = live_load3  

           # staticEarth pressure due to Dead load
            EQseismicPdeadload = EQKaretained * (dead_load11 + dead_load22 + dead_load33) * (wall_height+h) * math.cos(math.radians(deltaseis-wall_batter))


            # staticEarth pressure due to Live load       
            EQseismicPliveload = EQKaretained * (live_load1 + live_load2 + live_load3) * (wall_height+h)





            # Total Horizontal Load Calculation
            EQseismicRh1 = EQseismicPretainedfill + EQseismicPdeadload + (EQseismicPliveload * 0.5)


            # Check if 'use_direct_kh' is enabled from the session
            use_direct_kh = session.get('externalloads_data', {}).get('use_direct_kh', False)

            if use_direct_kh:
                kh_value = session.get('externalloads_data', {}).get('seismic_force', '')
                if kh_value:
                    try:
                        EQseismicRh = float(kh_value)
                        print(f"Using kh value: {EQseismicRh}")
                    except ValueError:
                        return jsonify({"error": "Invalid seismic earth pressure value."}), 400
                else:
                    return jsonify({"error": "No seismic earth pressure value entered."}), 400
            else:
                EQseismicRh = EQseismicRh1  # Assuming EQseismicRh1 is defined elsewhere
                print("Using default EQseismicRh value.")

           #if kh_value is not None:
           #     print(f"Using kh value: {kh_value}")
            #    EQseismicRh = kh_value
          #  else:
            #    EQseismicRh=EQseismicRh1

            Reinforcedfillinertia =  0.5*Wallacceleration*reinforced_soil_density * wall_height * wall_length
            Slopinginertia = 0.125*Wallacceleration * EQH2 * EQH2*reinforced_soil_density* math.tan(math.radians(betaseis))
            #Seismicearthpressuredretainedfill= 0.5*deltaKaretained*retained_soil_density * EQH2 * EQH2 * math.cos(math.radians(delta-wall_batter))
            # Seismicearthpressuredeadloadonretainedfill= deltaKaretained*wall_height*(dead_load1 + dead_load2 + dead_load3)

            EQRh = Reinforcedfillinertia+EQseismicRh+Slopinginertia

            # Total vertical load
            EQRv = EQV1 + EQVds1+EQVds2+EQVds3+ (EQVls1*0) + EQVss1 + EQVslp1 + ((EQseismicRh-(EQseismicPliveload*0.0))*math.sin(math.radians(deltaseis-wall_batter))/math.cos(math.radians(deltaseis-wall_batter)))

            #### Sliding Check
            EQSlidingresistancereinforcedfill = (EQRv * math.tan(math.radians(reinforced_angle)) /1.0)+( reinforced_cohesion* wall_length/1)
            
            
            EQSlidingresistancefoundation = (EQRv * math.tan(math.radians(foundation_angle)) /1.0)+( foundation_cohesion* wall_length/1)
            
            
            EQSlidingresistance = min(EQSlidingresistancereinforcedfill, EQSlidingresistancefoundation)


            EQCDRsliding = EQSlidingresistance / (EQRh)



            #### Overturning Check


                       
            if top_slope < 0:
                EQMslp1 = None
            elif top_slope == 0:
                EQMslp1 = 0
            elif top_slope > 0:
                if Xs > wall_length:
                    EQMslp1 = 0.5 * reinforced_soil_density * wall_length * h *2*wall_length/3
                else:  # top_slope > 0 and Xs < wall_length
                    EQMslp1 = (0.5 * reinforced_soil_density * Xs * top_rise*2*Xs/3) + (reinforced_soil_density * Ilengthofdlll * top_rise*(Xs+(0.5*Ilengthofdlll)))
            else:
                EQMslp1 = None

                 
             # Self-weight of the reinforced soil wall 
            EQM1 =  wall_height * reinforced_soil_density * wall_length * (wall_length / 2)

            
            # Vertical Load due to Dead Load 
            EQMds1 = (dead_load1 + dead_load2 + dead_load3)*lengthofdlll * (Xs+(lengthofdlll / 2))

            # Vertical Load due to Live Load 

            EQMls1 = (live_load1 + live_load2 + live_load3)*lengthofdlll * (Xs+(lengthofdlll / 2))
   

            # Vertical Load due to Strip Load 
            EQMss1 = ((vertical_strip_load1*strip_load1_width*strip_load1_distance) + (vertical_strip_load2*strip_load2_width*strip_load2_distance) + (vertical_strip_load3*strip_load3_width*strip_load3_distance))
    

            # Total vertical load
            EQMrecmax = (EQM1*1.35) + ((EQVds1*1.35* (Xs+(lengthofdlll / 2)))+(EQVds2*1.5* (Xs+(lengthofdlll / 2)))+(EQVds3*1.5* (Xs+(lengthofdlll / 2)))) + (EQMls1*0) + (EQMss1*1.25) + (EQMslp1*1.35)  + (((EQseismicPretainedfill*wall_length) + (EQseismicPdeadload*wall_length) + (0*EQseismicPliveload*wall_length))*(math.sin(math.radians(deltaseis-wall_batter)) /math.cos(math.radians(deltaseis-wall_batter))))
            EQMrecmin = EQM1 + ((EQVds1*1* (Xs+(lengthofdlll / 2)))+(EQVds2*0.75* (Xs+(lengthofdlll / 2)))+(EQVds3*0.65* (Xs+(lengthofdlll / 2)))) + (EQMls1*0) + (EQMss1*0.9) + EQMslp1  + (((EQseismicPretainedfill*wall_length) + (EQseismicPdeadload*wall_length) + (0*EQseismicPliveload*wall_length))*(math.sin(math.radians(deltaseis-wall_batter)) /math.cos(math.radians(deltaseis-wall_batter))))
            EQMrec = min(EQMrecmax, EQMrecmin)     
                      
            #### Eccentricity Check

            EQMomax = (EQseismicPretainedfill * (wall_height+h) / 2) + (EQseismicPdeadload * (wall_height+h) / 2) + (0.5*EQseismicPliveload * (wall_height+h) / 2) + (Reinforcedfillinertia*1.35* wall_height / 2) + (Slopinginertia*1.35* (wall_height+(h/3)))
            EQMomin =(EQseismicPretainedfill * (wall_height+h) / 2) + (EQseismicPdeadload * (wall_height+h) / 2) + (0.5*EQseismicPliveload * (wall_height+h) / 2) + (Reinforcedfillinertia* wall_height / 2) + (Slopinginertia* (wall_height+(h/3)))
            EQMo    = max(EQMomax, EQMomin)    

            EQRvec = EQV1 + ((EQVds1*1)+(EQVds2*0.75)+(EQVds3*0.65))+ (EQVls1*0) + (EQVss1*1.25) + EQVslp1 + ((EQseismicRh-(EQseismicPliveload*0.5))*math.sin(math.radians(deltaseis-wall_batter))/math.cos(math.radians(deltaseis-wall_batter)))


            
            EQEccentricity = (wall_length/2)- ((EQMrec-EQMo)/(EQRvec))

            AllowableEccentricityEQ = AllowableEccentricityseismic1*wall_length


            #### Bearing capcity Check
            EQMobearing = (EQseismicPretainedfill * (wall_height+h) / 2) + (EQseismicPdeadload * (wall_height+h) / 2) + (0.5*EQseismicPliveload * (wall_height+h) / 2) + (Reinforcedfillinertia*1.35* wall_height / 2) + (Slopinginertia*1.35* (wall_height+(h/3)))
            EQMrbearing = (EQM1*1.35) + ((EQVds1*1.35* (Xs+(lengthofdlll / 2)))+(EQVds2*1.5* (Xs+(lengthofdlll / 2)))+(EQVds3*1.5)* (Xs+(lengthofdlll / 2))) + (EQMls1*0.5) + (EQMss1*1.25) + (EQMslp1*1.35) + (((EQseismicPretainedfill*wall_length) + (EQseismicPdeadload*wall_length) + (0*EQseismicPliveload*wall_length))*(math.sin(math.radians(deltaseis-wall_batter)) /math.cos(math.radians(deltaseis-wall_batter))))
            EQRvbearing = (EQV1*1.35) + ((EQVds1*1.35)+(EQVds2*1.5)+(EQVds3*1.5))+ (EQVls1*0.5) + (EQVss1*1.25) + (EQVslp1*1.35) + ((EQseismicPretainedfill + EQseismicPdeadload)*math.sin(math.radians(deltaseis-wall_batter))/math.cos(math.radians(deltaseis-wall_batter)))
            
            EQbearingecc = (wall_length/2)- ((EQMrbearing-EQMobearing)/(EQRvbearing))
           
            print("wall_length:", wall_length)
            print("EQMrbearing:", EQMrbearing)
            print("EQMobearing:", EQMobearing)
            print("EQRvbearing:", EQRvbearing)
 
            print("EQbearingecc:", EQbearingecc)


           #  EQbearingecc = (wall_length/2) - ((EQMrbearing-EQMobearing)/(EQRvbearing))

            # Bearing Pressure
            EQqr = EQRvbearing/(wall_length-(2*EQbearingecc))
      
            EQqnet = (foundation_cohesion * Nc) + (0*(foundation_soil_density*embedment_depth)*(Nq-1)) + (0.5*foundation_soil_density*(wall_length-(2*EQEccentricity))*Ny*wtrcrc)

            AllowablestaticqrEQ=(EQqnet/1)+(foundation_soil_density*embedment_depth)


            CDRBearingEQ = AllowablestaticqrEQ / (1*EQqr)



##################################################################################################################
                    ##########INTERNAL STABILITY##############

             # Internal earth pressure calculations: Earth pressure behind reinforced soil block
 ######TIEBACK
            #EQkareinforced = (1- math.sin(math.radians(reinforced_angle)))/(1 + math.sin(math.radians(reinforced_angle)))

            if wall_batter==0:
                EQkareinforced = (1- math.sin(math.radians(reinforced_angle)))/(1 + math.sin(math.radians(reinforced_angle)))
            else:
                EQtauu1=(1+(((math.sin(math.radians(reinforced_angle+0))*math.sin(math.radians(reinforced_angle-0)))/(math.sin(math.radians(teta+0))*math.sin(math.radians(teta-0))))**0.5))**2
                EQkareinforced= math.sin(math.radians(reinforced_angle+teta)) * math.sin(math.radians(reinforced_angle+teta))/(EQtauu1*math.sin(math.radians(teta))*math.sin(math.radians(teta))*math.sin(math.radians(teta-delta)))
            
            print(f"EQkareinforced: {EQkareinforced}")
 ######COHERENTGRAVITY        
            # Assuming reinforcement_data is a list of dictionaries retrieved from the frontend
            total_reinforcements = len(reinforcement_layout)

            # Print or use the total number of reinforcements
            print(f"Total number of reinforcements: {total_reinforcements}")

            # Reinforcement calculations
            EQreinforcement_results = []



                        # Retrieve reinforcement layout data
            EQreinforcement_layout = session.get('reinforcement_layout_data', [])

            # Retrieve reinforcement properties data
            EQreinforcement_properties = session.get('reinforcement_data', [])

  
            
          #  sumofpulloutlengths=[0]* ((self.reinforcement_table.rowCount())+1)
            sumofpulloutlengths = [0] * (len(reinforcement_layout) + 1)
# wall_batter
# theta_f
            for j, row in enumerate(EQreinforcement_layout):
            
                EQdepthfrombottom = float(row.get("location", 0) or 0)
                EQLength_of_Reinforcement = float(row.get("length", 0) or 0)
                EQCoverge_Ratio = float(row.get("coverage_ratio", 0) or 0)
                
                EQconnectioncreep = float(row.get("connection_strength_longterm", 0) or 0)
                EQconnectionbiochem = float(row.get("connection_strength_biochem", 0) or 0)
                EQreinforcement_type = row.get("reinforcement_type", "").strip()


                # Initialize variables to store property values
                EQtensile_strength = 0.0
                EQrfid = 0.0
                EQrfw = 0.0
                EQrfcreep = 0.0
                EQfs = 0.0
                EQPull_Out_Interface_Angle = 0.0
                EQDirect_Sliding_Interface_Angle = 0.0
                EQscale_factor = 0.0


                for prop in reinforcement_properties:
                    if prop.get("name", "").strip() == reinforcement_type:
                        EQtensile_strength = float(prop.get("tult", 0) or 0)
                        EQrfid = float(prop.get("rfid", 0) or 0)
                        EQrfw = float(prop.get("rfw", 0) or 0)
                        EQrfcreep = float(prop.get("rfcr", 0) or 0)
                        EQfs = float(prop.get("fs", 0) or 0)
                        EQPull_Out_Interface_Angle = float(prop.get("pullout_angle", 0) or 0)
                        EQDirect_Sliding_Interface_Angle = float(prop.get("sliding_angle", 0) or 0)
                        EQscale_factor = float(prop.get("scale_factor", 0) or 0)


                EQdepth_current = EQdepthfrombottom
                EQnum_layers = len(reinforcement_layout)

                if EQnum_layers == 1:
                    EQSpacing = wall_height  # If there's only one layer, it spans the entire wall height
                elif i == 0:  # Bottommost layer
                    EQdepth_next = float(reinforcement_layout[i + 1].get("location", 0) or 0)
                    EQSpacing = EQdepth_current + ((EQdepth_next - EQdepth_current) / 2)
                elif i == num_layers - 1:  # Topmost layer
                    EQdepth_prev = float(reinforcement_layout[i - 1].get("location", 0) or 0)
                    EQSpacing = ((wall_height - EQdepth_current) + ((EQdepth_current - EQdepth_prev) / 2))
                else:  # Intermediate layers
                    EQdepth_prev = float(reinforcement_layout[i - 1].get("location", 0) or 0)
                    EQdepth_next = float(reinforcement_layout[i + 1].get("location", 0) or 0)
                    EQSpacing = ((EQdepth_current - EQdepth_prev) / 2) + ((EQdepth_next - EQdepth_current) / 2)

                print(f"Calculated EQSpacing for layer at depth {EQdepth_current}: {EQSpacing}")
                

                                
                if top_slope < 0:
                    # Display error message in GUI
                    # show_error_message("Wall batter cannot be less than 0.")
                    EQIlengthofdlll = None
                elif top_slope == 0:
                    EQIlengthofdlll=EQLength_of_Reinforcement       
                elif top_slope > 0:
                    if Xs >= 2 * wall_height:
                        EQIlengthofdlll=0 
                    else:  # This implicitly means Xs < 2 * wall_height
                        beta = Inclination
                        if Xs > EQLength_of_Reinforcement:
                            EQIlengthofdlll=0
                        else:
                            EQIlengthofdlll= EQLength_of_Reinforcement-Xs 
                else:
                    EQIlengthofdlll = None   
              
                # Self-weight of the reinforced soil wall 
                EQIV1 =  (wall_height-EQdepthfrombottom) * reinforced_soil_density * EQLength_of_Reinforcement
            
                # Vertical Load due to Dead Load 
                EQIVds1 = (dead_load1 + dead_load2 + dead_load3)*EQIlengthofdlll

                # Vertical Load due to Live Load 

                EQIVls1 = (live_load1 + live_load2 + live_load3)*EQIlengthofdlll
                
                Sequivalent=0.5*0.7*wall_height*math.tan(math.radians(beta))

                # Vertical Load due to Strip Load 
                EQIVss1 = ((vertical_strip_load1*strip_load1_width) + (vertical_strip_load2*strip_load2_width) + (vertical_strip_load3*strip_load3_width))
   
                
                # Earth pressure due to Retained fill
                EQIPretainedfill = 0.5* EQKaretained * retained_soil_density * (wall_height+h-EQdepthfrombottom) * (wall_height+h-EQdepthfrombottom) * math.cos(math.radians(deltaseis-wall_batter))


            # Earth pressure due to Dead load
                EQIPdeadload = EQKaretained * (dead_load1 + dead_load2 + dead_load3) * (wall_height+h-EQdepthfrombottom) * math.cos(math.radians(deltaseis-wall_batter))
 

            # Earth pressure due to Live load       
                EQIPliveload = EQKaretained * (live_load1 + live_load2 + live_load3) * (wall_height+h-EQdepthfrombottom) *1


# Total vertical load for eccentricity
                EQIRv = EQIV1 + EQIVds1 + (EQIVls1*0) + EQIVss1 + (reinforced_soil_density * Sequivalent) + ((EQIPretainedfill + EQIPdeadload + (0*0.5*EQIPliveload))*(math.sin(math.radians(deltaseis-wall_batter)) /math.cos(math.radians(deltaseis-wall_batter))))


            # Total Horizontal load 

                EQIseismicRh = EQIPretainedfill + EQIPdeadload+ (EQIPliveload*0.5)

                EQIReinforcedfillinertia =  0.5*Wallacceleration*reinforced_soil_density * (wall_height-EQdepthfrombottom) * length_of_Reinforcement
                EQISSlopinginertia = 0.125 * (EQH2-EQdepthfrombottom) * (EQH2-EQdepthfrombottom)*reinforced_soil_density* math.tan(math.radians(betaseis))
                EQIRh = EQIReinforcedfillinertia+EQIseismicRh+EQISSlopinginertia



            #### Internal Sliding Check
                
                EQISlidingresistance = (EQIRv * math.tan(math.radians(EQDirect_Sliding_Interface_Angle)) * EQCoverge_Ratio)+(EQIRv * math.tan(math.radians(reinforced_angle)) * (1-EQCoverge_Ratio))


                EQCDRInternalsliding = EQISlidingresistance / (1 * EQIRh)


                EQCDRsliding = EQSlidingresistance / (EQRh)

 


                if top_slope < 0:
                    EQIqr = None
                elif top_slope == 0:
                    EQIqr = ((wall_height - EQdepthfrombottom)*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)) +((live_load1 + live_load2 + live_load3)) + ((Sequivalent)*reinforced_soil_density)
                elif top_slope > 0:
                    if Xs > wall_height * math.tan(math.radians((45-(reinforced_angle/2)))):
                        EQIqr = ((wall_height - EQdepthfrombottom)*reinforced_soil_density) + ((Sequivalent)*reinforced_soil_density)
                    else:  # top_slope > 0 and Xs < wall_length
                        EQIqr = ((wall_height - EQdepthfrombottom)*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)) +((live_load1 + live_load2 + live_load3)) + ((Sequivalent)*reinforced_soil_density)
                else:
                    EQIqr = None
          
                                 
               # EQIqr = ((wall_height - EQdepthfrombottom)*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)) +((live_load1 + live_load2 + live_load3)) + ((Sequivalent)*reinforced_soil_density)
                



                if (wall_height - EQdepthfrombottom) < (2 * strip_load1_distance - strip_load1_width):
                    EQDj1 = strip_load1_width + (wall_height - EQdepthfrombottom)
                else:
                    EQDj1 = strip_load1_distance + ((strip_load1_width + (wall_height - EQdepthfrombottom)) / 2)
               
                if (wall_height - EQdepthfrombottom) < (2 * strip_load2_distance - strip_load2_width):
                    EQDj2 = strip_load2_width + (wall_height - EQdepthfrombottom)
                else:
                    EQDj2 = strip_load2_distance + ((strip_load2_width + (wall_height - EQdepthfrombottom)) / 2)
               
                if (wall_height - EQdepthfrombottom) < (2 * strip_load3_distance - strip_load3_width):
                    EQDj3 = strip_load3_width + (wall_height - EQdepthfrombottom)
                else:
                    EQDj3 = strip_load3_distance + ((strip_load3_width + (wall_height - EQdepthfrombottom)) / 2)



                EQbatterlength = EQdepthfrombottom * math.tan(math.radians(wall_batter))
                EQactivelength = (EQdepthfrombottom * math.tan(math.radians(90-theta_f))) - EQbatterlength 
                EQPulloutlength = EQLength_of_Reinforcement - EQactivelength



                if top_slope < 0:
                    slopeweightinactivewedge= None
                elif top_slope == 0:
                    slopeweightinactivewedge = 0
                elif top_slope > 0:
                    if Xs > EQactivelength:
                        slopeweightinactivewedge = 0.5 * reinforced_soil_density * EQactivelength * EQactivelength * math.tan(top_rise)
                    else:  # top_slope > 0 and Xs < wall_length
                        slopeweightinactivewedge = (0.5 * reinforced_soil_density * Xs * top_rise) + (reinforced_soil_density * (EQactivelength-Xs) * top_rise)
                else:
                    slopeweightinactivewedge = None

                #Weightofactivezone = 0.5 * wall_height * wall_height * math.tan(math.radians((45-(reinforced_angle/2))))
                Weightofactivezone = slopeweightinactivewedge + ( 0.5 * wall_height* reinforced_soil_density * (wall_height * math.tan(math.radians((90-theta_f)))-wall_height * math.tan(math.radians((wall_batter)))))
                Inertialforceofreinforcedactivemass = Wallacceleration * Weightofactivezone
              
               
                EQSequivalent=0.5*0.7*wall_height*math.tan(math.radians(top_rise))

                if top_slope < 0:
                    EQIqrstrengh1maximum = None
                    EQIqrstrengh1minimum = None
                    EQIqrcritical = None
                elif top_slope == 0:
                    EQIqrstrengh1maximum = ((wall_height - EQdepthfrombottom)*1.35*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*1.5) +((live_load1 + live_load2 + live_load3)*1.35) + ((EQSequivalent*1.35)*reinforced_soil_density)
                    EQIqrstrengh1minimum = ((wall_height - EQdepthfrombottom)*1*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*0.75) +((live_load1 + live_load2 + live_load3)*1) + ((EQSequivalent*1)*reinforced_soil_density)
                    EQIqrcritical = max(EQIqrstrengh1maximum,EQIqrstrengh1minimum)
                elif top_slope > 0:
                    if Xs > ((wall_height * math.tan(math.radians(90-theta_f)))-(wall_height * math.tan(math.radians(wall_batter)))):
                        EQIqrstrengh1maximum = ((wall_height - EQdepthfrombottom)*1.35*reinforced_soil_density) + ((EQSequivalent*1.35)*reinforced_soil_density)
                        EQIqrstrengh1minimum = ((wall_height - EQdepthfrombottom)*1*reinforced_soil_density) + ((EQSequivalent*1)*reinforced_soil_density)
                        EQIqrcritical = max(EQIqrstrengh1maximum,EQIqrstrengh1minimum)
                    else:  # top_slope > 0 and Xs < wall_length
                        EQIqrstrengh1maximum = ((wall_height - EQdepthfrombottom)*1.35*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*1.5) +((live_load1 + live_load2 + live_load3)*1.35) + ((EQSequivalent*1.35)*reinforced_soil_density)
                        EQIqrstrengh1minimum = ((wall_height - EQdepthfrombottom)*1*reinforced_soil_density) + ((dead_load1 + dead_load2 + dead_load3)*0.75) +((live_load1 + live_load2 + live_load3)*1) + ((EQSequivalent*1)*reinforced_soil_density)
                        EQIqrcritical = max(EQIqrstrengh1maximum,EQIqrstrengh1minimum)
                else:
                    EQIqrstrengh1maximum = None
                    EQIqrstrengh1minimum = None
                    EQIqrcritical = None
          
                  


              
                EQTensileloadstrengh1maximum = (EQkareinforced * EQIqrstrengh1maximum * EQSpacing) + (EQkareinforced * EQSpacing * 1.25*vertical_strip_load1/EQDj1) + (EQkareinforced * EQSpacing * 1.25*vertical_strip_load2/EQDj2) + (EQkareinforced * EQSpacing * 1.25*vertical_strip_load3/EQDj3)
                EQTensileloadstrengh1minimum= (EQkareinforced * EQIqrstrengh1minimum * EQSpacing) + (EQkareinforced * EQSpacing * 0.9*vertical_strip_load1/EQDj1) + (EQkareinforced * EQSpacing * 0.9*vertical_strip_load2/EQDj2) + (EQkareinforced * EQSpacing * 0.9*vertical_strip_load3/EQDj3)
                EQTensileloadcriticalstatic = (EQkareinforced * EQIqrcritical * EQSpacing) + (EQkareinforced * EQSpacing * 1.25*vertical_strip_load1/EQDj1) + (EQkareinforced * EQSpacing * 1.25*vertical_strip_load2/Dj2) + (EQkareinforced * EQSpacing * 1.25*vertical_strip_load3/EQDj3)

        
               

                #Tensileloadstrengh1maximum = (EQkareinforced * Iqrstrengh1maximum * Spacing) + (kareinforced * Spacing * 1.5*vertical_strip_load1/Dj1) + (kareinforced * Spacing * 1.5*vertical_strip_load2/Dj2) + (kareinforced * Spacing * 1.5*vertical_strip_load3/Dj3)
                #Tensileloadstrengh1minimum= (EQkareinforced * Iqrstrengh1minimum * Spacing) + (kareinforced * Spacing * 0.75*vertical_strip_load1/Dj1) + (kareinforced * Spacing * 0.75*vertical_strip_load2/Dj2) + (kareinforced * Spacing * 0.75*vertical_strip_load3/Dj3)
                EQTensileloadstaticcritical = EQTensileloadcriticalstatic
               
               
              #  EQTensileload= (Inertialforceofreinforcedactivemass*EQPulloutlength/totalsumofpulloutlengths) + EQTensileloadstaticcritical
             #   EQTstatic = EQTensileloadstaticcritical
              #  EQTdynamic =  (Inertialforceofreinforcedactivemass*EQPulloutlength/totalsumofpulloutlengths)

                
                EQTensileload= (Inertialforceofreinforcedactivemass/total_reinforcements) + EQTensileloadstaticcritical
                EQTstatic = EQTensileloadstaticcritical
                EQTdynamic =  (Inertialforceofreinforcedactivemass/total_reinforcements)



                T_staticrequired=  (EQrfid*EQrfw*EQrfcreep*EQfs*EQTstatic)/(1.2*EQCoverge_Ratio )
                             
                T_dynamicrequired=  (EQrfid*EQrfw*EQfs*EQTdynamic)/(1.2*EQCoverge_Ratio )


                T_staticconnectionrequired=  (EQconnectionbiochem*EQTstatic)/(0.8*1.2*EQCoverge_Ratio*EQconnectioncreep )
                             
                T_dynamicconnectionrequired=  (EQconnectionbiochem*EQTdynamic)/(0.8*1.2*EQCoverge_Ratio*EQconnectioncreep )

                ##### RUPTURE CHECK
                #EQCDRrupture = EQCoverge_Ratio*EQtensile_strength/(EQTensileload*1.1)
                EQCDRrupture = EQtensile_strength/(T_staticrequired+T_dynamicrequired)

                EQCDRconnection = EQtensile_strength/(T_staticconnectionrequired+T_dynamicconnectionrequired)
           

                EQIhp = (EQactivelength + (0.5*EQPulloutlength))*math.tan(math.radians(top_slope))

                if top_slope < 0:
                    EQIhp = None
                elif top_slope == 0:
                    EQIhp = 0
                elif top_slope > 0:
                    if EQIhp > top_rise:
                        EQIhp=top_rise
                    else:  # top_slope > 0 and Xs < wall_length
                        EQIhp = (EQactivelength + (0.5*EQPulloutlength))*math.tan(math.radians(top_slope))
                else:
                    EQIhp = None
                
                if EQIlengthofdlll<EQPulloutlength:
                    EQVERTSTRESS=((dead_load1 + dead_load2 + dead_load3)*EQIlengthofdlll/EQPulloutlength)+((wall_height+EQIhp-EQdepthfrombottom)*reinforced_soil_density)          
                else:
                    EQVERTSTRESS=((dead_load1 + dead_load2 + dead_load3)*1)+((wall_height+EQIhp-EQdepthfrombottom)*reinforced_soil_density) 
            
                EQpullout_capacity = 2*scale_factor*EQPulloutlength*EQCoverge_Ratio*EQVERTSTRESS*0.8*math.tan(math.radians(EQPull_Out_Interface_Angle))/(1)
                
                
                ##### PULLOUT CHECK
                EQCDRpullout = 1.2*EQpullout_capacity/(EQTensileload)
                
   #             EQreinforcement_results.append((EQdepthfrombottom, EQCDRInternalsliding, EQTensileload, EQTdynamic, EQTstatic, T_staticrequired, T_dynamicrequired, EQpullout_capacity, EQCDRrupture, EQCDRpullout, EQCDRconnection))

                # Append reinforcement results as a dictionary instead of a tuple
                EQreinforcement_results.append({
                    "Depth from Bottom": EQdepthfrombottom,
                    "CDR Internal Sliding": EQCDRInternalsliding,
                    "Tensile Load": EQTensileload,
                    "Dynamic Tensile Load": EQTdynamic,
                    "Static Tensile Load": EQTstatic,
                    "Static Required Tensile Load": T_staticrequired,
                    "Dynamic Required Tensile Load": T_dynamicrequired,
                    "Pullout Capacity": EQpullout_capacity,
                    "CDR Rupture": EQCDRrupture,
                    "CDR Pullout": EQCDRpullout,
                    "CDR Connection": EQCDRconnection
                })





                # After loop, print total results for verification
            print(f"Total EQreinforcement_results: {len(EQreinforcement_results)}")


            print("Rhstrengh1maximum:", Rhstrengh1maximum)
            print("Rhstrengh1minimum:", Rhstrengh1minimum)
            print("Rhcritical:", Rhcritical)

            print("Rvstrengh1maximum:", Rvstrengh1maximum)
            print("Rvstrengh1minimum:", Rvstrengh1minimum)
            print("Rvcritical:", Rvcritical)
            print("Slidingresistancestrengh1maximum:", Slidingresistancestrengh1maximum)
            print("Slidingresistancestrengh1minimum:", Slidingresistancestrengh1minimum)
            print("Slidingresistancecritical:", Slidingresistancecritical)

            print("CDRslidingstrengh1maximum:", CDRslidingstrengh1maximum)
            print("CDRslidingstrengh1minimum:", CDRslidingstrengh1minimum)
            print("CDRslidingcritical:", CDRslidingcritical)

                # Earthquake sliding
            print("EQRh:", EQRh)
            print("EQRv:", EQRv)
            print("EQSlidingresistance:", EQSlidingresistance)
            print("EQCDRsliding:", EQCDRsliding)

                # Bearing
            print("Mostrengh1maximum:", Mostrengh1maximum)
            print("Mostrengh1minimum:", Mostrengh1minimum)
            print("Moservice:", Moservice)
            print("Mocritical:", Mocritical)

            print("Mrstrengh1maximum:", Mrstrengh1maximum)
            print("Mrstrengh1minimum:", Mrstrengh1minimum)
            print("Mrservice:", Mrservice)
            print("Mrcritical:", Mrcritical)

            print("Rvstrengh1maximumforbearing:", Rvstrengh1maximumforbearing)
            print("Rvstrengh1minimumforbearing:", Rvstrengh1minimumforbearing)
            print("Rvcriticalforbearing:", Rvcriticalforbearing)
            print("Rvserviceforbearing:", Rvserviceforbearing)
            print("Emax:", Emax)
            print("Emin:", Emin)
            print("Ecritical:", Ecritical)
            print("Eservice:", Eservice)

            print("qrstrengh1maximum:", qrstrengh1maximum)
            print("qrstrengh1minimum:", qrstrengh1minimum)
            print("qrcritical:", qrcritical)
            print("qrservice:", qrservice)

            print("qnetstrengh1maximum:", qnetstrengh1maximum)
            print("qnetstrengh1minimum:", qnetstrengh1minimum)
            print("qnetcritical:", qnetcritical)
            print("qnetservice:", qnetservice)

            print("CDRBearingstrengh1maximum:", CDRBearingstrengh1maximum)
            print("CDRBearingstrengh1minimum:", CDRBearingstrengh1minimum)
            print("CDRBearingcritical:", CDRBearingcritical)
            print("CDRBearingservice:", CDRBearingservice)

                # For EQbearing
            print("EQMrbearing:", EQMrbearing)
            print("EQMobearing:", EQMobearing)
            print("EQRvbearing:", EQRvbearing)
            print("EQbearingecc:", EQbearingecc)
            print("EQqr:", EQqr)
            print("EQqnet:", EQqnet)
            print("CDRBearingEQ:", CDRBearingEQ)

                # Eccentricity
            print("Mostrengh1maximumforECC:", Mostrengh1maximumforECC)
            print("Mostrengh1minimumforECC:", Mostrengh1minimumforECC)
            print("MocriticalforECC:", MocriticalforECC)

            print("Mrstrengh1maximumforECC:", Mrstrengh1maximumforECC)
            print("Mrstrengh1minimumforECC:", Mrstrengh1minimumforECC)
            print("MrcriticalforECC:", MrcriticalforECC)

            print("Rvstrengh1maximumforECC:", Rvstrengh1maximumforECC)
            print("Rvstrengh1minimumforECC:", Rvstrengh1minimumforECC)
            print("RvcriticalforECC:", RvcriticalforECC)
            print("RvserviceforECC:", RvserviceforECC)

            print("EmaxforECC:", EmaxforECC)
            print("EminforECC:", EminforECC)
            print("EcriticalforECC:", EcriticalforECC)

                # For EQECC
            print("EQMrec:", EQMrec)
            print("EQMo:", EQMo)
            print("EQRv:", EQRv)
            print("EQEccentricity:", EQEccentricity)
            print("AllowableEccentricityEQ:", AllowableEccentricityEQ)


                #EQreinforcement_results.append((EQdepthfrombottom, EQCDRInternalsliding, EQTensileload, EQTdynamic, EQTstatic, T_staticrequired, T_dynamicrequired, EQpullout_capacity, EQCDRrupture, EQCDRpullout))
                #reinforcement_results.append((depthfrombottom, IRhcritical, IRvcritical,  ISlidingresistancecritical, CDRInternalslidingcritical, Tensileloadcritical, pullout_capacitycritical,CDRrupturecritical, CDRpulloutcritical))


            return jsonify({
                "Reinforcement Results": reinforcement_results,
                "EQ Reinforcement Results": EQreinforcement_results,
                
                "Horizontal Load for sliding check": {
                    "Maximum": Rhstrengh1maximum,
                    "Minimum": Rhstrengh1minimum,
                    "Critical": Rhcritical
                },
                
                "Vertical Load for sliding check": {
                    "Maximum": Rvstrengh1maximum,
                    "Minimum": Rvstrengh1minimum,
                    "Critical": Rvcritical
                },
                
                "Sliding Resistance for sliding check": {
                    "Maximum": Slidingresistancestrengh1maximum,
                    "Minimum": Slidingresistancestrengh1minimum,
                    "Critical": Slidingresistancecritical
                },
                
                "CDR Sliding for sliding check": {
                    "Maximum": CDRslidingstrengh1maximum,
                    "Minimum": CDRslidingstrengh1minimum,
                    "Critical": CDRslidingcritical
                },
                
                "Earthqauke case Values for sliding check": {
                    "EQRh": EQRh,
                    "EQRv": EQRv,
                    "EQ Sliding Resistance": EQSlidingresistance,
                    "EQ CDR Sliding": EQCDRsliding
                },
                
                "Overturning moment for bearing capacity check": {
                    "Maximum": Mostrengh1maximum,
                    "Minimum": Mostrengh1minimum,
                    "Service": Moservice,
                    "Critical": Mocritical
                },
                
                "Moment Resistance for bearing capacity check": {
                    "Maximum": Mrstrengh1maximum,
                    "Minimum": Mrstrengh1minimum,
                    "Service": Mrservice,
                    "Critical": Mrcritical
                },
                
                "Rv for bearing check": {
                    "Rv Maximum": Rvstrengh1maximumforbearing,
                    "Rv Minimum": Rvstrengh1minimumforbearing,
                    "Rv Critical": Rvcriticalforbearing,
                    "Rv Service": Rvserviceforbearing
                },
                
                "Eccentricity for bearing capacity check": {
                    "Emax": Emax,
                    "Emin": Emin,
                    "Ecritical": Ecritical,
                    "Eservice": Eservice
                },
                
                "Bearing stress for bearing capacity check": {
                    "Maximum": qrstrengh1maximum,
                    "Minimum": qrstrengh1minimum,
                    "Critical": qrcritical,
                    "Service": qrservice
                },
                
                "Net bearing capacity for bearing capacity check": {
                    "Maximum": qnetstrengh1maximum,
                    "Minimum": qnetstrengh1minimum,
                    "Critical": qnetcritical,
                    "Service": qnetservice
                },
                
                "CDR bearing capacity check": {
                    "Maximum": CDRBearingstrengh1maximum,
                    "Minimum": CDRBearingstrengh1minimum,
                    "Critical": CDRBearingcritical,
                    "Service": CDRBearingservice
                },
                
                "Earthqauke case for bearing check": {
                    "EQMrbearing": EQMrbearing,
                    "EQMobearing": EQMobearing,
                    "EQRvbearing": EQRvbearing,
                    "EQ Bearing ECC": EQbearingecc,
                    "EQ QR": EQqr,
                    "EQ Qnet": EQqnet,
                    "CDR Bearing EQ": CDRBearingEQ
                },
                
                "Overturning moment for eccentricity check": {
                    "Maximum": Mostrengh1maximumforECC,
                    "Minimum": Mostrengh1minimumforECC,
                    "Critical": MocriticalforECC
                },
                
                "Moment Resistance for eccentricity check": {
                    "Maximum": Mrstrengh1maximumforECC,
                    "Minimum": Mrstrengh1minimumforECC,
                    "Critical": MrcriticalforECC
                },
                
                "Rv for eccentricity check": {
                    "Maximum": Rvstrengh1maximumforECC,
                    "Minimum": Rvstrengh1minimumforECC,
                    "Critical": RvcriticalforECC,
                    "Service": RvserviceforECC
                },
                
                "Eccentricity for static check": {        
                    "Eminimum": EminforECC,
                    "Ecritical": EcriticalforECC,
                    "Emaximum": EmaxforECC
                },
                
                "Earthquake case for for eccentricity check": {
                    "EQMrec": EQMrec,
                    "EQMo": EQMo,
                    "EQRv": EQRv,
                    "EQ Eccentricity": EQEccentricity,
                    "Allowable EQ Eccentricity": AllowableEccentricityEQ,
                    "Allowable Eccentricity 1": AllowableEccentricity1
                }
            })
            

        except ValueError as e:
            error_message = "Error: Invalid input detected. Please ensure all values are numeric."
            print(f"ValueError encountered: {e}")  # Logs the exact error for debugging
            return {"error": error_message}

