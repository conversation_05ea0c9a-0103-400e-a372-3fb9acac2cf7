{% extends "base.html" %} {% block content %}
<div class="hero">
  <h1>Welcome to GRS Wall Designer</h1>
  <p class="tagline">
    Innovative Solutions for Geosynthetic Reinforced Soil Walls
  </p>
</div>

<div class="features">
  <div class="feature">
    <i class="fas fa-cogs"></i>
    <h2>Precise Calculations</h2>
    <p>
      Our advanced algorithms ensure accurate design and analysis of GRS walls.
    </p>
  </div>
  <div class="feature">
    <i class="fas fa-chart-line"></i>
    <h2>Comprehensive Reports</h2>
    <p>Generate detailed reports with visualizations for your projects.</p>
  </div>
  <div class="feature">
    <i class="fas fa-users"></i>
    <h2>User-Friendly Interface</h2>
    <p>Intuitive design makes it easy for engineers of all levels to use.</p>
  </div>
</div>

<div class="cta">
  <h2>Ready to Start Your Project?</h2>
  <p>
    Select an option from the navigation menu to begin your GRS wall design
    journey.
  </p>
  <button id="run-analysis-btn" class="cta-button">Run Analysis</button>
</div>

<div id="analysis-results" style="display: none">
  <div id="result-buttons" class="mt-3">
    <a
      href="{{ url_for('external_stability_results') }}"
      class="btn btn-primary"
      >External Stability Results</a
    >
    <a
      href="{{ url_for('internal_stability_results') }}"
      class="btn btn-primary"
      >Internal Stability Results</a
    >
  </div>
  <div class="mt-3">
    <label for="satisfaction-checkbox" class="form-check-label">
      <input
        type="checkbox"
        id="satisfaction-checkbox"
        class="form-check-input"
      />
      Are you satisfied with the design configuration?
    </label>
  </div>
  <div id="generate-report-container" class="mt-3" style="display: none">
    <button id="generate-report-btn" class="btn btn-success">
      Generate Report
    </button>
  </div>
</div>

<div
  id="error-message"
  class="alert alert-danger mt-3"
  style="display: none"
></div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const runAnalysisBtn = document.getElementById("run-analysis-btn");
    const analysisResults = document.getElementById("analysis-results");
    const satisfactionCheckbox = document.getElementById(
      "satisfaction-checkbox"
    );
    const generateReportContainer = document.getElementById(
      "generate-report-container"
    );
    const generateReportBtn = document.getElementById("generate-report-btn");
    const errorMessage = document.getElementById("error-message");

    if (localStorage.getItem("analysisRun") === "true") {
      analysisResults.style.display = "block";
    }

    runAnalysisBtn.addEventListener("click", function () {
      fetch("/run_analysis", { method: "POST" })
        .then((response) => response.json())
        .then((data) => {
          if (data.has_results) {
            analysisResults.style.display = "block";
            localStorage.setItem("analysisRun", "true");
            errorMessage.style.display = "none";
          } else {
            errorMessage.textContent = data.error;
            errorMessage.style.display = "block";
            analysisResults.style.display = "none";
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          errorMessage.textContent =
            "An error occurred while running the analysis.";
          errorMessage.style.display = "block";
        });
    });

    satisfactionCheckbox.addEventListener("change", function () {
      generateReportContainer.style.display = this.checked ? "block" : "none";
    });

    generateReportBtn.addEventListener("click", function () {
      window.location.href = "/generate_report";
    });
  });
</script>

{% with messages = get_flashed_messages(with_categories=true) %} {% if messages
%}
<ul class="flashes">
  {% for category, message in messages %}
  <li class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
    {{ message }}
  </li>
  {% endfor %}
</ul>
{% endif %} {% endwith %} {% endblock %}
