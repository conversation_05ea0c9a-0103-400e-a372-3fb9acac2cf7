{% extends "base.html" %}

{% block content %}

<div id="reinforcementproperties-container">
    <h1>Reinforcement Properties</h1>
    <form method="POST" id="reinforcementproperties-form">
        <input type="hidden" id="row_count" name="row_count" value="{{ reinforcement_data|length }}">
        <div class="table-responsive">
            <table id="reinforcementTable" class="table table-bordered table-striped">
                <thead class="table-light">
                    <tr>
                        <th>Type ID</th>
                        <th>Name</th>
                        <th>Tult (kN/m)</th>
                        <th>RF ID</th>
                        <th>RF W</th>
                        <th>RF CR</th>
                        <th>FS</th>
                        <th>Pullout Angle</th>
                        <th>Sliding Angle</th>
                        <th>Scale Factor</th>
                        <th>Remove</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in reinforcement_data %}
                        <tr>
                            <td><input type="text" name="type_id_{{ loop.index0 }}" value="{{ item.get('type_id', '') }}" required></td>
                            <td><input type="text" name="name_{{ loop.index0 }}" value="{{ item.get('name', '') }}" required></td>
                            <td><input type="text" name="tult_{{ loop.index0 }}" value="{{ item.get('tult', '') }}" required></td>
                            <td><input type="text" name="rfid_{{ loop.index0 }}" value="{{ item.get('rfid', '') }}" required></td>
                            <td><input type="text" name="rfw_{{ loop.index0 }}" value="{{ item.get('rfw', '') }}" required></td>
                            <td><input type="text" name="rfcr_{{ loop.index0 }}" value="{{ item.get('rfcr', '') }}" required></td>
                            <td><input type="text" name="fs_{{ loop.index0 }}" value="{{ item.get('fs', '') }}" required></td>
                            <td><input type="text" name="pullout_angle_{{ loop.index0 }}" value="{{ item.get('pullout_angle', '') }}" required></td>
                            <td><input type="text" name="sliding_angle_{{ loop.index0 }}" value="{{ item.get('sliding_angle', '') }}" required></td>
                            <td><input type="text" name="scale_factor_{{ loop.index0 }}" value="{{ item.get('scale_factor', '') }}" required></td>
                            <td><button type="button" class="remove-row">Remove</button></td>
                        </tr>
                    {% else %}
                    <tr>
                         <td><input type="text" name="type_id_0" value="" required></td>
                            <td><input type="text" name="name_0" value="" required></td>
                            <td><input type="text" name="tult_0" value="" required></td>
                            <td><input type="text" name="rfid_0" value="" required></td>
                            <td><input type="text" name="rfw_0" value="" required></td>
                            <td><input type="text" name="rfcr_0" value="" required></td>
                            <td><input type="text" name="fs_0" value="" required></td>
                            <td><input type="text" name="pullout_angle_0" value="" required></td>
                            <td><input type="text" name="sliding_angle_0" value="" required></td>
                            <td><input type="text" name="scale_factor_0" value="" required></td>
                            <td><button type="button" class="remove-row">Remove</button></td>
                       
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <button type="button" id="add-row">Add Row</button>
        <button type="submit">Save Reinforcement Properties</button>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script>
    $(document).ready(function(){
        // Function to add a new row to the table
        $("#add-row").click(function(){
            var table = $("#reinforcementTable tbody");
            var newRowIndex = table.children().length;
            var newRow = `
                <tr>
                    <td><input type="text" name="type_id_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="name_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="tult_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="rfid_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="rfw_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="rfcr_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="fs_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="pullout_angle_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="sliding_angle_${newRowIndex}" value="" required></td>
                    <td><input type="text" name="scale_factor_${newRowIndex}" value="" required></td>
                    <td><button type="button" class="remove-row">Remove</button></td>
                </tr>
            `;
            table.append(newRow);

            // Update the row_count hidden field
            updateRowCount();
        });

        // Function to remove a row from the table
        $(document).on('click', '.remove-row', function(){
            var rowCount = $("#reinforcementTable tbody tr").length;
            if (rowCount <= 1) {
                alert('Cannot remove the last row. Please keep at least one row.');
                return;
            }

            $(this).closest('tr').remove();

            // Update row numbers and row_count hidden field
            $("#reinforcementTable tbody tr").each(function(index) {
                $(this).find('input').each(function(){
                    var name = $(this).attr('name');
                    if (name) {
                        $(this).attr('name', name.replace(/_\d+$/, "_" + index));
                    }
                });
            });

            // Update the row_count hidden field
            updateRowCount();
        });

        // Update row_count when form is submitted
        $("#reinforcementproperties-form").submit(function(event) {
            // Validate form
            if ($('#reinforcementTable tbody tr').length === 0) {
                alert('Please add at least one reinforcement property.');
                event.preventDefault();
                return;
            }
            updateRowCount();
        });

        // Function to update the row_count hidden field
        function updateRowCount() {
            $("#row_count").val($("#reinforcementTable tbody").children().length);
        }
    });
</script>
{% endblock %}
