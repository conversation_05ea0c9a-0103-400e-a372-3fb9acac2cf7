/* Existing styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

nav {
    background-color: #333;
    color: #fff;
    padding: 1rem;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin-right: 1rem;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
}

main {
    padding: 2rem;
}

h1 {
    color: #4a4a4a;
    font-size: 24px;
    margin-bottom: 20px;
}

form {
    max-width: 500px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

label {
    display: block;
    margin-top: 1rem;
    font-size: 16px;
    color: #4a4a4a;
    font-weight: 600;
}

input[type="text"],
input[type="number"] {
    width: 100%;
    padding: 8px;
    margin-top: 0.5rem;
    border: 2px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
}

input[type="text"]:focus,
input[type="number"]:focus {
    border: 2px solid #66afe9;
    background-color: #ffffff;
}

button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 1rem;
}

button:hover {
    background-color: #45a049;
}

#back-button {
    background-color: #555;
    float: right;
}

#back-button:hover {
    background-color: #333;
}

#geometry-visualization {
    margin-top: 2rem;
    border: 1px solid #ccc;
    height: 400px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    font-size: 16px;
    color: #4a4a4a;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

input[type="number"] {
    padding: 8px;
    border: 2px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    width: 100%;
}

input[type="number"]:focus {
    border: 2px solid #66afe9;
    background-color: #ffffff;
}

button[type="submit"] {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
}

button[type="submit"]:hover {
    background-color: #45a049;
}

.note {
    font-size: 13px;
    color: #555;
    padding: 10px;
    background-color: #f0f8ff;
    border-left: 4px solid #ffa500;
    border-radius: 5px;
    margin-top: 8px;
}

/* New styles for the home page */
.hero {
    background-color: #798387;
    color: white;
    text-align: center;
    padding: 4rem 2rem;
    margin-bottom: 2rem;
}

.hero h1 {
    font-size: 2.8rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero .tagline {
    font-size: 1.4rem;
    font-weight: 300;
    margin-bottom: 2rem;
}

.features {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.feature {
    flex-basis: 30%;
    text-align: center;
    padding: 2rem;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 2rem;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.feature i {
    font-size: 3rem;
    color: #1a5f7a;
    margin-bottom: 1rem;
}

.feature h2 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature p {
    color: #666;
}

.cta {
    background-color: #f0f8ff;
    text-align: center;
    padding: 4rem 2rem;
    margin-top: 3rem;
    border-radius: 8px;
}

.cta h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.cta p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-button {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    font-size: 1.1rem;
    transition: background-color 0.3s ease;
}

.cta-button:hover {
    background-color: #45a049;
}

/* Responsive design for home page */
@media (max-width: 768px) {
    .features {
        flex-direction: column;
    }
    
    .feature {
        flex-basis: 100%;
        margin-bottom: 2rem;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero .tagline {
        font-size: 1.2rem;
    }

    .cta h2 {
        font-size: 1.8rem;
    }

    .cta p {
        font-size: 1rem;
    }
}
