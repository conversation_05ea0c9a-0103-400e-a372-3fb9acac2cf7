.results-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.result-item {
    flex: 1 1 300px; /* Each item takes up at least 300px or 1/3 of the container */
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.result-item strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

.result-item span,
.result-item pre {
    display: block;
    word-break: break-word; /* Prevents long words from breaking the layout */
    white-space: pre-wrap; /* Preserves formatting and line breaks */
    color: #555;
}

/* Optional: Style for pre elements to make them more readable */
.result-item pre {
    background-color: #eee;
    padding: 5px;
    border-radius: 3px;
    overflow-x: auto; /* Add horizontal scroll for long content */
}

/* Add some responsive styling */
@media (max-width: 768px) {
    .result-item {
        flex: 1 1 100%; /* On smaller screens, each item takes full width */
    }
}
