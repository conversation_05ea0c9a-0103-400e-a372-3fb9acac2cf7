{% extends "base.html" %}

{% block content %}
<div id="retainedsoil-form">
    <h1>Retained Soil Properties</h1>
    <form method="POST" id="retainedsoil-form">
        <div class="form-group">
            <label for="retained-density">Soil Density (kN/m³):</label>
            <input type="number" id="retained-density" name="retainedsoil_density" step="0.1" required value="{{ session.get('retainedsoil_density', '') }}">
        </div>
        
        <div class="form-group">
            <label for="retained-friction">Friction Angle (°):</label>
            <input type="number" id="retained-friction" name="retainedfriction_angle" step="0.1" required value="{{ session.get('retainedfriction_angle', '') }}">
        </div>
        
        <div class="form-group">
            <label for="retained-cohesion">Cohesion (kN/m²):</label>
            <input type="number" id="retained-cohesion" name="retainedcohesion" step="0.1" required value="{{ session.get('retainedcohesion', '') }}">
        </div>
        
        <button type="submit">Save Retained Soil Inputs</button>
    </form>

    <div class="note">
        <p>Note: Cohesion is ignored in design.</p>
    </div>
</div>

{% endblock %}
