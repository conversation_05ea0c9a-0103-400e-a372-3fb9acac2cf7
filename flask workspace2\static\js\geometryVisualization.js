/**
 * Geometry Visualization Module
 * Handles geometry canvas drawing and interactions
 * @version 2.0.0 - Production Ready - Uses shared DrawingUtils
 */

(function() {
    'use strict';

    // Check if DrawingUtils is available
    if (typeof window.DrawingUtils === 'undefined') {
        console.error('DrawingUtils is required but not loaded. Please ensure drawingUtils.js is loaded first.');
        return;
    }

    /**
     * Initialize Geometry Visualization
     * @returns {boolean} Success status
     */
    function initializeGeometryVisualization() {
        // Prevent multiple initializations
        if (window.geometryVisualizationInitialized) {
            return false;
        }

        try {

            // Canvas setup
            const canvas = document.getElementById('geometry-canvas');
            if (!canvas) {
                console.error('Canvas geometry-canvas not found!');
                return false;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Failed to get canvas context');
                return false;
            }

            // Canvas state variables
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let isDown = false;
            let lastX, lastY;

            canvas.width = 1000;
            canvas.height = 600;

            /**
             * Get current form values for geometry
             * @returns {Object} Current form values
             */
            function getFormValues() {
                return {
                    wallHeight: parseFloat(document.getElementById('wall-height')?.value) || 5,
                    embedmentDepth: parseFloat(document.getElementById('embedment-depth')?.value) || 1,
                    wallLength: parseFloat(document.getElementById('wall-length')?.value) || 6,
                    wallBatter: parseFloat(document.getElementById('wall-batter')?.value) || 0,
                    backslopeAngle: parseFloat(document.getElementById('backslope-angle')?.value) || 0,
                    backslopeRise: parseFloat(document.getElementById('backslope-rise')?.value) || 0
                };
            }

            /**
             * Main drawing function using DrawingUtils
             * @param {number} wallHeight - Wall height in meters
             * @param {number} embedmentDepth - Embedment depth in meters
             * @param {number} wallLength - Wall length in meters
             * @param {number} wallBatter - Wall batter in degrees
             * @param {number} backslopeAngle - Backslope angle in degrees
             * @param {number} backslopeRise - Backslope rise in meters
             * @returns {void}
             */
            function drawGRSWall(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise) {
                try {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    ctx.save();
                    ctx.translate(translateX, translateY);
                    ctx.scale(scale, scale);

                    // Use DrawingUtils for consistent geometry calculations
                    const geometry = window.DrawingUtils.calculateGeometry(
                        wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise
                    );

                    // Draw basic wall structure
                    window.DrawingUtils.drawBasicWall(ctx, geometry);

                    // Draw backslope if angle > 0
                    if (backslopeAngle > 0) {
                        const backslopeGeometry = window.DrawingUtils.calculateBackslope(
                            geometry, backslopeAngle, backslopeRise
                        );
                        window.DrawingUtils.drawBackslope(ctx, backslopeGeometry);
                    }

                    // Draw dimensions
                    window.DrawingUtils.drawDimensions(ctx, geometry);

                    // Draw labels
                    window.DrawingUtils.drawLabels(ctx, geometry);

                    ctx.restore();
                } catch (error) {
                    console.error('Error drawing GRS wall:', error);
                    ctx.restore();
                }
            }



            /**
             * Redraw canvas with current form values
             * @returns {void}
             */
            function redrawCanvas() {
                try {
                    const values = getFormValues();
                    drawGRSWall(
                        values.wallHeight,
                        values.embedmentDepth,
                        values.wallLength,
                        values.wallBatter,
                        values.backslopeAngle,
                        values.backslopeRise
                    );
                } catch (error) {
                    console.error('Error redrawing geometry canvas:', error);
                }
            }

    // Define the input change handler function first
    function handleGeometryInputChange() {
        console.log("🔥 GEOMETRY INPUT CHANGED:", this.id, "value:", this.value);

        // Trigger validation if the validateField function exists (from the template)
        if (typeof validateField === 'function') {
            validateField(this);
        }

        // Small delay to ensure value is updated, then redraw
        console.log("🔥 About to call redrawCanvas...");
        setTimeout(() => {
            console.log("🔥 Calling redrawCanvas now!");
            redrawCanvas();
        }, 10);
    }

    // Use event delegation to handle input changes (works with AJAX content)
    function setupFormListeners() {
        console.log("Setting up event delegation for geometry form...");

        // Remove any existing delegated listeners to prevent duplicates
        document.removeEventListener('input', handleDelegatedInput);
        document.removeEventListener('change', handleDelegatedInput);

        // Add delegated event listeners to the document
        document.addEventListener('input', handleDelegatedInput);
        document.addEventListener('change', handleDelegatedInput);

        console.log("Event delegation setup complete for geometry inputs");
        return true;
    }

    // Delegated event handler
    function handleDelegatedInput(event) {
        const target = event.target;

        // Check if this is a geometry form input
        if (target.type === 'number' && target.closest('#geometry-form')) {
            console.log("🔥 DELEGATED GEOMETRY INPUT CHANGED:", target.id, "value:", target.value);

            // Call the original handler with the correct context
            handleGeometryInputChange.call(target);
        }
    }

    // Setup event delegation (always succeeds)
    setupFormListeners();

    // Make redraw function available globally for form handler integration
    window.geometryRedrawCanvas = redrawCanvas;

    // Also attach to canvas for direct access
    if (canvas) {
        canvas.redrawCanvas = redrawCanvas;
    }

    // Set up zoom controls
    const zoomInButton = document.getElementById('zoom-in-button');
    const zoomOutButton = document.getElementById('zoom-out-button');
    if (zoomInButton && zoomOutButton) {
        zoomInButton.addEventListener('click', function() {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 1.1;
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });

        zoomOutButton.addEventListener('click', function() {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 0.9;
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });
    }

    // Fit to window button
    const fitButton = document.getElementById('fit-button');
    if (fitButton) {
        fitButton.addEventListener('click', function() {
            scale = 1;
            translateX = 0;
            translateY = 0;
            redrawCanvas();
        });
    }

    // Set up canvas interactions
    canvas.addEventListener('wheel', function(e) {
        e.preventDefault();
        const rect = canvas.getBoundingClientRect();
        const cursorX = e.clientX - rect.left;
        const cursorY = e.clientY - rect.top;
        
        const oldScale = scale;
        scale *= e.deltaY > 0 ? 0.9 : 1.1;
        
        translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
        translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
        
        redrawCanvas();
    });

    canvas.addEventListener('mousedown', function(e) {
        isDown = true;
        canvas.style.cursor = 'grabbing';
        const rect = canvas.getBoundingClientRect();
        lastX = e.clientX - rect.left;
        lastY = e.clientY - rect.top;
    });

    canvas.addEventListener('mouseup', function() {
        isDown = false;
        canvas.style.cursor = 'grab';
    });

    canvas.addEventListener('mousemove', function(e) {
        if (isDown) {
            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;
            
            translateX += currentX - lastX;
            translateY += currentY - lastY;
            
            lastX = currentX;
            lastY = currentY;
            
            redrawCanvas();
        }
    });

            // Initial drawing - wait for form values to be loaded first
            setTimeout(() => {
                redrawCanvas();
            }, 100);

            // Additional redraw after localStorage has had time to load
            setTimeout(() => {
                redrawCanvas();
            }, 600);

            // Mark as initialized
            window.geometryVisualizationInitialized = true;
            return true;

        } catch (error) {
            console.error('Error initializing geometry visualization:', error);
            return false;
        }
    }



    /**
     * Setup screenshot functionality for geometry page
     * @returns {void}
     */
    function setupScreenshotFunctionality() {
        const screenshotButton = document.getElementById('screenshot-button');
        const geometryCanvas = document.getElementById('geometry-canvas');
        const geometryForm = document.getElementById('geometry-form');

        if (screenshotButton && geometryCanvas && geometryForm) {
            // Clear existing event listeners by cloning the button
            const newScreenshotButton = screenshotButton.cloneNode(true);
            screenshotButton.parentNode.replaceChild(newScreenshotButton, screenshotButton);

            // Add screenshot handler
            newScreenshotButton.addEventListener('click', handleGeometryScreenshot);
        }
    }

    /**
     * Handle geometry screenshot
     * @returns {void}
     */
    function handleGeometryScreenshot() {
        try {
            const geometryForm = document.getElementById('geometry-form');
            if (!geometryForm) return;

            const canvas = document.getElementById('geometry-canvas');
            if (!canvas) return;

            const dataURL = canvas.toDataURL('image/png');

            // Store screenshot on server for report generation
            storeScreenshotOnServer(dataURL);

            // Download screenshot
            const link = document.createElement('a');
            link.href = dataURL;
            link.download = 'grs_wall_geometry.png';
            link.click();
        } catch (error) {
            console.error('Error taking geometry screenshot:', error);
        }
    }

    /**
     * Store screenshot on server for report generation
     * @param {string} dataURL - Canvas data URL
     * @returns {void}
     */
    function storeScreenshotOnServer(dataURL) {
        fetch('/store-screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                screenshot: dataURL
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status !== 'success') {
                console.error('Failed to store screenshot:', data.message);
            }
        })
        .catch(error => {
            console.error('Error storing screenshot:', error);
        });
    }

    // Setup screenshot functionality
    setupScreenshotFunctionality();

    /**
     * Check if all required elements are available
     * @returns {boolean} Whether elements are ready
     */
    function checkGeometryElementsReady() {
        const canvas = document.getElementById('geometry-canvas');
        const form = document.getElementById('geometry-form');
        const wallHeightInput = document.getElementById('wall-height');
        return canvas && form && wallHeightInput;
    }

    /**
     * Wait for elements and then initialize
     * @returns {void}
     */
    function waitForGeometryElements() {
        if (checkGeometryElementsReady()) {
            initializeGeometryVisualization();
        } else {
            setTimeout(waitForGeometryElements, 50);
        }
    }

    // Public API - expose initialization function globally
    window.initializeGeometryVisualization = initializeGeometryVisualization;

    // Initialize based on DOM state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForGeometryElements);
    } else {
        waitForGeometryElements();
    }

    // Additional initialization attempts for AJAX content
    setTimeout(() => {
        if (document.getElementById('geometry-canvas') && !window.geometryVisualizationInitialized) {
            initializeGeometryVisualization();
        }
    }, 100);

    setTimeout(() => {
        if (document.getElementById('geometry-canvas') && !window.geometryVisualizationInitialized) {
            initializeGeometryVisualization();
        }
    }, 500);

    window.addEventListener('load', () => {
        if (document.getElementById('geometry-canvas') && !window.geometryVisualizationInitialized) {
            initializeGeometryVisualization();
        }
    });

})(); // End IIFE
