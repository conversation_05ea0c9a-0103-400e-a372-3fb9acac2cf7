{% extends "base.html" %}

{% block content %}
<div id="reinforcedsoil-form">
    <h1>Reinforced Soil Properties</h1>
    <form method="POST" id="reinforcedsoil-form">
        <div class="form-group">
            <label for="reinforced-density">Soil Density (kN/m³):</label>
            <input type="number" id="reinforced-density" name="soil_density" step="0.1" required value="{{ session.get('soil_density', '') }}">
        </div>
        
        <div class="form-group">
            <label for="reinforced-friction">Friction Angle (°):</label>
            <input type="number" id="reinforced-friction" name="friction_angle" step="0.1" required value="{{ session.get('friction_angle', '') }}">
        </div>
        
        <div class="form-group">
            <label for="reinforced-cohesion">Cohesion (kN/m²):</label>
            <input type="number" id="reinforced-cohesion" name="cohesion" step="0.1" required value="{{ session.get('cohesion', '') }}">
        </div>
        
        <button type="submit">Save Reinforced Soil Inputs</button>
    </form>

    <div class="note">
        <p>Note: Cohesion is ignored in design.</p>
    </div>
</div>

{% endblock %}
