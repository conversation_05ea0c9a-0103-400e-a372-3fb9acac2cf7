{% extends "base.html" %}

{% block content %}
<h1>Geometry Inputs</h1>
<form id="geometry-form" method="POST">
    <div class="form-group">
        <label for="wall-height">Wall Height (H: m):</label>
        <input type="number" id="wall-height" name="wall_height" step="0.1" required value="{{ wall_height }}">
    </div>
    
    <div class="form-group">
        <label for="embedment-depth">Embedment Depth (m):</label>
        <input type="number" id="embedment-depth" name="embedment_depth" step="0.1" required value="{{ embedment_depth }}">
    </div>
    
    <div class="form-group">
        <label for="wall-length">Overall Length of Reinforcement (m):</label>
        <input type="number" id="wall-length" name="wall_length" step="0.1" required value="{{ wall_length }}">
    </div>
    
    <div class="form-group">
        <label for="wall-batter"><PERSON><PERSON> (°):</label>
        <input type="number" id="wall-batter" name="wall_batter" step="0.1" required value="{{ wall_batter }}">
    </div>
    
    <div class="form-group">
        <label for="backslope-angle">Back Slope Angle (°):</label>
        <input type="number" id="backslope-angle" name="backslope_angle" step="0.1" required value="{{ backslope_angle }}">
    </div>
    
    <div class="form-group">
        <label for="backslope-rise">Back Slope Rise (m):</label>
        <input type="number" id="backslope-rise" name="backslope_rise" step="0.1" required value="{{ backslope_rise }}">
    </div>
    
    <button type="submit" id="save-button">Save Geometry</button>
</form>

<div class="note">
    <p>Note: Batter < 20° is only designed as walls.<br>Batter < 10° is considered a vertical wall in calculations as a conservative approach.</p>
</div>

<div class="note">
    <p>Note: Slope of soil above wall should be less than the friction angle, else it should be reinforced.</p>
</div>

<div id="geometry-visualization">
    <!-- Placeholder for geometry visualization -->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('geometry-form');
    const saveButton = document.getElementById('save-button');

    // Load data from localStorage
    form.querySelectorAll('input').forEach(input => {
        const storedValue = localStorage.getItem(input.id);
        if (storedValue) input.value = storedValue;
    });

    // Save data to localStorage and server
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Save to localStorage
        form.querySelectorAll('input').forEach(input => {
            localStorage.setItem(input.id, input.value);
        });

        // Save to server
        const formData = new FormData(form);
        fetch('/geometry', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message || 'Geometry data saved successfully!');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving geometry data.');
        });
    });
});
</script>
{% endblock %}
