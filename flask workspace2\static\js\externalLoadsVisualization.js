/**
 * External Loads Visualization Module
 * Handles external loads canvas drawing and interactions
 */

// Make the function available globally and handle both initial load and AJAX reloads
function initializeExternalLoadsVisualization() {
    console.log('Initializing External Loads Visualization...');

    // ONLY skip if we're clearly on reinforcement layout page
    const reinforcementLayoutTable = document.getElementById('reinforcementLayoutTable');
    if (reinforcementLayoutTable) {
        console.log('❌ Detected reinforcement layout page - skipping external loads visualization to prevent conflicts');
        return;
    }

    console.log('✅ Not on reinforcement layout page - proceeding with external loads visualization');

    // Retrieve data from localStorage with error handling
    let geometryData, externalloads_data;

    try {
        geometryData = JSON.parse(localStorage.getItem('geometryData')) || {};
        externalloads_data = JSON.parse(localStorage.getItem('externalloads_data')) || {};
    } catch (error) {
        console.error('Error parsing localStorage data:', error);
        geometryData = {};
        externalloads_data = {};
    }

    // Provide default values if any are missing (using template placeholder defaults)
    const wallHeight = geometryData.wallHeight || 5;
    const embedmentDepth = geometryData.embedmentDepth || 1;
    const wallLength = geometryData.wallLength || 6;
    const wallBatter = geometryData.wallBatter || 0;
    const backslopeAngle = geometryData.backslopeAngle || 0;
    const backslopeRise = geometryData.backslopeRise || 2;

    console.log('External loads using geometry data:', {
        wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise
    });

    // Use let for variables that will be updated
    let dead_loads = externalloads_data.dead_loads || [0, 0, 0];
    let live_loads = externalloads_data.live_loads || [0, 0, 0];
    let vertical_strip_load = externalloads_data.vertical_strip_load || [0, 0, 0];
    let horizontal_strip_load = externalloads_data.horizontal_strip_load || [0, 0, 0];
    let strip_load_width = externalloads_data.strip_load_width || [0, 0, 0];
    let strip_load_distance = externalloads_data.strip_load_distance || [0, 0, 0];
    let earthquake_acceleration = externalloads_data.earthquake_acceleration || 0;
    let seismic_force = externalloads_data.seismic_force || 0;
    let impact_loads = externalloads_data.impact_loads || {
        rupture: { upper: 0, second: 0 },
        pullout: { upper: 0, second: 0 }
    };

    console.log('Loaded external loads data successfully');

    const canvas = document.getElementById('geometry2-canvas');
    if (!canvas) {
        console.error('Canvas geometry2-canvas not found! Available elements:',
            Array.from(document.querySelectorAll('canvas')).map(c => c.id));
        return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('Failed to get canvas context');
        return;
    }

    console.log('External loads canvas found and context created successfully');

    let scale = 1;
    let translateX = 0;
    let translateY = 0;
    let isDown = false;
    let lastX, lastY;

    canvas.width = 1000;
    canvas.height = 600;

    console.log("External loads canvas initialized:", canvas.width, "x", canvas.height);

    function drawGRSWall(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise, dead_loads, live_loads, vertical_strip_load, horizontal_strip_load, strip_load_width, strip_load_distance, earthquake_acceleration, seismic_force, impact_loads) {
        console.log("drawGRSWall called with:", {wallHeight, embedmentDepth, wallLength});

        // Clear the canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Continue with the original drawing
        ctx.save(); // Save the current context state
        ctx.translate(translateX, translateY); // Apply translation
        ctx.scale(scale, scale); // Apply scaling

        const baseScale = 50;
        const baseX = 100, baseY = 400;
        const batterOffset = Math.tan((wallBatter * Math.PI) / 180) * (wallHeight * baseScale);
        const fasciaThickness = 0.2 * baseScale;

        // Fascia as a polygon
        const fascia = {
            x1: baseX - fasciaThickness, y1: baseY,
            x2: baseX - fasciaThickness + batterOffset, y2: baseY - (wallHeight * baseScale),
            x3: baseX + batterOffset + fasciaThickness, y3: baseY - (wallHeight * baseScale),
            x4: baseX, y4: baseY
        };

        // Reinforced fill as a polygon
        const reinforcedFill = {
            x1: baseX, y1: baseY,
            x2: baseX + batterOffset, y2: baseY - (wallHeight * baseScale),
            x3: baseX + (wallLength * baseScale) + batterOffset, y3: baseY - (wallHeight * baseScale),
            x4: baseX + (wallLength * baseScale), y4: baseY
        };

        // Retained fill as a polygon
        const retainedFill = {
            x1: baseX + (wallLength * baseScale), y1: baseY,
            x2: baseX + (wallLength * baseScale) + batterOffset, y2: baseY - (wallHeight * baseScale),
            x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y3: baseY - (wallHeight * baseScale),
            x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y4: baseY
        };

        const embedment = {
            x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY,
            x2: baseX - 10 + batterOffset, y2: baseY - (embedmentDepth * baseScale)
        };

        const foundationSoil = {
            x1: embedment.x1, y1: baseY + (0.5*wallHeight * baseScale),
            x2: retainedFill.x4, y2: baseY
        };

        // Function to draw an arrow
        function drawArrow(ctx, x1, y1, x2, y2, arrowSize = 0.05) {
            ctx.strokeStyle = "#000"; // Set the color of the arrow
            ctx.fillStyle = "#000"; // Set the fill color for the arrow head

            const dx = x2 - x1;
            const dy = y2 - y1;
            const angle = Math.atan2(dy, dx);
            const arrowLength = Math.sqrt(dx * dx + dy * dy) * arrowSize;

            // Draw arrow head at the end
            ctx.save();
            ctx.translate(x2, y2);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(-arrowLength, -arrowLength / 2);
            ctx.lineTo(-arrowLength, arrowLength / 2);
            ctx.closePath();
            ctx.fill();
            ctx.restore();

            // Draw arrow head at the start
            ctx.save();
            ctx.translate(x1, y1);
            ctx.rotate(angle + Math.PI);
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(-arrowLength, -arrowLength / 2);
            ctx.lineTo(-arrowLength, arrowLength / 2);
            ctx.closePath();
            ctx.fill();
            ctx.restore();

            // Draw the arrow line
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // Calculate slope offset
        const backslopeAngleRadians = (backslopeAngle * Math.PI) / 180;
        const slopeBaseLength = backslopeRise / Math.tan(backslopeAngleRadians);
        const slopeStartX = reinforcedFill.x2;
        const slopeStartY = reinforcedFill.y2;
        let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);
        let slopeEndY = slopeStartY - (backslopeRise * baseScale);

        ctx.fillStyle = "#FFA500"; // Soil color

        if (slopeEndX <= retainedFill.x3) {
            // Case 1: Slope ends before or exactly at retainedFill.x3
            const horizontalEndX = retainedFill.x3;
            const horizontalEndY = slopeEndY;

            // Draw slope
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.stroke();

            // Draw horizontal extension
            ctx.beginPath();
            ctx.moveTo(slopeEndX, slopeEndY);
            ctx.lineTo(horizontalEndX, horizontalEndY);
            ctx.stroke();

            // Fill area (Trapezoidal)
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.lineTo(horizontalEndX, horizontalEndY);
            ctx.lineTo(retainedFill.x3, retainedFill.y3);
            ctx.closePath();
            ctx.fill();

            // Display top rise dimension
            const topRise = backslopeRise; // Use correct rise value
            ctx.beginPath();
            ctx.moveTo(retainedFill.x3, retainedFill.y3);
            ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise * baseScale);
            ctx.stroke();

            // Draw arrow
            drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - topRise * baseScale, 0.1);

            // Display dimension text slightly offset
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 -40, retainedFill.y3 - (topRise * baseScale) / 2);
        } else {
            // Case 2: Slope extends beyond retainedFill.x3
            slopeEndX = retainedFill.x3;
            slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));

            // Draw slope
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.stroke();

            // Fill area (Triangular)
            ctx.beginPath();
            ctx.moveTo(slopeStartX, slopeStartY);
            ctx.lineTo(slopeEndX, slopeEndY);
            ctx.lineTo(retainedFill.x3, retainedFill.y3);
            ctx.closePath();
            ctx.fill();

            // Display top rise dimension
            const topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan(backslopeAngleRadians)/baseScale;
            ctx.beginPath();
            ctx.moveTo(retainedFill.x3, retainedFill.y3);
            ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise*baseScale);
            ctx.stroke();

            // Draw arrow
            drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - (topRise*baseScale), 0.1);

            // Display dimension text slightly offset
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 - 40, retainedFill.y3 - (topRise*0.5*baseScale));
        }

        // Draw foundation soil
        ctx.fillStyle = "#A98B6D";
        ctx.fillRect(foundationSoil.x1, foundationSoil.y1, foundationSoil.x2 - foundationSoil.x1, foundationSoil.y2 - foundationSoil.y1);

        // Draw embedment
        ctx.fillStyle = "#A98B6D";
        ctx.fillRect(embedment.x1, embedment.y1, embedment.x2 - embedment.x1, embedment.y2 - embedment.y1);

        // Draw fascia
        ctx.fillStyle = "#666";
        ctx.beginPath();
        ctx.moveTo(fascia.x1, fascia.y1);
        ctx.lineTo(fascia.x2, fascia.y2);
        ctx.lineTo(fascia.x3, fascia.y3);
        ctx.lineTo(fascia.x4, fascia.y4);
        ctx.fill();

        // Draw reinforced fill
        ctx.fillStyle = "#D6B85A";
        ctx.beginPath();
        ctx.moveTo(reinforcedFill.x1, reinforcedFill.y1);
        ctx.lineTo(reinforcedFill.x2, reinforcedFill.y2);
        ctx.lineTo(reinforcedFill.x3, reinforcedFill.y3);
        ctx.lineTo(reinforcedFill.x4, reinforcedFill.y4);
        ctx.fill();

        // Draw retained fill
        ctx.fillStyle = "#D2B48C";
        ctx.beginPath();
        ctx.moveTo(retainedFill.x1, retainedFill.y1);
        ctx.lineTo(retainedFill.x2, retainedFill.y2);
        ctx.lineTo(retainedFill.x3, retainedFill.y3);
        ctx.lineTo(retainedFill.x4, retainedFill.y4);
        ctx.fill();

        // Draw dimensions with arrows
        ctx.strokeStyle = "#000";
        ctx.lineWidth = 1;

        // Draw wall height dimension
        ctx.beginPath();
        ctx.moveTo(baseX - 20, baseY);
        ctx.lineTo(baseX - 20, baseY - (wallHeight * baseScale));
        ctx.stroke();
        drawArrow(ctx, baseX - 20, baseY, baseX - 20, baseY - (wallHeight * baseScale), 0.05);
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "top";
        ctx.fillText(`${wallHeight} m`, baseX - 40, baseY - (0.5*wallHeight * baseScale) - 10);

        // Draw wall length dimension
        ctx.beginPath();
        ctx.moveTo(baseX, baseY);
        ctx.lineTo(baseX + (wallLength * baseScale), baseY);
        ctx.stroke();
        drawArrow(ctx, baseX, baseY, baseX + (wallLength * baseScale), baseY, 0.05);
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`${wallLength} m`, baseX + ((wallLength * baseScale) / 2), baseY + 10);

        // Display Reinforced fill
        ctx.beginPath();
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`Reinforced Fill`, baseX + ((0.5*wallLength * baseScale)), baseY - (0.5*wallHeight * baseScale) - 10);

        // Display Retained fill
        ctx.beginPath();
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`Retained Fill`, baseX + ((1.5*wallLength * baseScale)), baseY - (0.5*wallHeight * baseScale) - 10);

        // Display Foundation Soil
        ctx.beginPath();
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(`Foundation Soil`, baseX + ((1.0*wallLength * baseScale)), baseY + (0.25*wallHeight * baseScale) - 10);

        // Draw embedment depth dimension
        ctx.beginPath();
        ctx.moveTo(baseX - 40, baseY);
        ctx.lineTo(baseX - 40, baseY - (embedmentDepth * baseScale));
        ctx.stroke();
        drawArrow(ctx, baseX - 40, baseY, baseX - 40, baseY - (embedmentDepth * baseScale), 0.2);
        ctx.font = "18px Arial";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "top";
        ctx.fillText(`${embedmentDepth} m`, baseX - 50-fasciaThickness, baseY - (0.5*embedmentDepth * baseScale) - 10);

        // Function to draw arrows (for dead, live, and strip loads)
        function drawArrow1(ctx, fromX, fromY, toX, toY, size, color, hollow = false) {
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            if (hollow) {
                ctx.stroke(); // Draw only the outline for hollow arrows
            } else {
                ctx.lineTo(toX - size * Math.cos(angle - Math.PI / 6), toY - size * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - size * Math.cos(angle + Math.PI / 6), toY - size * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }
        }

        // Function to draw dead and live loads (downward arrows)
        function drawLoads(ctx, startX, endX, y, loadValue, color, label) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.font = "18px Arial";
            ctx.fillStyle = color;
            ctx.textAlign = "center";
            ctx.textBaseline = "top";

            // Draw arrows to represent uniform surcharge (pointing downward)
            const arrowSpacing = 50; // Adjust spacing as needed
            for (let x = startX; x <= endX; x += arrowSpacing) {
                drawArrow1(ctx, x, y, x, y + 20, 5, color); // Arrow points downward
            }

            // Display load value
            ctx.fillText(`${label}: ${loadValue.toFixed(2)} kPa`, (startX + endX) / 2, y -25);
        }

        // Function to draw strip loads (footing with vertical and horizontal arrows)
        function drawStripLoads(ctx, startX, y, verticalStripLoads, horizontalStripLoads, stripLoadWidths, stripLoadDistances) {
            const stripLoadColor = "purple";
            for (let i = 0; i < verticalStripLoads.length; i++) {
                if (verticalStripLoads[i] > 0 || horizontalStripLoads[i] > 0) {
                    const stripLoadX = startX + (stripLoadDistances[i] * baseScale) - (stripLoadWidths[i] * baseScale / 2);
                    const centerX = stripLoadX + (stripLoadWidths[i] * baseScale / 2);

                    if (stripLoadX <= retainedFill.x3) {
                        // Draw strip load as a footing
                        ctx.fillStyle = stripLoadColor;
                        ctx.fillRect(stripLoadX, y, stripLoadWidths[i] * baseScale, 20); // Adjust height as needed

                        // Draw vertical arrow (pointing downward) for vertical load
                        if (verticalStripLoads[i] > 0) {
                            drawArrow1(ctx, centerX, y-40, centerX, y  , 5, stripLoadColor); // Vertical arrow
                            ctx.font = "14px Arial";
                            ctx.fillStyle = stripLoadColor;
                            ctx.textAlign = "center";
                            ctx.textBaseline = "top";
                            ctx.fillText(`${verticalStripLoads[i].toFixed(2)} kN/m`, centerX, y -30); // Display vertical load value
                        }

                        // Draw horizontal arrow (pointing left) for horizontal load
                        if (horizontalStripLoads[i] > 0) {
                            drawArrow1(ctx, centerX, y-5, centerX - 30, y-5, 5, stripLoadColor); // Horizontal arrow
                            ctx.font = "14px Arial";
                            ctx.fillStyle = stripLoadColor;
                            ctx.textAlign = "right";
                            ctx.textBaseline = "middle";
                            ctx.fillText(`${horizontalStripLoads[i].toFixed(2)} kN/m`, centerX - 35, y); // Display horizontal load value
                        }
                    } else {
                        // Display message if beyond influence zone
                        ctx.font = "14px Arial";
                        ctx.fillStyle = stripLoadColor;
                        ctx.textAlign = "center";
                        ctx.textBaseline = "top";
                        ctx.fillText(`Strip Load ${i + 1}: Beyond Influence Zone`, retainedFill.x3 + 50, y - 40);
                    }
                }
            }
        }

        // Function to draw earthquake acceleration (hollow arrow pointing left)
        function drawEarthquakeAcceleration(ctx, x, y, acceleration) {
            const arrowLength = 50; // Length of the arrow
            const arrowSize = 10; // Size of the arrowhead
            const label = `${acceleration.toFixed(2)}g`;

            // Draw hollow arrow (pointing left)
            ctx.strokeStyle = "#000";
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x - arrowLength, y);
            ctx.lineTo(x - arrowLength + arrowSize, y - arrowSize / 2);
            ctx.moveTo(x - arrowLength, y);
            ctx.lineTo(x - arrowLength + arrowSize, y + arrowSize / 2);
            ctx.stroke();

            // Display acceleration value
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(label, x - arrowLength - 30, y);
        }

        // Main drawing logic for loads
        if (backslopeAngle === 0) {
            // Case when backslope angle is zero
            const loadY = reinforcedFill.y2 - 50; // Adjust Y position as needed
            drawLoads(ctx, reinforcedFill.x2, retainedFill.x3, loadY, dead_loads.reduce((a, b) => a + b, 0), "red", "Dead Load");
            drawLoads(ctx, reinforcedFill.x2, retainedFill.x3, loadY - 50, live_loads.reduce((a, b) => a + b, 0), "blue", "Live Load");
        } else if (slopeEndX < retainedFill.x3) {
            // Case 1: Slope ends before or exactly at retainedFill.x3
            const loadY = slopeEndY - 50; // Adjust Y position as needed
            drawLoads(ctx, slopeEndX, retainedFill.x3, loadY, dead_loads.reduce((a, b) => a + b, 0), "red", "Dead Load");
            drawLoads(ctx, slopeEndX, retainedFill.x3, loadY - 50, live_loads.reduce((a, b) => a + b, 0), "blue", "Live Load");
        } else {
            // Case 2: Slope extends beyond retainedFill.x3
            ctx.font = "18px Arial";
            ctx.fillStyle = "#000";
            ctx.textAlign = "center";
            ctx.textBaseline = "top";
            ctx.fillText("Dead and Live Loads: Beyond Influence Zone", (reinforcedFill.x2 + retainedFill.x3) / 2, reinforcedFill.y2 - 50);
        }

        // Draw strip loads
        const stripLoadY = (backslopeAngle === 0 ? reinforcedFill.y2 : slopeEndY) - 20; // Adjust Y position as needed
        drawStripLoads(ctx, reinforcedFill.x2, stripLoadY, vertical_strip_load, horizontal_strip_load, strip_load_width, strip_load_distance);

        // Draw earthquake acceleration
        drawEarthquakeAcceleration(ctx, (retainedFill.x1 + retainedFill.x3) / 2, (retainedFill.y1 + retainedFill.y3+60) / 2, earthquake_acceleration);

        ctx.restore(); // Restore the context state
    }

    // Redraw function to avoid repeating code
    function redrawCanvas() {
        console.log("Redrawing external loads canvas...");
        const canvas = document.getElementById('geometry2-canvas');
        if (!canvas) {
            console.error("Canvas 'geometry2-canvas' not found!");
            console.log("Available canvases:", Array.from(document.querySelectorAll('canvas')).map(c => c.id));
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error("Could not get canvas context!");
            return;
        }

        console.log("Canvas found, drawing with parameters:", {
            wallHeight, embedmentDepth, wallLength, wallBatter,
            backslopeAngle, backslopeRise, dead_loads, live_loads,
            vertical_strip_load, horizontal_strip_load, strip_load_width,
            strip_load_distance, earthquake_acceleration, seismic_force, impact_loads
        });

        try {
            drawGRSWall(
                wallHeight,
                embedmentDepth,
                wallLength,
                wallBatter,
                backslopeAngle,
                backslopeRise,
                dead_loads,
                live_loads,
                vertical_strip_load,
                horizontal_strip_load,
                strip_load_width,
                strip_load_distance,
                earthquake_acceleration,
                seismic_force,
                impact_loads
            );
            console.log("External loads canvas drawn successfully");
        } catch (error) {
            console.error("Error drawing external loads canvas:", error);
        }
    }

    // Zoom buttons
    const zoomInButton = document.getElementById('zoom-in-button');
    const zoomOutButton = document.getElementById('zoom-out-button');
    if (zoomInButton && zoomOutButton) {
        zoomInButton.addEventListener('click', function () {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 1.1;

            // Adjust translation to zoom around the cursor position
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });

        zoomOutButton.addEventListener('click', function () {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 0.9;

            // Adjust translation to zoom around the cursor position
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });
    }

    // Fit to window
    const fitButton = document.getElementById('fit-button');
    if (fitButton) {
        fitButton.addEventListener('click', function () {
            scale = 1;
            translateX = 0;
            translateY = 0;
            redrawCanvas();
        });
    }

    // Zoom and Pan functionality
    canvas.addEventListener('wheel', function (e) {
        e.preventDefault();
        const rect = canvas.getBoundingClientRect();
        const cursorX = e.clientX - rect.left;
        const cursorY = e.clientY - rect.top;

        const oldScale = scale;
        const factor = e.deltaY > 0 ? 0.9 : 1.1;
        scale *= factor;

        // Adjust translation to zoom around the cursor position
        translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
        translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
        redrawCanvas();
    });

    canvas.addEventListener('mousedown', function (e) {
        isDown = true;
        canvas.style.cursor = 'grabbing';
        const rect = canvas.getBoundingClientRect();
        lastX = e.clientX - rect.left;
        lastY = e.clientY - rect.top;
    });

    canvas.addEventListener('mouseup', function () {
        isDown = false;
        canvas.style.cursor = 'grab';
    });

    canvas.addEventListener('mousemove', function (e) {
        if (isDown) {
            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;

            translateX += currentX - lastX;
            translateY += currentY - lastY;

            lastX = currentX;
            lastY = currentY;
            redrawCanvas();
        }
    });
    
    // Make redraw function available globally for form handler integration
    window.externalLoadsRedrawCanvas = redrawCanvas;

    // Initial draw with default parameters
    console.log("Drawing initial external loads canvas...");
    console.log("Using values:", {wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise});
    console.log("Canvas element:", canvas);
    console.log("Canvas dimensions:", canvas.width, "x", canvas.height);

    // Try immediate draw first
    console.log("Attempting immediate draw...");
    redrawCanvas();

    // Force a redraw after a short delay to ensure canvas is ready
    setTimeout(() => {
        console.log("Force redrawing external loads canvas (500ms delay)...");
        redrawCanvas();
    }, 500);

    // Also try another draw
    setTimeout(() => {
        console.log("Force redrawing external loads canvas (1000ms delay)...");
        redrawCanvas();
    }, 1000);
    


    // Screenshot functionality - only attach if we're on external loads page
    const screenshotButton = document.getElementById('screenshot-button');
    const externalLoadsCanvas = document.getElementById('geometry2-canvas');
    let externalLoadsForm = document.getElementById('externalloadsform');

    if (screenshotButton && externalLoadsCanvas && externalLoadsForm) {
        // Clear ALL existing event listeners by cloning the button
        const newScreenshotButton = screenshotButton.cloneNode(true);
        screenshotButton.parentNode.replaceChild(newScreenshotButton, screenshotButton);

        // Add only our handler
        newScreenshotButton.addEventListener('click', handleExternalLoadsScreenshot);
        console.log("Attached external loads screenshot button listener (cleaned)");
    }

    function handleExternalLoadsScreenshot() {
        // Only handle if we're on external loads page
        const externalLoadsForm = document.getElementById('externalloadsform');
        if (!externalLoadsForm) return;

        const canvas = document.getElementById('geometry2-canvas');
        if (canvas) {
            const dataURL = canvas.toDataURL('image/png');

            // Store screenshot on server for report generation
            storeScreenshotOnServer(dataURL);

            const link = document.createElement('a');
            link.href = dataURL;
            link.download = 'grs_wall_external_loads.png';
            link.click();
            console.log("External loads screenshot downloaded");
        }
    }

    // Function to store screenshot on server for report generation
    function storeScreenshotOnServer(dataURL) {
        fetch('/store-screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                screenshot: dataURL
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Screenshot stored successfully for report generation:', data.screenshot_id);
            } else {
                console.error('Failed to store screenshot:', data.message);
            }
        })
        .catch(error => {
            console.error('Error storing screenshot:', error);
        });
    }
    
    // Add input change event listeners for all external loads inputs
    externalLoadsForm = document.getElementById('externalloadsform');
    if (externalLoadsForm) {
        const inputFields = externalLoadsForm.querySelectorAll('input[type="number"], input[type="checkbox"]');
        console.log("Found", inputFields.length, "input fields for dynamic updates");

        inputFields.forEach(input => {
            // Remove any existing listeners to prevent duplicates
            input.removeEventListener('input', handleInputChange);
            input.addEventListener('input', handleInputChange);
            input.removeEventListener('change', handleInputChange);
            input.addEventListener('change', handleInputChange);
        });

        console.log("Successfully attached dynamic update listeners to all external loads inputs");
    } else {
        console.error("External loads form not found!");
    }

    // Separate function for input change handling to prevent duplicates
    function handleInputChange() {
        const input = this;
        console.log("External loads input changed:", input.id, "value:", input.value);

        // Trigger validation if the validateField function exists (from the template)
        if (typeof validateField === 'function') {
            validateField(input);
        }

        // Update the corresponding variable based on input id
        if (input.id.startsWith('dead_load')) {
            const index = parseInt(input.id.replace('dead_load', '')) - 1;
            if (index >= 0 && index < dead_loads.length) {
                dead_loads[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('live_load')) {
            const index = parseInt(input.id.replace('live_load', '')) - 1;
            if (index >= 0 && index < live_loads.length) {
                live_loads[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('vertical_strip_load')) {
            const index = parseInt(input.id.replace('vertical_strip_load', '')) - 1;
            if (index >= 0 && index < vertical_strip_load.length) {
                vertical_strip_load[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('horizontal_strip_load')) {
            const index = parseInt(input.id.replace('horizontal_strip_load', '')) - 1;
            if (index >= 0 && index < horizontal_strip_load.length) {
                horizontal_strip_load[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('strip_load_width')) {
            const index = parseInt(input.id.replace('strip_load_width', '')) - 1;
            if (index >= 0 && index < strip_load_width.length) {
                strip_load_width[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('strip_load_distance')) {
            const index = parseInt(input.id.replace('strip_load_distance', '')) - 1;
            if (index >= 0 && index < strip_load_distance.length) {
                strip_load_distance[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id === 'earthquake_acceleration') {
            earthquake_acceleration = parseFloat(input.value) || 0;
            console.log("Earthquake acceleration updated:", earthquake_acceleration);
        } else if (input.id === 'seismic_force') {
            seismic_force = parseFloat(input.value) || 0;
        } else if (input.id.includes('impact_')) {
            // Handle impact loads - fix the parsing
            const parts = input.id.split('_');
            if (parts.length >= 3) {
                const type = parts[0]; // rupture or pullout
                const layer = parts[2]; // upper or second

                if (!impact_loads[type]) impact_loads[type] = {};
                impact_loads[type][layer] = parseFloat(input.value) || 0;
            }
        } else if (input.id === 'use_direct_kh') {
            // Handle checkbox change
            console.log("Checkbox changed:", input.checked);
        }

        console.log("Updated values:", {
            dead_loads, live_loads, vertical_strip_load, horizontal_strip_load,
            strip_load_width, strip_load_distance, earthquake_acceleration, seismic_force, impact_loads
        });

        // Small delay to ensure value is updated, then redraw
        setTimeout(() => {
            redrawCanvas();
        }, 10);
    }
}

// Initialize on DOM ready and make available for AJAX reloads
document.addEventListener('DOMContentLoaded', initializeExternalLoadsVisualization);

// Make function available globally for AJAX reinitialization
window.initializeExternalLoadsVisualization = initializeExternalLoadsVisualization;


