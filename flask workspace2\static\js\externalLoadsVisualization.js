/**
 * External Loads Visualization Module
 * Handles external loads canvas drawing and interactions
 * @version 2.0.0 - Production Ready - Uses shared DrawingUtils
 */

(function() {
    'use strict';

    // Check if DrawingUtils is available
    if (typeof window.DrawingUtils === 'undefined') {
        console.error('DrawingUtils is required but not loaded. Please ensure drawingUtils.js is loaded first.');
        return;
    }

    /**
     * Initialize External Loads Visualization
     * @returns {boolean} Success status
     */
    function initializeExternalLoadsVisualization() {
        try {
            // ONLY skip if we're clearly on reinforcement layout page
            const reinforcementLayoutTable = document.getElementById('reinforcementLayoutTable');
            if (reinforcementLayoutTable) {
                return false; // Skip silently for other pages
            }

            // Retrieve data from localStorage with error handling
            const { geometryData, externalloads_data } = loadStoredData();

            // Extract geometry parameters with defaults
            const geometryParams = extractGeometryParams(geometryData);

            // Extract load parameters with defaults
            const loadParams = extractLoadParams(externalloads_data);

            // Setup canvas
            const { canvas, ctx } = setupCanvas();
            if (!canvas || !ctx) {
                return false;
            }

            // Initialize visualization state
            const visualizationState = initializeVisualizationState(canvas, ctx, geometryParams, loadParams);

            // Setup event handlers
            setupEventHandlers(visualizationState);

            // Initial draw
            performInitialDraw(visualizationState);

            return true;

        } catch (error) {
            console.error('Error initializing external loads visualization:', error);
            return false;
        }
    }

    /**
     * Load and parse stored data from localStorage
     * @returns {Object} Parsed geometry and external loads data
     */
    function loadStoredData() {
        let geometryData = {};
        let externalloads_data = {};

        try {
            geometryData = JSON.parse(localStorage.getItem('geometryData')) || {};
            externalloads_data = JSON.parse(localStorage.getItem('externalloads_data')) || {};
        } catch (error) {
            console.error('Error parsing localStorage data:', error);
        }

        return { geometryData, externalloads_data };
    }

    /**
     * Extract geometry parameters with defaults
     * @param {Object} geometryData - Raw geometry data from storage
     * @returns {Object} Processed geometry parameters
     */
    function extractGeometryParams(geometryData) {
        return {
            wallHeight: geometryData.wallHeight || 5,
            embedmentDepth: geometryData.embedmentDepth || 1,
            wallLength: geometryData.wallLength || 6,
            wallBatter: geometryData.wallBatter || 0,
            backslopeAngle: geometryData.backslopeAngle || 0,
            backslopeRise: geometryData.backslopeRise || 2
        };
    }

    /**
     * Extract load parameters with defaults
     * @param {Object} externalloads_data - Raw external loads data from storage
     * @returns {Object} Processed load parameters
     */
    function extractLoadParams(externalloads_data) {
        return {
            dead_loads: externalloads_data.dead_loads || [0, 0, 0],
            live_loads: externalloads_data.live_loads || [0, 0, 0],
            vertical_strip_load: externalloads_data.vertical_strip_load || [0, 0, 0],
            horizontal_strip_load: externalloads_data.horizontal_strip_load || [0, 0, 0],
            strip_load_width: externalloads_data.strip_load_width || [0, 0, 0],
            strip_load_distance: externalloads_data.strip_load_distance || [0, 0, 0],
            earthquake_acceleration: externalloads_data.earthquake_acceleration || 0,
            seismic_force: externalloads_data.seismic_force || 0,
            impact_loads: externalloads_data.impact_loads || {
                rupture: { upper: 0, second: 0 },
                pullout: { upper: 0, second: 0 }
            }
        };
    }

    /**
     * Setup canvas and context
     * @returns {Object} Canvas and context objects
     */
    function setupCanvas() {
        const canvas = document.getElementById('geometry2-canvas');
        if (!canvas) {
            console.error('Canvas geometry2-canvas not found');
            return { canvas: null, ctx: null };
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Failed to get canvas context');
            return { canvas, ctx: null };
        }

        // Set canvas dimensions
        canvas.width = 1000;
        canvas.height = 600;

        return { canvas, ctx };
    }

    /**
     * Initialize visualization state
     * @param {HTMLCanvasElement} canvas - Canvas element
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {Object} geometryParams - Geometry parameters
     * @param {Object} loadParams - Load parameters
     * @returns {Object} Visualization state object
     */
    function initializeVisualizationState(canvas, ctx, geometryParams, loadParams) {
        return {
            canvas,
            ctx,
            geometryParams,
            loadParams,
            // Canvas interaction state
            scale: 1,
            translateX: 0,
            translateY: 0,
            isDown: false,
            lastX: 0,
            lastY: 0,
            // Cached calculations
            geometry: null,
            slope: null
        };
    }

    /**
     * Main drawing function using shared DrawingUtils
     * @param {Object} state - Visualization state
     */
    function drawGRSWallWithLoads(state) {
        try {
            const { canvas, ctx, scale, translateX, translateY } = state;

            // Refresh geometry and load parameters from localStorage for dynamic updates
            const { geometryData, externalloads_data } = loadStoredData();
            const geometryParams = extractGeometryParams(geometryData);
            const loadParams = extractLoadParams(externalloads_data);

            // Update state with fresh data
            state.geometryParams = geometryParams;
            state.loadParams = loadParams;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Apply transformations
            ctx.save();
            ctx.translate(translateX, translateY);
            ctx.scale(scale, scale);

            // Calculate geometry using shared utilities
            const geometry = window.DrawingUtils.calculateGeometry(
                geometryParams.wallHeight,
                geometryParams.embedmentDepth,
                geometryParams.wallLength,
                geometryParams.wallBatter,
                geometryParams.backslopeAngle,
                geometryParams.backslopeRise
            );

            // Calculate backslope using shared utilities
            const slope = window.DrawingUtils.calculateBackslope(
                geometry,
                geometryParams.backslopeAngle,
                geometryParams.backslopeRise
            );

            // Cache calculations in state
            state.geometry = geometry;
            state.slope = slope;

            // Draw basic wall structure using shared utilities
            window.DrawingUtils.drawBasicWall(ctx, geometry, slope);

            // Draw dimensions using shared utilities
            window.DrawingUtils.drawDimensions(
                ctx,
                geometry,
                geometryParams.wallHeight,
                geometryParams.wallLength,
                geometryParams.embedmentDepth
            );

            // Draw labels using shared utilities
            window.DrawingUtils.drawLabels(ctx, geometry, geometryParams.wallLength);

            // Draw backslope rise dimension
            drawBackslopeRiseDimension(ctx, geometry, slope, geometryParams);

            // Draw external loads
            drawExternalLoads(ctx, geometry, slope, geometryParams, loadParams);

            ctx.restore();

        } catch (error) {
            console.error('Error drawing GRS wall with loads:', error);
        }
    }

    /**
     * Draw backslope rise dimension
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {Object} geometry - Geometry calculations
     * @param {Object} slope - Slope calculations
     * @param {Object} geometryParams - Geometry parameters
     */
    function drawBackslopeRiseDimension(ctx, geometry, slope, geometryParams) {
        try {
            const { retainedFill, baseScale, reinforcedFill } = geometry;
            const { slopeEndY, isCase1 } = slope;
            const { backslopeAngle, backslopeRise } = geometryParams;

            // Only draw if backslope angle is greater than 0
            if (backslopeAngle <= 0) {
                return;
            }

            let topRise;
            if (isCase1) {
                topRise = backslopeRise;
            } else {
                topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan((backslopeAngle * Math.PI) / 180) / baseScale;
            }

            // Only draw if topRise is meaningful
            if (topRise <= 0) {
                return;
            }

            // Draw dimension line
            ctx.strokeStyle = window.DrawingUtils.CONSTANTS.COLORS.DIMENSION_LINE;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(retainedFill.x3, retainedFill.y3);
            ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise * baseScale);
            ctx.stroke();

            // Draw arrow using shared utility
            window.DrawingUtils.drawArrow(
                ctx,
                retainedFill.x3,
                retainedFill.y3,
                retainedFill.x3,
                retainedFill.y3 - topRise * baseScale,
                0.1
            );

            // Display dimension text
            ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;
            ctx.fillStyle = window.DrawingUtils.CONSTANTS.COLORS.TEXT;
            ctx.textAlign = "center";
            ctx.fillText(
                `${topRise.toFixed(2)} m`,
                retainedFill.x3 - 40,
                retainedFill.y3 - (topRise * baseScale) / 2
            );
        } catch (error) {
            console.error('Error drawing backslope rise dimension:', error);
        }
    }

    /**
     * Draw external loads on the wall
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {Object} geometry - Geometry calculations
     * @param {Object} slope - Slope calculations
     * @param {Object} geometryParams - Geometry parameters
     * @param {Object} loadParams - Load parameters
     */
    function drawExternalLoads(ctx, geometry, slope, geometryParams, loadParams) {
        try {
            const { reinforcedFill, retainedFill } = geometry;
            const { slopeEndX, slopeEndY, isCase1 } = slope;
            const { backslopeAngle } = geometryParams;
            const {
                dead_loads, live_loads, vertical_strip_load, horizontal_strip_load,
                strip_load_width, strip_load_distance, earthquake_acceleration
            } = loadParams;

            // Draw dead and live loads based on backslope configuration
            if (backslopeAngle === 0) {
                // Case when backslope angle is zero
                const loadY = reinforcedFill.y2 - 50;
                drawUniformLoads(ctx, reinforcedFill.x2, retainedFill.x3, loadY, dead_loads, "red", "Dead Load");
                drawUniformLoads(ctx, reinforcedFill.x2, retainedFill.x3, loadY - 50, live_loads, "blue", "Live Load");
            } else if (isCase1) {
                // Case 1: Slope ends before or exactly at retainedFill.x3
                const loadY = slopeEndY - 50;
                drawUniformLoads(ctx, slopeEndX, retainedFill.x3, loadY, dead_loads, "red", "Dead Load");
                drawUniformLoads(ctx, slopeEndX, retainedFill.x3, loadY - 50, live_loads, "blue", "Live Load");
            } else {
                // Case 2: Slope extends beyond retainedFill.x3
                ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;
                ctx.fillStyle = window.DrawingUtils.CONSTANTS.COLORS.TEXT;
                ctx.textAlign = "center";
                ctx.textBaseline = "top";
                ctx.fillText(
                    "Dead and Live Loads: Beyond Influence Zone",
                    (reinforcedFill.x2 + retainedFill.x3) / 2,
                    reinforcedFill.y2 - 50
                );
            }

            // Draw strip loads
            const stripLoadY = (backslopeAngle === 0 ? reinforcedFill.y2 : slopeEndY) - 20;
            drawStripLoads(ctx, geometry, stripLoadY, vertical_strip_load, horizontal_strip_load,
                         strip_load_width, strip_load_distance);

            // Draw earthquake acceleration
            drawEarthquakeAcceleration(ctx, geometry, earthquake_acceleration);

        } catch (error) {
            console.error('Error drawing external loads:', error);
        }
    }

    /**
     * Draw uniform loads (dead and live loads)
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} startX - Start X coordinate
     * @param {number} endX - End X coordinate
     * @param {number} y - Y coordinate
     * @param {Array} loadValues - Array of load values
     * @param {string} color - Load color
     * @param {string} label - Load label
     */
    function drawUniformLoads(ctx, startX, endX, y, loadValues, color, label) {
        try {
            const totalLoad = loadValues.reduce((a, b) => a + b, 0);

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;
            ctx.fillStyle = color;
            ctx.textAlign = "center";
            ctx.textBaseline = "top";

            // Draw arrows to represent uniform surcharge (pointing downward)
            const arrowSpacing = 50;
            for (let x = startX; x <= endX; x += arrowSpacing) {
                window.DrawingUtils.drawLoadArrow(ctx, x, y, x, y + 20, 5, color, false);
            }

            // Display load value
            ctx.fillText(`${label}: ${totalLoad.toFixed(2)} kPa`, (startX + endX) / 2, y - 25);
        } catch (error) {
            console.error('Error drawing uniform loads:', error);
        }
    }

    /**
     * Draw strip loads (footing with vertical and horizontal arrows)
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {Object} geometry - Geometry calculations
     * @param {number} y - Y coordinate for strip loads
     * @param {Array} verticalStripLoads - Vertical strip load values
     * @param {Array} horizontalStripLoads - Horizontal strip load values
     * @param {Array} stripLoadWidths - Strip load widths
     * @param {Array} stripLoadDistances - Strip load distances
     */
    function drawStripLoads(ctx, geometry, y, verticalStripLoads, horizontalStripLoads, stripLoadWidths, stripLoadDistances) {
        try {
            const stripLoadColor = "purple";
            const { reinforcedFill, retainedFill, baseScale } = geometry;

            for (let i = 0; i < verticalStripLoads.length; i++) {
                if (verticalStripLoads[i] > 0 || horizontalStripLoads[i] > 0) {
                    const stripLoadX = reinforcedFill.x2 + (stripLoadDistances[i] * baseScale) - (stripLoadWidths[i] * baseScale / 2);
                    const centerX = stripLoadX + (stripLoadWidths[i] * baseScale / 2);

                    if (stripLoadX <= retainedFill.x3) {
                        // Draw strip load as a footing
                        ctx.fillStyle = stripLoadColor;
                        ctx.fillRect(stripLoadX, y, stripLoadWidths[i] * baseScale, 20);

                        // Draw vertical arrow for vertical load
                        if (verticalStripLoads[i] > 0) {
                            window.DrawingUtils.drawLoadArrow(ctx, centerX, y - 40, centerX, y, 5, stripLoadColor, false);
                            ctx.font = "14px Arial";
                            ctx.fillStyle = stripLoadColor;
                            ctx.textAlign = "center";
                            ctx.textBaseline = "top";
                            ctx.fillText(`${verticalStripLoads[i].toFixed(2)} kN/m`, centerX, y - 30);
                        }

                        // Draw horizontal arrow for horizontal load
                        if (horizontalStripLoads[i] > 0) {
                            window.DrawingUtils.drawLoadArrow(ctx, centerX, y - 5, centerX - 30, y - 5, 5, stripLoadColor, false);
                            ctx.font = "14px Arial";
                            ctx.fillStyle = stripLoadColor;
                            ctx.textAlign = "right";
                            ctx.textBaseline = "middle";
                            ctx.fillText(`${horizontalStripLoads[i].toFixed(2)} kN/m`, centerX - 35, y);
                        }
                    } else {
                        // Display message if beyond influence zone
                        ctx.font = "14px Arial";
                        ctx.fillStyle = stripLoadColor;
                        ctx.textAlign = "center";
                        ctx.textBaseline = "top";
                        ctx.fillText(`Strip Load ${i + 1}: Beyond Influence Zone`, retainedFill.x3 + 50, y - 40);
                    }
                }
            }
        } catch (error) {
            console.error('Error drawing strip loads:', error);
        }
    }

    /**
     * Draw earthquake acceleration (hollow arrow pointing left)
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {Object} geometry - Geometry calculations
     * @param {number} acceleration - Earthquake acceleration value
     */
    function drawEarthquakeAcceleration(ctx, geometry, acceleration) {
        try {
            const { retainedFill } = geometry;
            const x = (retainedFill.x1 + retainedFill.x3) / 2;
            const y = (retainedFill.y1 + retainedFill.y3 + 60) / 2;

            const arrowLength = 50;
            const arrowSize = 10;
            const label = `${acceleration.toFixed(2)}g`;

            // Draw hollow arrow (pointing left)
            ctx.strokeStyle = window.DrawingUtils.CONSTANTS.COLORS.DIMENSION_LINE;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x - arrowLength, y);
            ctx.lineTo(x - arrowLength + arrowSize, y - arrowSize / 2);
            ctx.moveTo(x - arrowLength, y);
            ctx.lineTo(x - arrowLength + arrowSize, y + arrowSize / 2);
            ctx.stroke();

            // Display acceleration value
            ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;
            ctx.fillStyle = window.DrawingUtils.CONSTANTS.COLORS.TEXT;
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.fillText(label, x - arrowLength - 30, y);
        } catch (error) {
            console.error('Error drawing earthquake acceleration:', error);
        }
    }

    /**
     * Setup event handlers for canvas interaction and dynamic updates
     * @param {Object} state - Visualization state
     */
    function setupEventHandlers(state) {
        const { canvas } = state;

        // Mouse wheel zoom
        canvas.addEventListener('wheel', function(e) {
            e.preventDefault();
            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            state.scale *= zoomFactor;
            drawGRSWallWithLoads(state);
        });

        // Mouse drag pan
        canvas.addEventListener('mousedown', function(e) {
            state.isDown = true;
            const rect = canvas.getBoundingClientRect();
            state.lastX = e.clientX - rect.left;
            state.lastY = e.clientY - rect.top;
        });

        canvas.addEventListener('mousemove', function(e) {
            if (!state.isDown) return;
            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;
            state.translateX += currentX - state.lastX;
            state.translateY += currentY - state.lastY;
            state.lastX = currentX;
            state.lastY = currentY;
            drawGRSWallWithLoads(state);
        });

        canvas.addEventListener('mouseup', function() {
            state.isDown = false;
        });

        canvas.addEventListener('mouseleave', function() {
            state.isDown = false;
        });

        // Setup dynamic input listeners for real-time updates
        setupDynamicInputListeners(state);

        // Setup screenshot functionality
        setupScreenshotFunctionality();

        // Setup zoom buttons
        setupZoomButtons(state);
    }

    /**
     * Setup dynamic input listeners for real-time visualization updates
     * @param {Object} state - Visualization state
     */
    function setupDynamicInputListeners(state) {
        const externalLoadsForm = document.getElementById('externalloadsform');
        if (!externalLoadsForm) {
            return;
        }

        const inputFields = externalLoadsForm.querySelectorAll('input[type="number"], input[type="checkbox"]');

        inputFields.forEach(input => {
            // Remove any existing listeners to prevent duplicates
            input.removeEventListener('input', handleInputChange);
            input.addEventListener('input', handleInputChange);
            input.removeEventListener('change', handleInputChange);
            input.addEventListener('change', handleInputChange);
        });

        function handleInputChange() {
            const input = this;

            // Trigger validation if the validateField function exists (from the template)
            if (typeof validateField === 'function') {
                validateField(input);
            }

            // Update the corresponding load parameter based on input id
            updateLoadParameter(input, state);

            // Small delay to ensure value is updated, then redraw
            setTimeout(() => {
                drawGRSWallWithLoads(state);
            }, 10);
        }
    }

    /**
     * Update load parameters based on input changes
     * @param {HTMLInputElement} input - The changed input element
     * @param {Object} state - Visualization state
     */
    function updateLoadParameter(input, state) {
        const { loadParams } = state;

        if (input.id.startsWith('dead_load')) {
            const index = parseInt(input.id.replace('dead_load', '')) - 1;
            if (index >= 0 && index < loadParams.dead_loads.length) {
                loadParams.dead_loads[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('live_load')) {
            const index = parseInt(input.id.replace('live_load', '')) - 1;
            if (index >= 0 && index < loadParams.live_loads.length) {
                loadParams.live_loads[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('vertical_strip_load')) {
            const index = parseInt(input.id.replace('vertical_strip_load', '')) - 1;
            if (index >= 0 && index < loadParams.vertical_strip_load.length) {
                loadParams.vertical_strip_load[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('horizontal_strip_load')) {
            const index = parseInt(input.id.replace('horizontal_strip_load', '')) - 1;
            if (index >= 0 && index < loadParams.horizontal_strip_load.length) {
                loadParams.horizontal_strip_load[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('strip_load_width')) {
            const index = parseInt(input.id.replace('strip_load_width', '')) - 1;
            if (index >= 0 && index < loadParams.strip_load_width.length) {
                loadParams.strip_load_width[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id.startsWith('strip_load_distance')) {
            const index = parseInt(input.id.replace('strip_load_distance', '')) - 1;
            if (index >= 0 && index < loadParams.strip_load_distance.length) {
                loadParams.strip_load_distance[index] = parseFloat(input.value) || 0;
            }
        } else if (input.id === 'earthquake_acceleration') {
            loadParams.earthquake_acceleration = parseFloat(input.value) || 0;
        } else if (input.id === 'seismic_force') {
            loadParams.seismic_force = parseFloat(input.value) || 0;
        } else if (input.id.includes('impact_')) {
            // Handle impact loads
            const parts = input.id.split('_');
            if (parts.length >= 3) {
                const type = parts[1]; // rupture or pullout
                const layer = parts[2]; // upper or second

                if (!loadParams.impact_loads[type]) loadParams.impact_loads[type] = {};
                loadParams.impact_loads[type][layer] = parseFloat(input.value) || 0;
            }
        }
    }

    /**
     * Setup zoom buttons functionality
     * @param {Object} state - Visualization state
     */
    function setupZoomButtons(state) {
        const zoomInButton = document.getElementById('zoom-in-button');
        const zoomOutButton = document.getElementById('zoom-out-button');
        const fitButton = document.getElementById('fit-button');

        if (zoomInButton) {
            zoomInButton.addEventListener('click', function() {
                const cursorX = state.canvas.width / 2;
                const cursorY = state.canvas.height / 2;
                const oldScale = state.scale;
                state.scale *= 1.1;

                // Adjust translation to zoom around the cursor position
                state.translateX = state.translateX + (cursorX - state.translateX - cursorX / oldScale) * (1 - 1 / oldScale);
                state.translateY = state.translateY + (cursorY - state.translateY - cursorY / oldScale) * (1 - 1 / oldScale);
                drawGRSWallWithLoads(state);
            });
        }

        if (zoomOutButton) {
            zoomOutButton.addEventListener('click', function() {
                const cursorX = state.canvas.width / 2;
                const cursorY = state.canvas.height / 2;
                const oldScale = state.scale;
                state.scale *= 0.9;

                // Adjust translation to zoom around the cursor position
                state.translateX = state.translateX + (cursorX - state.translateX - cursorX / oldScale) * (1 - 1 / oldScale);
                state.translateY = state.translateY + (cursorY - state.translateY - cursorY / oldScale) * (1 - 1 / oldScale);
                drawGRSWallWithLoads(state);
            });
        }

        if (fitButton) {
            fitButton.addEventListener('click', function() {
                state.scale = 1;
                state.translateX = 0;
                state.translateY = 0;
                drawGRSWallWithLoads(state);
            });
        }
    }

    /**
     * Setup screenshot functionality
     */
    function setupScreenshotFunctionality() {
        const screenshotButton = document.getElementById('screenshot-button');
        const externalLoadsCanvas = document.getElementById('geometry2-canvas');
        const externalLoadsForm = document.getElementById('externalloadsform');

        if (screenshotButton && externalLoadsCanvas && externalLoadsForm) {
            // Clear ALL existing event listeners by cloning the button
            const newScreenshotButton = screenshotButton.cloneNode(true);
            screenshotButton.parentNode.replaceChild(newScreenshotButton, screenshotButton);

            // Add only our handler
            newScreenshotButton.addEventListener('click', handleExternalLoadsScreenshot);
        }
    }

    /**
     * Handle screenshot button click
     */
    function handleExternalLoadsScreenshot() {
        // Only handle if we're on external loads page
        const externalLoadsForm = document.getElementById('externalloadsform');
        if (!externalLoadsForm) return;

        const canvas = document.getElementById('geometry2-canvas');
        if (canvas) {
            const dataURL = canvas.toDataURL('image/png');

            // Store screenshot on server for report generation
            storeScreenshotOnServer(dataURL);

            const link = document.createElement('a');
            link.href = dataURL;
            link.download = 'grs_wall_external_loads.png';
            link.click();
        }
    }

    /**
     * Store screenshot on server for report generation
     * @param {string} dataURL - Canvas data URL
     */
    function storeScreenshotOnServer(dataURL) {
        fetch('/store-screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                screenshot: dataURL
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Screenshot stored successfully for report generation:', data.screenshot_id);
            } else {
                console.error('Failed to store screenshot:', data.message);
            }
        })
        .catch(error => {
            console.error('Error storing screenshot:', error);
        });
    }

    /**
     * Perform initial draw
     * @param {Object} state - Visualization state
     */
    function performInitialDraw(state) {
        drawGRSWallWithLoads(state);

        // Make redraw function available globally for form handler integration
        window.externalLoadsRedrawCanvas = function() {
            drawGRSWallWithLoads(state);
        };
    }

    // Make the initialization function available globally for AJAX reloads
    window.initializeExternalLoadsVisualization = initializeExternalLoadsVisualization;

    // Initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeExternalLoadsVisualization);
    } else {
        // DOM is already loaded
        initializeExternalLoadsVisualization();
    }

})();

// Production ready - only log in development
if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost') {
    console.log('✅ External Loads Visualization loaded (development mode)');
}


