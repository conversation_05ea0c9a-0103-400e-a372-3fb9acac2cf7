document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById("foundationsoil-form");
    const fields = ["foundationsoildensity", "foundationsoilfriction_angle", "foundationsoilcohesion", "eccentricity", "eccentricity_seismic", "watertable"];

    // Load stored values
    fields.forEach(field => {
        if (localStorage.getItem(field)) {
            document.getElementById(field).value = localStorage.getItem(field);
        }
    });

    // Save values to localStorage on input change
    form.addEventListener("input", function() {
        fields.forEach(field => {
            localStorage.setItem(field, document.getElementById(field).value);
        });
    });

    // Save form data asynchronously with AJAX
    form.addEventListener("submit", function(e) {
        e.preventDefault();  // Prevent normal form submission

        const formData = new FormData(form);

        fetch('/foundationsoil', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);  // Show success message
        })
        .catch(error => {
            console.error('Error:', error);
            alert('There was an error with the submission.');
        });
    });
});