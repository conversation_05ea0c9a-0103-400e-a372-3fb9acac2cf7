{% extends "base.html" %}

{% block content %}
<h1>Project Information</h1>
<form id="project-info-form" method="POST">
    <label for="project-name">Project Name:</label>
    <input type="text" id="project-name" name="project_name" required>
    
    <label for="project-id">Project ID:</label>
    <input type="text" id="project-id" name="project_id" required>
    
    <label for="designer">Designer:</label>
    <input type="text" id="designer" name="designer" required>
    
    <label for="client">Client:</label>
    <input type="text" id="client" name="client" required>
    
    <label for="description">Project Description:</label>
    <input type="text" id="description" name="description" required>
    
    <label for="date">Date (day/month/year):</label>
    <input type="text" id="date" name="date" required>
    
    <label for="revision">Revision Number:</label>
    <input type="text" id="revision" name="revision" required>
    
    <button type="submit" id="save-button">Save Project Info</button>
</form>
<button id="back-button">Back</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('project-info-form');
    const saveButton = document.getElementById('save-button');

    // Load data from localStorage
    form.querySelectorAll('input').forEach(input => {
        const storedValue = localStorage.getItem(input.id);
        if (storedValue) input.value = storedValue;
    });

    // Save data to localStorage and server
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Save to localStorage
        form.querySelectorAll('input').forEach(input => {
            localStorage.setItem(input.id, input.value);
        });

        // Save to server
        const formData = new FormData(form);
        fetch('/project_info', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message || 'Project info saved successfully!');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error saving project info.');
        });
    });
});
</script>
{% endblock %}
