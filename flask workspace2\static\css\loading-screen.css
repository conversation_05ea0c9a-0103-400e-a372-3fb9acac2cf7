/**
 * Loading Screen Styles for GRS Application
 * Provides smooth loading animation during AJAX navigation
 */

/* Loading Screen Container */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.loading-screen.show {
    opacity: 1;
}

/* Loading Content Container */
.loading-content {
    text-align: center;
    color: #333333;
    max-width: 300px;
    padding: 2rem;
}

/* Loading Spinner */
.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(51, 51, 51, 0.2);
    border-top: 4px solid #007bff;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading Text */
.loading-text {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.loading-subtext {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 300;
}

/* Pulse Animation for Text */
.loading-text {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Alternative Spinner Styles (can be used for variety) */
.loading-spinner.dots {
    width: auto;
    height: auto;
    border: none;
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.loading-spinner.dots::before,
.loading-spinner.dots::after,
.loading-spinner.dots {
    content: '';
    width: 12px;
    height: 12px;
    background: #007bff;
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-spinner.dots::before {
    animation-delay: -0.32s;
}

.loading-spinner.dots::after {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .loading-content {
        padding: 1.5rem;
        max-width: 250px;
    }
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        margin-bottom: 1rem;
    }
    
    .loading-text {
        font-size: 1.25rem;
    }
    
    .loading-subtext {
        font-size: 0.9rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .loading-screen {
        background: rgba(255, 255, 255, 0.95);
    }

    .loading-spinner {
        border-color: rgba(51, 51, 51, 0.3);
        border-top-color: #007bff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .loading-spinner {
        animation: none;
        border: 4px solid rgba(51, 51, 51, 0.2);
        border-left-color: #007bff;
    }

    .loading-text {
        animation: none;
    }

    .loading-screen {
        transition: none;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .loading-screen {
        background: rgba(40, 40, 40, 0.95);
    }

    .loading-content {
        color: #ffffff;
    }

    .loading-spinner {
        border-color: rgba(255, 255, 255, 0.2);
        border-top-color: #007bff;
    }
}
