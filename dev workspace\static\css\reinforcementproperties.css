/* General Container Styling */
#reinforcementproperties-container {
    width: 95%;  /* Increased width to utilize available space */
    max-width: 2000px; /* Reasonable maximum width */
    margin: 20px auto; /* Center the container */
    padding: 20px;
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

/* Form Styling */
#reinforcementproperties-form {
    width: 130%; /* Form takes the full width of its container */
    max-width: none; /* Remove max-width to allow expansion */
    margin: 0; /* Reset margins */
    padding: 0; /* Reset padding */
    box-sizing: border-box;
}

/* Table Container Styling */
.table-responsive {
    overflow-x: auto; /* Enable horizontal scrolling if the table exceeds the container width */
    border-radius: 8px;
    background: #f8f9fa;
    padding: 15px;
    box-sizing: border-box;
}

/* Table Styling */
#reinforcementTable {
    width: 100%; /* Table spans full width */
    border-collapse: collapse; /* Collapses the borders into a single border */
    table-layout: fixed; /* Ensure that the columns widths remain the same */
}

#reinforcementTable th,
#reinforcementTable td {
    text-align: center;
    padding: 8px;
    border: 1px solid #ddd;
    box-sizing: border-box; /* Ensures padding and border are included in the element's total width and height */
}

#reinforcementTable thead {
    background-color: #e3f2fd;
    font-weight: bold;
}

/* Input Fields Styling */
#reinforcementTable input[type="text"],
#reinforcementTable input[type="number"] {
    width: 100%;
    padding: 6px;
    border: 2px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
    box-sizing: border-box; /* Ensures padding and border are included */
}

#reinforcementTable input[type="number"]:focus,
#reinforcementTable input[type="text"]:focus {
    border: 2px solid #007bff;
    background-color: #ffffff;
}

/* Adjust width of specific columns */
#reinforcementTable th:nth-child(1),   /* Type ID */
#reinforcementTable td:nth-child(1)   { width: 80px;  }

#reinforcementTable th:nth-child(2),   /* Name */
#reinforcementTable td:nth-child(2)   { width: 120px; }

#reinforcementTable th:nth-child(3),   /* Tult (kN/m) */
#reinforcementTable td:nth-child(3)   { width: 100px; }

#reinforcementTable th:nth-child(4),   /* RF ID */
#reinforcementTable td:nth-child(4)   { width: 80px;  }

#reinforcementTable th:nth-child(5),   /* RF W */
#reinforcementTable td:nth-child(5)   { width: 80px;  }

#reinforcementTable th:nth-child(6),   /* RF CR */
#reinforcementTable td:nth-child(6)   { width: 80px;  }

#reinforcementTable th:nth-child(7),   /* FS */
#reinforcementTable td:nth-child(7)   { width: 60px;  }

#reinforcementTable th:nth-child(8),   /* Pullout Angle */
#reinforcementTable td:nth-child(8)   { width: 110px; }

#reinforcementTable th:nth-child(9),   /* Sliding Angle */
#reinforcementTable td:nth-child(9)   { width: 110px; }

#reinforcementTable th:nth-child(10),  /* Scale Factor */
#reinforcementTable td:nth-child(10)  { width: 90px;  }

#reinforcementTable th:nth-child(11),  /* Remove Button */
#reinforcementTable td:nth-child(11)  { width: 80px;  }


/* Buttons Styling */
.btn-add {
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
    display: block;
    margin: 20px auto;
}

.btn-add:hover {
    background-color: #218838;
}

.btn-remove {
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
}

.btn-remove:hover {
    background-color: #c82333;
}

/* Media Query for Smaller Screens */
@media (max-width: 768px) {
    #reinforcementproperties-container {
        width: 100%;  /* Take full width on smaller screens */
        padding: 10px;
    }

    #reinforcementTable th,
    #reinforcementTable td {
        font-size: 12px;  /* Reduce font size */
        padding: 6px;
    }
}

