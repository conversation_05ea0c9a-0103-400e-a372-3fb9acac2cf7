document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById("reinforcedsoil-form");

    // Load saved data (if available)
    if (localStorage.getItem("soil_density")) {
        document.getElementById("reinforced-density").value = localStorage.getItem("soil_density");
    }
    if (localStorage.getItem("friction_angle")) {
        document.getElementById("reinforced-friction").value = localStorage.getItem("friction_angle");
    }
    if (localStorage.getItem("cohesion")) {
        document.getElementById("reinforced-cohesion").value = localStorage.getItem("cohesion");
    }

    // Save data when input changes
    form.addEventListener("input", function() {
        localStorage.setItem("soil_density", document.getElementById("reinforced-density").value);
        localStorage.setItem("friction_angle", document.getElementById("reinforced-friction").value);
        localStorage.setItem("cohesion", document.getElementById("reinforced-cohesion").value);
    });

    // Save form data asynchronously with AJAX
    form.addEventListener("submit", function(e) {
        e.preventDefault();  // Prevent normal form submission

        const formData = new FormData(form);

        // Send the data to the server using AJAX
        fetch('/reinforcedsoil', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);  // Show a success message
            // Optionally, you can clear localStorage after successful save
            // localStorage.removeItem("soil_density");
            // localStorage.removeItem("friction_angle");
            // localStorage.removeItem("cohesion");
        })
        .catch(error => {
            console.error('Error:', error);
            alert('There was an error with the submission.');
        });
    });
});