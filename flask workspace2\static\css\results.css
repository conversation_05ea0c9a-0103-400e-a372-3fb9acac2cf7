/* Consolidated Results Styles - Used by both external and internal stability results */

/* Note: Body styles removed to avoid conflicts with base.html styles */

h2, h3 {
    color: #333;
}

.card {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #007bff;
    color: white;
    padding: 10px;
    border-bottom: 1px solid #ddd;
}

.card-body {
    padding: 20px;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.text-danger {
    color: #dc3545;
}

.text-success {
    color: #28a745;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.btn {
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    text-decoration: none;
    color: white;
    background-color: #6c757d;
    border: none;
    cursor: pointer;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Results container styles */
.results-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.result-item {
    flex: 1 1 300px; /* Each item takes up at least 300px or 1/3 of the container */
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.result-item strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

.result-item span,
.result-item pre {
    display: block;
    word-break: break-word; /* Prevents long words from breaking the layout */
    white-space: pre-wrap; /* Preserves formatting and line breaks */
    color: #555;
}

/* Optional: Style for pre elements to make them more readable */
.result-item pre {
    background-color: #eee;
    padding: 5px;
    border-radius: 3px;
    overflow-x: auto; /* Add horizontal scroll for long content */
}

/* Enhanced mobile responsive styling */
@media (max-width: 992px) {
    .results-container {
        gap: 15px;
    }

    .result-item {
        flex: 1 1 calc(50% - 10px); /* Two columns on tablets */
    }
}

@media (max-width: 768px) {
    .results-container {
        gap: 12px;
        margin-top: 15px;
    }

    .result-item {
        flex: 1 1 100%; /* Full width on mobile */
        padding: 15px;
        font-size: 16px;
    }

    .result-item strong {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .result-item pre {
        padding: 8px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .results-container {
        gap: 10px;
        margin-top: 10px;
    }

    .result-item {
        padding: 12px;
    }

    .result-item strong {
        font-size: 16px;
    }
}
