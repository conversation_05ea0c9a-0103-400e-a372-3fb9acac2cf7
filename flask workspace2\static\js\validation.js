/**
 * Centralized Validation Utility for GRS Application
 * Provides consistent validation logic across all forms
 */

// Common validation rules
const VALIDATION_RULES = {
    // Geometry validation rules
    'wall-height': {
        test: (value) => value > 0,
        message: "Wall height must be greater than 0"
    },
    'embedment-depth': {
        test: (value) => value >= 0,
        message: "Embedment depth cannot be negative"
    },
    'wall-length': {
        test: (value) => value > 0,
        message: "Reinforcement length must be greater than 0"
    },
    'wall-batter': {
        test: (value) => value >= 0 && value <= 90,
        message: "Wall batter must be between 0 and 90 degrees"
    },
    'backslope-angle': {
        test: (value) => value >= 0 && value <= 90,
        message: "Backslope angle must be between 0 and 90 degrees"
    },
    'backslope-rise': {
        test: (value) => value >= 0,
        message: "Backslope rise cannot be negative"
    },

    // Soil properties validation rules
    'reinforced-density': {
        test: (value) => value > 0,
        message: "Soil density must be greater than 0"
    },
    'reinforced-friction': {
        test: (value) => value >= 0 && value <= 90,
        message: "Friction angle must be between 0 and 90 degrees"
    },
    'reinforced-cohesion': {
        test: (value) => value >= 0,
        message: "Cohesion cannot be negative"
    },
    'retained-density': {
        test: (value) => value > 0,
        message: "Soil density must be greater than 0"
    },
    'retained-friction': {
        test: (value) => value >= 0 && value <= 90,
        message: "Friction angle must be between 0 and 90 degrees"
    },
    'retained-cohesion': {
        test: (value) => value >= 0,
        message: "Cohesion cannot be negative"
    },

    // Foundation soil validation rules
    'foundation-density': {
        test: (value) => value > 0,
        message: "Foundation soil density must be greater than 0"
    },
    'foundation-friction': {
        test: (value) => value >= 0 && value <= 90,
        message: "Foundation friction angle must be between 0 and 90 degrees"
    },
    'foundation-cohesion': {
        test: (value) => value >= 0,
        message: "Foundation cohesion cannot be negative"
    },
    'foundation-bearing': {
        test: (value) => value > 0,
        message: "Bearing capacity must be greater than 0"
    },

    // Project info validation rules
    'project-name': {
        test: (value) => value.trim() !== "",
        message: "Project name is required"
    },
    'client-name': {
        test: (value) => value.trim() !== "",
        message: "Client name is required"
    },
    'designer-name': {
        test: (value) => value.trim() !== "",
        message: "Designer name is required"
    },
    'project-number': {
        test: (value) => value.trim() !== "",
        message: "Project number is required"
    },
    'revision': {
        test: (value) => value !== "" && !isNaN(value) && parseInt(value) >= 0,
        message: "Revision must be a non-negative number"
    },

    // External loads validation rules (basic numeric validation)
    'dead_load1': {
        test: (value) => value >= 0,
        message: "Dead load must be non-negative"
    },
    'dead_load2': {
        test: (value) => value >= 0,
        message: "Dead load must be non-negative"
    },
    'dead_load3': {
        test: (value) => value >= 0,
        message: "Dead load must be non-negative"
    },
    'live_load1': {
        test: (value) => value >= 0,
        message: "Live load must be non-negative"
    },
    'live_load2': {
        test: (value) => value >= 0,
        message: "Live load must be non-negative"
    },
    'live_load3': {
        test: (value) => value >= 0,
        message: "Live load must be non-negative"
    }
};

/**
 * Validate a single field using centralized rules
 * @param {HTMLElement} input - The input element to validate
 * @returns {boolean} - True if valid, false if invalid
 */
function validateField(input) {
    const fieldId = input.id;
    const errorElement = document.getElementById(`${fieldId}-error`);
    const validation = VALIDATION_RULES[fieldId];
    
    if (!validation) {
        // No validation rule defined - consider it valid
        if (errorElement) {
            errorElement.textContent = "";
        }
        input.classList.remove("invalid");
        return true;
    }

    const value = input.type === 'number' ? parseFloat(input.value) : input.value;
    
    // Check for empty required fields
    if (input.hasAttribute('required') && (input.value.trim() === "" || (input.type === 'number' && isNaN(value)))) {
        if (errorElement) {
            errorElement.textContent = "This field is required";
        }
        input.classList.add("invalid");
        return false;
    }
    
    // Apply validation rule
    if (input.type === 'number' && isNaN(value)) {
        if (errorElement) {
            errorElement.textContent = "Please enter a valid number";
        }
        input.classList.add("invalid");
        return false;
    }
    
    if (!validation.test(value)) {
        if (errorElement) {
            errorElement.textContent = validation.message;
        }
        input.classList.add("invalid");
        return false;
    }
    
    // Field is valid
    if (errorElement) {
        errorElement.textContent = "";
    }
    input.classList.remove("invalid");
    return true;
}

/**
 * Validate an entire form
 * @param {HTMLElement} form - The form element to validate
 * @returns {boolean} - True if all fields are valid, false otherwise
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Add validation listeners to a form
 * @param {HTMLElement} form - The form element
 */
function addValidationListeners(form) {
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        // Only add listeners if not already added
        if (!input.hasAttribute('data-validation-attached')) {
            input.addEventListener('input', () => validateField(input));
            input.addEventListener('blur', () => validateField(input));
            input.setAttribute('data-validation-attached', 'true');
        }
    });
}

// Make functions available globally
window.validateField = validateField;
window.validateForm = validateForm;
window.addValidationListeners = addValidationListeners;
window.VALIDATION_RULES = VALIDATION_RULES;

console.log("✅ Centralized validation utility loaded");
