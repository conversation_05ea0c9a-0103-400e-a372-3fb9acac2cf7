from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash, Response, render_template, send_file, make_response
from flask_login import Lo<PERSON><PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from flask_mysqldb import MySQL
import math
from backend import calculate_pressure
import logging
import pdfkit
import os
from datetime import datetime
from weasyprint import HTML


app = Flask(__name__)
app.secret_key = 'YourSecretKeyHere'  # VERY IMPORTANT! Replace with a real secret key.

# MySQL configuration
app.config['MYSQL_HOST'] = 'localhost'
app.config['MYSQL_USER'] = 'root'
app.config['MYSQL_PASSWORD'] = 'YourPasswordHere'
app.config['MYSQL_DB'] = 'grs_software'

mysql = MySQL(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# User class
class User(UserMixin):
    def __init__(self, id, username):
        self.id = id
        self.username = username

# User loader function
@login_manager.user_loader
def load_user(user_id):
    cur = mysql.connection.cursor()
    cur.execute("SELECT id, username FROM users WHERE id = %s", (user_id,))
    user = cur.fetchone()
    cur.close()
    if user:
        return User(id=user[0], username=user[1])
    return None

@app.route('/')
@login_required
def home():
    return render_template('home.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        cur = mysql.connection.cursor()
        cur.execute("SELECT id, password FROM users WHERE username = %s", (username,))
        user = cur.fetchone()
        cur.close()
        if user and user[1] == password:  # Replace with hashed password check in production
            user_obj = User(id=user[0], username=username)
            login_user(user_obj)
            flash('Logged in successfully.', 'success')
            return redirect(url_for('home'))
        else:
            flash('Invalid username or password.', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out.', 'success')
    return redirect(url_for('login'))


@app.route('/project_info', methods=['GET', 'POST'])
@login_required
def project_info():
    if request.method == 'POST':
        # Process form data
        project_name = request.form.get('project_name')
        project_id = request.form.get('project_id')
        designer = request.form.get('designer')
        client = request.form.get('client')
        description = request.form.get('description')
        date = request.form.get('date')
        revision = request.form.get('revision')
        
        # Save data to session
        session['project_name'] = project_name
        session['project_id'] = project_id
        session['designer'] = designer
        session['client'] = client
        session['description'] = description
        session['date'] = date
        session['revision'] = revision
        
        flash('Project info saved successfully', 'success')
        return jsonify({'message': 'Project info saved successfully!'})

    # Handle GET request
    return render_template('project_info.html',
                           project_name=session.get('project_name', ''),
                           project_id=session.get('project_id', ''),
                           designer=session.get('designer', ''),
                           client=session.get('client', ''),
                           description=session.get('description', ''),
                           date=session.get('date', ''),
                           revision=session.get('revision', ''))


@app.route('/geometry', methods=['GET', 'POST'])
@login_required
def geometry():
    if request.method == 'POST':
        # Getting the form data from the request
        wall_height = request.form.get('wall_height')
        embedment_depth = request.form.get('embedment_depth')
        wall_length = request.form.get('wall_length')
        wall_batter = request.form.get('wall_batter')
        backslope_angle = request.form.get('backslope_angle')
        backslope_rise = request.form.get('backslope_rise')

        # Save the data to the session
        session['wall_height'] = wall_height
        session['embedment_depth'] = embedment_depth
        session['wall_length'] = wall_length
        session['wall_batter'] = wall_batter
        session['backslope_angle'] = backslope_angle
        session['backslope_rise'] = backslope_rise
        session['geometry_data_saved'] = True

        flash('Geometry data has been saved!', 'success')
        return jsonify({'message': 'Geometry data saved successfully!'})

    # Handle GET request to display the form with data from the session
    return render_template('geometry.html',
                           wall_height=session.get('wall_height', ''),
                           embedment_depth=session.get('embedment_depth', ''),
                           wall_length=session.get('wall_length', ''),
                           wall_batter=session.get('wall_batter', ''),
                           backslope_angle=session.get('backslope_angle', ''),
                           backslope_rise=session.get('backslope_rise', ''))


@app.route('/reinforcedsoil', methods=['GET', 'POST'])
@login_required
def reinforcedsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        soil_density = request.form.get('soil_density')
        friction_angle = request.form.get('friction_angle')
        cohesion = request.form.get('cohesion')
        
        # Save the data to the session (or to a database, if needed)
        session['soil_density'] = soil_density
        session['friction_angle'] = friction_angle
        session['cohesion'] = cohesion
        session['reinforcedsoil_data_saved'] = True
        
        # Set a message that data has been saved
        flash('Reinforced soil data has been saved!', 'success')
        # Return a response (can return a message or redirect)
        return redirect(url_for('reinforcedsoil'))
        
    # Handle GET request to display the form with data from session
    return render_template('reinforcedsoil.html', 
                           soil_density=session.get('soil_density', ''),
                           friction_angle=session.get('friction_angle', ''),
                           cohesion=session.get('cohesion', ''))

@app.route('/retainedsoil', methods=['GET', 'POST'])
@login_required
def retainedsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        retainedsoil_density = request.form.get('retainedsoil_density')
        retainedfriction_angle = request.form.get('retainedfriction_angle')
        retainedcohesion = request.form.get('retainedcohesion')
        
        # Save the data to the session (or to a database, if needed)
        session['retainedsoil_density'] = retainedsoil_density
        session['retainedfriction_angle'] = retainedfriction_angle
        session['retainedcohesion'] = retainedcohesion
        session['retainedsoil_data_saved'] = True
        
        # Set a message that data has been saved
        flash('Retained soil data has been saved!', 'success')
        # Return a response (can return a message or redirect)
        return redirect(url_for('retainedsoil'))
        
    # Handle GET request to display the form with data from session
    return render_template('retainedsoil.html', 
                           retainedsoil_density=session.get('retainedsoil_density', ''),
                           retainedfriction_angle=session.get('retainedfriction_angle', ''),
                           retainedcohesion=session.get('retainedcohesion', ''))

@app.route('/foundationsoil', methods=['GET', 'POST'])
@login_required
def foundationsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        foundationsoildensity = request.form.get('foundationsoildensity')
        foundationsoilfriction_angle = request.form.get('foundationsoilfriction_angle')
        foundationsoilcohesion = request.form.get('foundationsoilcohesion')
        eccentricity = request.form.get('eccentricity')
        eccentricity_seismic = request.form.get('eccentricity_seismic')
        watertable = request.form.get('watertable')

        # Save the data to the session (or to a database, if needed)
        session['foundationsoildensity'] = foundationsoildensity
        session['foundationsoilfriction_angle'] = foundationsoilfriction_angle
        session['foundationsoilcohesion'] = foundationsoilcohesion
        session['eccentricity'] = eccentricity
        session['eccentricity_seismic'] = eccentricity_seismic
        session['watertable'] = watertable
        session['foundationsoil_data_saved'] = True

        # Set a message that data has been saved
        flash('Foundation soil data has been saved!', 'success')
        
        # Return a response (redirect back to the same page)
        return redirect(url_for('foundationsoil'))

    # Handle GET request to display the form with session data
    return render_template('foundationsoil.html', 
                           density=session.get('foundationsoildensity', ''),
                           friction_angle=session.get('foundationsoilfriction_angle', ''),
                           cohesion=session.get('foundationsoilcohesion', ''),
                           eccentricity=session.get('eccentricity', ''),
                           eccentricity_seismic=session.get('eccentricity_seismic', ''),
                           watertable=session.get('watertable', ''))


@app.route('/externalloads', methods=['GET', 'POST'])
@login_required
def externalloads():
    # Default values for form fields
    default_strip_load = [""] * 3
    default_impact_loads = {
        "rupture": {"upper": "", "second": ""},
        "pullout": {"upper": "", "second": ""}
    }

    if request.method == 'POST':
        try:
            # Getting form data
            dead_loads = [request.form.get(f'dead_load{i}', '') for i in range(1, 4)]
            live_loads = [request.form.get(f'live_load{i}', '') for i in range(1, 4)]
            vertical_strip_load = [request.form.get(f'vertical_strip_load{i}', '') for i in range(1, 4)]
            horizontal_strip_load = [request.form.get(f'horizontal_strip_load{i}', '') for i in range(1, 4)]
            strip_load_width = [request.form.get(f'strip_load_width{i}', '') for i in range(1, 4)]
            strip_load_distance = [request.form.get(f'strip_load_distance{i}', '') for i in range(1, 4)]
            earthquake_acceleration = request.form.get('earthquake_acceleration', '')
            use_direct_kh = request.form.get('use_direct_kh', False)
            seismic_force = request.form.get('seismic_force', '')

            impact_loads = {
                "rupture": {
                    "upper": request.form.get('rupture_impact_upper', ''),
                    "second": request.form.get('rupture_impact_second', '')
                },
                "pullout": {
                    "upper": request.form.get('pullout_impact_upper', ''),
                    "second": request.form.get('pullout_impact_second', '')
                }
            }

            # Convert to floats
            dead_loads = [float(x) if x else 0 for x in dead_loads]
            live_loads = [float(x) if x else 0 for x in live_loads]
            vertical_strip_load = [float(x) if x else 0 for x in vertical_strip_load]
            horizontal_strip_load = [float(x) if x else 0 for x in horizontal_strip_load]
            strip_load_width = [float(x) if x else 0 for x in strip_load_width]
            strip_load_distance = [float(x) if x else 0 for x in strip_load_distance]
            earthquake_acceleration = float(earthquake_acceleration) if earthquake_acceleration else 0
            seismic_force = float(seismic_force) if seismic_force and use_direct_kh else 0
            impact_loads = {
                "rupture": {
                    "upper": float(impact_loads["rupture"]["upper"]) if impact_loads["rupture"]["upper"] else 0,
                    "second": float(impact_loads["rupture"]["second"]) if impact_loads["rupture"]["second"] else 0
                },
                "pullout": {
                    "upper": float(impact_loads["pullout"]["upper"]) if impact_loads["pullout"]["upper"] else 0,
                    "second": float(impact_loads["pullout"]["second"]) if impact_loads["pullout"]["second"] else 0
                }
            }

            # Debugging: Print the actual values being stored
            logging.debug(f"Dead Loads: {dead_loads}")
            logging.debug(f"Live Loads: {live_loads}")
            logging.debug(f"Strip Loads: {vertical_strip_load}, {horizontal_strip_load}, {strip_load_width}, {strip_load_distance}")
            logging.debug(f"Earthquake Acceleration: {earthquake_acceleration}, Seismic Force: {seismic_force}")
            logging.debug(f"Impact Loads: {impact_loads}")

            # Store data in session
            session['externalloads_data'] = {
                'dead_loads': dead_loads,
                'live_loads': live_loads,
                'vertical_strip_load': vertical_strip_load,
                'horizontal_strip_load': horizontal_strip_load,
                'strip_load_width': strip_load_width,
                'strip_load_distance': strip_load_distance,
                'earthquake_acceleration': earthquake_acceleration,
                'seismic_force': seismic_force,
                'impact_loads': impact_loads
            }
            session.modified = True

            # Debugging: Print the session data for verification
            logging.debug(f"Session after saving external loads: {session.get('externalloads_data')}")

            return jsonify({'message': 'External loads data has been saved!'})
        except Exception as e:
            logging.error(f"Error saving external loads data: {str(e)}")
            return jsonify({'message': f"Error saving external loads data: {str(e)}"}), 400

    # Load data from session
    externalloads_data = session.get('externalloads_data', {})
    default_strip_load = [""] * 3
    default_impact_loads = {
        "rupture": {"upper": "", "second": ""},
        "pullout": {"upper": "", "second": ""}
    }

    # Provide session info
    return render_template('externalloads.html',
                           dead_loads=externalloads_data.get('dead_loads', [""] * 3),
                           live_loads=externalloads_data.get('live_loads', [""] * 3),
                           vertical_strip_load=externalloads_data.get('vertical_strip_load', default_strip_load),
                           horizontal_strip_load=externalloads_data.get('horizontal_strip_load', default_strip_load),
                           strip_load_width=externalloads_data.get('strip_load_width', default_strip_load),
                           strip_load_distance=externalloads_data.get('strip_load_distance', default_strip_load),
                           earthquake_acceleration=externalloads_data.get('earthquake_acceleration', ''),
                           seismic_force=externalloads_data.get('seismic_force', ''),
                           impact_loads=externalloads_data.get('impact_loads', default_impact_loads))



@app.route('/reinforcementproperties', methods=['GET', 'POST'])
@login_required
def reinforcementproperties():
    if request.method == 'POST':
        try:
            # Extract multiple reinforcement rows
            reinforcement_data = []
            row_count = int(request.form.get('row_count', 0))  # Get number of rows

            for i in range(row_count):
                reinforcement_data.append({
                    'type_id': request.form.get(f'type_id_{i}', ''),  # Providing default
                    'name': request.form.get(f'name_{i}', ''),    # defaults to prevent
                    'tult': request.form.get(f'tult_{i}', ''),    # key errors
                    'rfid': request.form.get(f'rfid_{i}', ''),
                    'rfw': request.form.get(f'rfw_{i}', ''),
                    'rfcr': request.form.get(f'rfcr_{i}', ''),
                    'fs': request.form.get(f'fs_{i}', ''),
                    'pullout_angle': request.form.get(f'pullout_angle_{i}', ''),
                    'sliding_angle': request.form.get(f'sliding_angle_{i}', ''),
                    'scale_factor': request.form.get(f'scale_factor_{i}', '')
                })

            # Save in session to retain data
            session['reinforcement_data'] = reinforcement_data
            session['reinforcementproperties_data_saved'] = True

            flash('Reinforcement properties saved successfully!', 'success')
            return redirect(url_for('reinforcementproperties'))

        except Exception as e:
            flash(f"Error saving reinforcement properties data: {str(e)}", 'error')  # Flash specific error message
            return redirect(url_for('reinforcementproperties'))

    # Retrieve stored reinforcement data
    reinforcement_data = session.get('reinforcement_data', [])

    return render_template('reinforcementproperties.html', reinforcement_data=reinforcement_data)



@app.route('/reinforcementlayout', methods=['GET', 'POST'])
@login_required
def reinforcementlayout():
    if request.method == 'POST':
        # Process the data submitted by the form
        data = request.get_json()
        session['reinforcement_layout_data'] = data # Save the data to the session
        session['reinforcementlayout_data_saved'] = True

        flash('Reinforcement layout data saved successfully!', 'success')
        return jsonify({'message': 'Reinforcement layout data saved successfully!'})

    # Handle GET request to display the form
    return render_template('reinforcementlayout.html')


@app.route('/reinforcementlayout/data', methods=['GET'])
@login_required
def reinforcementlayout_data():
    # Retrieve the data from the session (or database)
    data = session.get('reinforcement_layout_data', [])

    return jsonify(data)


@app.context_processor
def inject_reinforcement_data():
    reinforcement_data = session.get('reinforcement_data', [])
    return dict(reinforcement_data=reinforcement_data)

@app.route('/run_analysis', methods=['POST'])
@login_required
def run_analysis():
    required_data = [
        'project_name', 'wall_height', 'embedment_depth', 'wall_length', 'wall_batter',
        'backslope_angle', 'backslope_rise', 'soil_density', 'friction_angle', 'cohesion',
        'retainedsoil_density', 'retainedfriction_angle', 'retainedcohesion',
        'foundationsoildensity', 'foundationsoilfriction_angle', 'foundationsoilcohesion',
        'externalloads_data',
        'reinforcement_data', 'reinforcement_layout_data'
    ]

    # Check for missing required data
    input_values = {key: session.get(key) for key in required_data}
    missing_data = [key for key in required_data if not session.get(key)]
    print("Input values used for calculation:", input_values)

    if missing_data:
        logging.error(f"Missing required data in session: {missing_data}")
        return jsonify({'error': 'Missing required data in session', 'missing_fields': missing_data}), 400

    # Validate externalloads_data
    externalloads_data = session.get('externalloads_data')
    if not externalloads_data:
        logging.error("Missing externalloads_data in session")
        return jsonify({'error': 'Missing externalloads_data', 'missing_fields': ['externalloads_data']}), 400

    external_loads_keys = [
        'dead_loads', 'live_loads', 'vertical_strip_load', 'horizontal_strip_load',
        'strip_load_width', 'strip_load_distance', 'earthquake_acceleration',
        'seismic_force', 'impact_loads'
    ]

    for key in external_loads_keys:
        if key not in externalloads_data:
            logging.error(f"Missing key {key} in externalloads_data")
            return jsonify({'error': f'Missing key {key} in externalloads_data', 'missing_fields': [key]}), 400

        # Special handling for impact_loads
        if key == 'impact_loads':
            impact_loads = externalloads_data[key]
            if not isinstance(impact_loads, dict):
                logging.error(f"impact_loads must be a dictionary")
                return jsonify({'error': 'impact_loads must be a dictionary', 'missing_fields': [key]}), 400

            # Validate nested structure
            for load_type in ['pullout', 'rupture']:
                if load_type not in impact_loads:
                    logging.error(f"Missing load type {load_type} in impact_loads")
                    return jsonify({'error': f'Missing load type {load_type} in impact_loads', 'missing_fields': [key]}), 400

                for layer in ['upper', 'second']:
                    if layer not in impact_loads[load_type]:
                        logging.error(f"Missing layer {layer} in impact_loads[{load_type}]")
                        return jsonify({'error': f'Missing layer {layer} in impact_loads[{load_type}]', 'missing_fields': [key]}), 400

                    value = impact_loads[load_type][layer]
                    if not isinstance(value, (int, float)):
                        logging.error(f"Non-numeric value in impact_loads[{load_type}][{layer}]")
                        return jsonify({'error': f'Non-numeric value in impact_loads[{load_type}][{layer}]', 'missing_fields': [key]}), 400
        else:
            # For other keys (lists or single numeric values)
            if isinstance(externalloads_data[key], list):
                if len(externalloads_data[key]) != 3:  # Assuming all lists should have 3 elements
                    logging.error(f"Invalid length for {key} in externalloads_data")
                    return jsonify({'error': f'Invalid length for {key}', 'missing_fields': [key]}), 400

                # Check if all elements are numeric (including 0.0)
                if not all(isinstance(x, (int, float)) for x in externalloads_data[key]):
                    logging.error(f"Non-numeric values in {key}")
                    return jsonify({'error': f'Non-numeric values in {key}', 'missing_fields': [key]}), 400
            else:
                # For non-list values (e.g., earthquake_acceleration, seismic_force)
                if not isinstance(externalloads_data[key], (int, float)):
                    logging.error(f"Non-numeric value for {key}")
                    return jsonify({'error': f'Non-numeric value for {key}', 'missing_fields': [key]}), 400

    # Run the analysis using backend.py
    results = calculate_pressure(session)
    if results is not None and isinstance(results, Response):
        # Store the response content in the session
        session['analysis_results'] = results.get_json()
        return jsonify({
            'message': 'Analysis completed successfully',
            'has_results': True
        })
    else:
        return jsonify({
            'error': 'Error occurred during calculation',
            'has_results': False
        }), 400
    


@app.route('/external_stability_results')
@login_required
def external_stability_results():
    results = session.get('analysis_results')
    if not results:
        return redirect(url_for('home'))
    return render_template('external_stability_results.html', results=results)

@app.route('/internal_stability_results')
@login_required
def internal_stability_results():
    results = session.get('analysis_results')
    if not results:
        return redirect(url_for('home'))
    return render_template('internal_stability_results.html', results=results)






@app.route('/generate_report')
@login_required
def generate_report():
    project_info = {
        'project_name': session.get('project_name', ''),
        'project_id': session.get('project_id', ''),
        'designer': session.get('designer', ''),
        'client': session.get('client', ''),
        'date': session.get('date', ''),
        'description': session.get('description', ''),
        'revision': session.get('revision', ''),
    }
    geometry = {
        'wall_height': session.get('wall_height', ''),
        'wall_length': session.get('wall_length', ''),
        'wall_batter': session.get('wall_batter', ''),
        'backslope_angle': session.get('backslope_angle', ''),
        'backslope_rise': session.get('backslope_rise', ''),
        'embedment_depth': session.get('embedment_depth', ''),
    }
    soil_properties = {
        'reinforced_soil': {
            'density': session.get('soil_density', ''),
            'friction_angle': session.get('friction_angle', ''),
            'cohesion': session.get('cohesion', ''),
        },
        'retained_soil': {
            'density': session.get('retainedsoil_density', ''),
            'friction_angle': session.get('retainedfriction_angle', ''),
            'cohesion': session.get('retainedcohesion', ''),
        },
        'foundation_soil': {
            'density': session.get('foundationsoildensity', ''),
            'friction_angle': session.get('foundationsoilfriction_angle', ''),
            'cohesion': session.get('foundationsoilcohesion', ''),
            'eccentricity': session.get('eccentricity', ''),
            'eccentricity_seismic': session.get('eccentricity_seismic', ''),
            'watertable': session.get('watertable', ''),
        },
    }
    external_loads = session.get('externalloads_data', {})
    reinforcement_data = session.get('reinforcement_data', [])
    reinforcement_layout = session.get('reinforcement_layout_data', {})
    analysis_results = session.get('analysis_results', {})
    
    html = render_template('report.html',
                           project_info=project_info,
                           geometry=geometry,
                           soil_properties=soil_properties,
                           external_loads=external_loads,
                           reinforcement_data=reinforcement_data,
                           reinforcement_layout=reinforcement_layout,
                           external_stability=analysis_results.get('external_stability', {}),
                           internal_stability={
                               'Reinforcement Results': analysis_results.get('Reinforcement Results', []),
                               'EQ Reinforcement Results': analysis_results.get('EQ Reinforcement Results', []),
                           },
                           generation_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # Generate PDF using WeasyPrint
    pdf = HTML(string=html).write_pdf()
    
    response = make_response(pdf)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'attachment; filename=report.pdf'
    
    return response



if __name__ == '__main__':
    import json
    app.run(debug=True)
