import sys
import os
import random
from load_file import load_data
from save_file import save_data

from PySide6.QtWidgets import (
    QApplication,
    QWidget,
    QLabel,
    QLineEdit,
    QPushButton,
    QHBoxLayout,
    QVBoxLayout,
    QStackedLayout,
    QFormLayout,
    QSpinBox,
    QScrollArea,
    QGroupBox,
    QCheckBox,
    QTableWidget,
    QTableWidgetItem,
    QDoubleSpinBox,
    QRadioButton,  
    QPushButton,
    QMainWindow,  
    QMenuBar,
    QFileDialog,
    QGraphicsView, 
    QGraphicsScene, 
    QGraphicsRectItem,
    QGraphicsTextItem,
    QGraphicsPolygonItem,
    QGraphicsLineItem,
    QHeaderView, 
    QComboBox,
    QMessageBox,
    QAbstractItemView,
    QAbstractScrollArea
)
from PySide6.QtGui import QPixmap, QPen, QFont, QBrush, QColor, QPolygonF, QTextLength, QPainterPath, QPainter, QTextDocument, QTextCharFormat, QTextTableFormat, QTextCursor, QPageSize, QTextOption, QTextCursor, QTextBlockFormat

from PySide6.QtCore import Qt, QPointF, QFileInfo, QMarginsF
from PySide6.QtGui import QFont, QAction, QPainter, QBrush, QLinearGradient, QColor, Qt
from PySide6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
import math




from PySide6.QtWidgets import QMainWindow, QVBoxLayout, QLabel, QWidget, QTableWidget, QTableWidgetItem, QPushButton
from PySide6.QtGui import QColor, QFont
from PySide6.QtCore import Qt

class ResultsWindow(QMainWindow):
    def __init__(self, title, heading, tables=None, safety_texts=None, additional_texts=None, main_window=None, parent=None):
        super().__init__()
        self.setWindowTitle(title)
        self.main_window = main_window

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        heading_label = QLabel(heading)
        heading_label.setStyleSheet("color: blue; font-weight: bold;")
        heading_label.setFont(QFont('Arial', 16))
        heading_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(heading_label)

        # Adding tables and safety texts
        if tables:
            for table_widget, sub_heading, safety_text in tables:
                sub_heading_label = QLabel(sub_heading)
                layout.addWidget(sub_heading_label)
                layout.addWidget(table_widget)
                safety_label = QLabel(safety_text)
                layout.addWidget(safety_label)

        # Adding additional texts if provided
        if additional_texts:
            for text in additional_texts:
                text_label = QLabel(text)
                layout.addWidget(text_label)

        back_button = QPushButton("Back")
        back_button.clicked.connect(self.go_back)
        layout.addWidget(back_button)

        self.resize(1800, 1200)

    def go_back(self):
        self.close()
        if self.main_window:
            self.main_window.show()




class GRSWALLDESIGNER(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
        self.table_widget = None
       # self.safety_label = None  # Initialize safety_label attribute
        self.reinforcement_table_widget = None
        #self.heading_label = None
        self.reinforcement_types = [] 


    def zoom_in(self):
        self.graphics_view.scale(self.scale_factor, self.scale_factor)

    def zoom_out(self):
        self.graphics_view.scale(1 / self.scale_factor, 1 / self.scale_factor)

    def fit_to_window(self):
        self.graphics_view.fitInView(self.graphics_scene.sceneRect(), Qt.KeepAspectRatio)


    def init_ui(self):
        self.setWindowTitle("GEOSYNAPSE GRS WALL DESIGNER V1.0 make in India")
        self.resize(1200, 800)
        self.setWindowState(Qt.WindowMaximized)

        main_layout = QHBoxLayout()
        left_layout = QVBoxLayout()

        right_layout = QVBoxLayout()  # Create right layout for graphics view

        self.stacked_layout = QStackedLayout()

        self.main_widget = QWidget()
        self.main_layout = QVBoxLayout()

        # Initialize the QGraphicsView and QGraphicsScene
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_view.setMinimumSize(900, 850)  # Set minimum size for the graphics view

   #     right_layout.addWidget(self.graphics_view)

        # Zoom buttons
        zoom_in_button = QPushButton("Zoom In")
        zoom_out_button = QPushButton("Zoom Out")
        fit_to_window_button = QPushButton("Fit to Window")

                # Style the buttons for a modern look
        button_style = """
            QPushButton {
                background-color: #4CAF50; /* Pleasant green */
                border: 2px solid #388E3C; /* Darker green border */
                border-radius: 8px;
                color: white;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
                transition: background-color 0.3s, color 0.3s;
                min-width: 100px;
                max-width: 120px;
            }
            QPushButton:hover {
                background-color: #66BB6A; /* Lighter green on hover */
                color: #F5F5F5; /* Slightly lighter font */
            }
            QPushButton:pressed {
                background-color: #2E7D32; /* Dark green on press */
            }
        """
        zoom_in_button.setStyleSheet(button_style)
        zoom_out_button.setStyleSheet(button_style)
        fit_to_window_button.setStyleSheet(button_style)

        # Connect buttons to zoom methods
        zoom_in_button.clicked.connect(self.zoom_in)
        zoom_out_button.clicked.connect(self.zoom_out)
        fit_to_window_button.clicked.connect(self.fit_to_window)

        # Layout for zoom buttons
        button_layout = QHBoxLayout()
        button_layout.addWidget(zoom_in_button)
        button_layout.addWidget(zoom_out_button)
        button_layout.addWidget(fit_to_window_button)

        # Add the graphics view and buttons to the right layout
        right_layout.addWidget(self.graphics_view)
        right_layout.addLayout(button_layout)

        # Add left and right layouts to the main layout
        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)

        # Set initial scale factor
        self.scale_factor = 1.15

        # Placeholder call for geometry setup
        self.draw_generic_geometry()



        # Add a spacer item to push the button to the bottom of the layout
        right_layout.addStretch()

        self.visualize_button = QPushButton("Visualize Geometry")
        self.visualize_button.clicked.connect(self.draw_geometry)
        self.visualize_button.setStyleSheet("""
            QPushButton {
                background-color: #D3D3D3; /* Cement light color */
                border: 2px solid #555; /* Darker gray border */
                border-radius: 8px; /* Rounded corners */
                color: black; /* Font color */
                padding: 10px 20px; /* Padding inside the button */
                font-size: 14px; /* Larger font size */
                font-weight: bold;
                min-width: 150px; /* Set minimum width */
                max-width: 200px; /* Set maximum width */
            }
            QPushButton:hover {
                background-color: white;
                color: darkgreen; /* Font color on hover */
            }
        """)
        
        # Align the button within its layout
        visualize_button_container = QWidget()
        visualize_button_layout = QVBoxLayout()
        visualize_button_layout.addWidget(self.visualize_button)
        visualize_button_container.setLayout(visualize_button_layout)
        right_layout.addWidget(visualize_button_container, alignment=Qt.AlignCenter)

        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)

        self.setLayout(main_layout)
        self.draw_generic_geometry()



        # Define an interactive, modern style for the buttons
        button_style = """
            QPushButton {
                background-color: #f0f0f0; /* Soft cement color */
                border: 2px solid #a0a0a0; /* Slightly darker border */
                border-radius: 10px; /* Rounded corners for a modern look */
                color: #333; /* Text color */
                padding: 12px 28px; /* Padding for a larger button */
                font-size: 14px; /* Slightly larger font */
                font-weight: bold;
                letter-spacing: 0.5px; /* Small spacing for readability */
                transition: all 0.3s ease; /* Smooth transition effect */
                box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.2); /* Soft shadow for depth */
            }

            QPushButton:hover {
                background-color: #ffffff; /* Brighter background on hover */
                color: #006400; /* Dark green text on hover */
                border: 2px solid #006400; /* Dark green border on hover */
                box-shadow: 4px 4px 12px rgba(0, 100, 0, 0.3); /* Enhanced shadow on hover */
            }

            QPushButton:pressed {
                background-color: #e0e0e0; /* Slightly darker for pressed effect */
                box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.3); /* Inset shadow for pressed look */
                color: #003300; /* Darker green text on press */
                transform: scale(0.98); /* Slight scale effect on press */
            }
        """


        # Define a colorful, vibrant style for the buttons
        def create_button_style(background_color, hover_color, text_color):
            return f"""
                QPushButton {{
                    background-color: {background_color}; /* Custom background color */
                    border: 2px solid #888; /* Slightly darker border */
                    border-radius: 10px; /* Rounded corners */
                    color: {text_color}; /* Text color */
                    padding: 12px 28px; /* Padding for a larger button */
                    font-size: 14px; /* Larger font */
                    font-weight: bold;
                    letter-spacing: 0.5px;
                    transition: all 0.3s ease;
                    box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.2); /* Shadow for depth */
                }}
                
                QPushButton:hover {{
                    background-color: {hover_color}; /* Hover color */
                    color: #ffffff; /* White text on hover */
                    border: 2px solid {hover_color}; /* Border color changes on hover */
                    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3); /* Enhanced shadow on hover */
                }}
                
                QPushButton:pressed {{
                    background-color: #cccccc; /* Slightly darker on press */
                    box-shadow: inset 2px 2px 8px rgba(0, 0, 0, 0.4); /* Inset shadow for pressed look */
                    transform: scale(0.98);
                }}
            """

        # Assign unique colors to each button using the style generator
        self.projectinfo_button = QPushButton("Project Information")
        self.projectinfo_button.setStyleSheet(create_button_style("#FFB6C1", "#FF69B4", "#333"))  # Light pink
        self.projectinfo_button.clicked.connect(self.show_projectinfo_inputs)

        

        self.geometry_button = QPushButton("Geometry Inputs")
        self.geometry_button.setStyleSheet(create_button_style("#90EE90", "#32CD32", "#333"))  # Light green
        self.geometry_button.clicked.connect(self.show_geometry_inputs)

        self.reinforced_soil_button = QPushButton("Reinforced Soil Inputs")
        self.reinforced_soil_button.setStyleSheet(create_button_style("#FFA07A", "#FF4500", "#333"))  # Light coral
        self.reinforced_soil_button.clicked.connect(self.show_reinforced_soil_inputs)
        

        self.retained_soil_button = QPushButton("Retained Soil Inputs")
        self.retained_soil_button.setStyleSheet(create_button_style("#FFD700", "#FF8C00", "#333"))  # Gold
        self.retained_soil_button.clicked.connect(self.show_retained_soil_inputs)

        self.foundation_soil_button = QPushButton("Foundation Soil Inputs")
        self.foundation_soil_button.setStyleSheet(create_button_style("#AFEEEE", "#40E0D0", "#333"))  # Pale turquoise
        self.foundation_soil_button.clicked.connect(self.show_foundation_soil_inputs)

        self.external_loads_button = QPushButton("External Loads Inputs")
        self.external_loads_button.setStyleSheet(create_button_style("#FFDEAD", "#F4A460", "#333"))  # Navajo white
        self.external_loads_button.clicked.connect(self.show_external_loads_options)

        self.reinforcement_properties_button = QPushButton("Reinforcement Properties")
        self.reinforcement_properties_button.setStyleSheet(create_button_style("#DDA0DD", "#BA55D3", "#333"))  # Plum
        self.reinforcement_properties_button.clicked.connect(self.show_reinforcement_properties) 

        self.reinforcement_details_button = QPushButton("Reinforcement Details")
        self.reinforcement_details_button.setStyleSheet(create_button_style("#87CEFA", "#4682B4", "#333"))  # Light sky blue
        self.reinforcement_details_button.clicked.connect(self.show_reinforcement_details)

        self.results_external_button = QPushButton("External Stability Results")
        self.results_external_button.setStyleSheet(create_button_style("#FFB6C1", "#FF69B4", "#333"))  # Light pink
        self.results_external_button.clicked.connect(self.show_external_results)

        self.results_internal_button = QPushButton("Internal Stability Results")
        self.results_internal_button.setStyleSheet(create_button_style("#90EE90", "#32CD32", "#333"))  # Light green
        self.results_internal_button.clicked.connect(self.show_internal_results)

        # Add Generate Report button

        self.generate_report_button = QPushButton("Generate Report")
        self.generate_report_button.setStyleSheet(create_button_style("#FF6347", "#FF4500", "#FFFFFF"))  # Tomato
       # self.generate_report_button.clicked.connect(self.generate_report)
      #  right_layout.addWidget(self.generate_report_button, alignment=Qt.AlignCenter)


#############################################################
         # Add Save and Load buttons

        self.save_button = QPushButton("Save Data")
        self.save_button.setStyleSheet(create_button_style("#8A2BE2", "#7B68EE", "#FFFFFF"))  # Medium purple
        self.save_button.clicked.connect(lambda: save_data(self))

        self.load_button = QPushButton("Load Data")
        self.load_button.setStyleSheet(create_button_style("#20B2AA", "#008080", "#FFFFFF"))  # Light sea green
        self.load_button.clicked.connect(lambda: load_data(self))


        self.main_layout.addWidget(self.save_button)
        self.main_layout.addWidget(self.load_button)
##############################################################



        self.result_label = QLabel("")
        self.result2_label = QLabel("")
        self.result3_label = QLabel("")
        self.result4_label = QLabel("")


        self.main_layout.addWidget(self.projectinfo_button)
#        self.main_layout.addWidget(self.designmethod_button)
        self.main_layout.addWidget(self.geometry_button)
        self.main_layout.addWidget(self.reinforced_soil_button)
        self.main_layout.addWidget(self.retained_soil_button)
        self.main_layout.addWidget(self.foundation_soil_button)
        
        self.main_layout.addWidget(self.external_loads_button)

        self.main_layout.addWidget(self.reinforcement_properties_button)


        self.main_layout.addWidget(self.reinforcement_details_button)

 
        self.main_layout.addWidget(self.results_external_button)
        self.main_layout.addWidget(self.results_internal_button)
        self.main_layout.addWidget(self.generate_report_button)
   


        self.main_layout.addWidget(self.result_label)
        self.main_layout.addWidget(self.result2_label)
        self.main_layout.addWidget(self.result3_label)
        self.main_layout.addWidget(self.result4_label)

        self.main_widget.setLayout(self.main_layout)
        self.stacked_layout.addWidget(self.main_widget)

        
        self.create_projectinfo_inputs()
#        self.create_designmethod_inputs()
        self.create_geometry_inputs()
        self.create_reinforced_soil_inputs()
        self.create_retained_soil_inputs()
        self.create_foundation_soil_inputs()
        
        self.create_external_loads_options()
        self.create_dead_load_inputs()
        self.create_live_load_inputs()
        self.create_strip_load_inputs()
        self.create_impact_load_inputs()
        self.create_earthquake_load_inputs()

        self.create_reinforcement_properties_inputs()

        self.create_reinforcement_details_inputs()

        self.create_external_results_view()
        self.create_internal_results_view()
   
        


        left_layout.addLayout(self.stacked_layout)
      #  self.calculate_button = QPushButton("Calculate")
     #   self.calculate_button.setStyleSheet(button_style)
     #   self.calculate_button.clicked.connect(self.calculate_pressure)
      #  left_layout.addWidget(self.calculate_button)

        right_layout = QVBoxLayout()

       # self.image_label1 = QLabel()
        self.image_label2 = QLabel()

       # self.load_image(self.image_label1, "PHOTO3.png", 600, 1200)
        self.load_image(self.image_label2, "PHOTO2.jpg", 100, 600)

        #right_layout.addWidget(self.image_label1)
        right_layout.addWidget(self.image_label2)

        # Add layouts to the main layout
        main_layout.addLayout(left_layout)
        main_layout.addLayout(right_layout)

        self.setLayout(main_layout)

    def load_image(self, label, image_path, width, height):
        absolute_image_path = os.path.abspath(image_path)
        if os.path.exists(absolute_image_path):
            pixmap = QPixmap(absolute_image_path)
            if not pixmap.isNull():
                label.setPixmap(pixmap.scaled(width, height, Qt.KeepAspectRatio))
            else:
                label.setText(f"Failed to load image {image_path}")
        else:
            label.setText(f"Image {image_path} not found")



    def create_projectinfo_inputs(self):
        self.projectinfo_widget = QWidget()
        self.projectinfo_layout = QVBoxLayout()

        # Define the form layout and styles
        form_layout = QFormLayout()
        
        # Set styles for labels and input fields
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;  /* Dark gray for readability */
                font-weight: 600;  /* Slightly bold */
                padding: 3px 0;  /* Padding for spacing */
            }
        """

        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """

        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """

        # Project Name
        projectname_label = QLabel("Project Name:")
        projectname_label.setStyleSheet(main_label_style)
        self.projectname_input = QLineEdit()
        self.projectname_input.setStyleSheet(input_style)
        form_layout.addRow(projectname_label, self.projectname_input)

        # Project ID
        projectid_label = QLabel("Project ID:")
        projectid_label.setStyleSheet(main_label_style)
        self.projectid_input = QLineEdit()
        self.projectid_input.setStyleSheet(input_style)
        form_layout.addRow(projectid_label, self.projectid_input)

        # Designer
        designer_label = QLabel("Designer:")
        designer_label.setStyleSheet(main_label_style)
        self.designer_input = QLineEdit()
        self.designer_input.setStyleSheet(input_style)
        form_layout.addRow(designer_label, self.designer_input)

        # Client
        client_label = QLabel("Client:")
        client_label.setStyleSheet(main_label_style)
        self.client_input = QLineEdit()
        self.client_input.setStyleSheet(input_style)
        form_layout.addRow(client_label, self.client_input)

        # Project Description
        description_label = QLabel("Project Description:")
        description_label.setStyleSheet(main_label_style)
        self.description_input = QLineEdit()
        self.description_input.setStyleSheet(input_style)
        form_layout.addRow(description_label, self.description_input)

        # Date
        date_label = QLabel("Date (day/month/year):")
        date_label.setStyleSheet(main_label_style)
        self.date_input = QLineEdit()
        self.date_input.setStyleSheet(input_style)
        form_layout.addRow(date_label, self.date_input)

        # Revision Number
        revision_label = QLabel("Revision Number:")
        revision_label.setStyleSheet(main_label_style)
        self.revision_input = QLineEdit()
        self.revision_input.setStyleSheet(input_style)
        form_layout.addRow(revision_label, self.revision_input)


              # Add form layout to the main project info layout
        self.projectinfo_layout.addLayout(form_layout)

        # Create a horizontal layout for button alignment just after the inputs

        self.projectinfo_back_button = QPushButton("Back")
        self.projectinfo_back_button.setStyleSheet(button_style)
        self.projectinfo_back_button.clicked.connect(self.show_main_view)
        self.projectinfo_layout.addWidget(self.projectinfo_back_button, alignment=Qt.AlignRight)

       # button_layout = QVBoxLayout()
       # button_layout.addStretch()  # Adds space before the button
       # button_layout.addWidget(self.projectinfo_back_button, alignment=Qt.AlignRight)

        # Add button layout to the project info layout
      #  self.projectinfo_layout.addLayout(button_layout)


        # Set layout for the project info widget
        self.projectinfo_widget.setLayout(self.projectinfo_layout)
        self.stacked_layout.addWidget(self.projectinfo_widget)


       

    def initUI(self):
        self.stacked_layout = QStackedLayout(self)
        self.create_geometry_inputs()
        self.setLayout(self.stacked_layout)

  


    def create_geometry_inputs(self):
        self.geometry_widget = QWidget()
        self.geometry_layout = QVBoxLayout()

        # Form layout and enhanced styles for input fields and labels
        form_layout = QFormLayout()

        
        # Style for input fields (QLineEdit)
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """

        # Style for main labels (field descriptions)
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;  /* Dark gray for readability */
                font-weight: 600;  /* Slightly bold */
                padding: 3px 0;  /* Padding for spacing */
            }
        """

        # Style for note labels (informational messages)
        note_label_style = """
            QLabel {
                font-size: 13px;
                color: #555;  /* Softer color for notes */
                padding: 10px;
                background-color: #f0f8ff;  /* Light blue background for distinction */
                border-left: 4px solid #ffa500;  /* Orange accent border */
                border-radius: 5px;
                margin-top: 8px;
            }
        """

        # Apply styles to input fields
        self.wall_height_input = QLineEdit()
        self.wall_height_input.setStyleSheet(input_style)
        
        self.embedment_depth_input = QLineEdit()
        self.embedment_depth_input.setStyleSheet(input_style)
        
        self.wall_length_input = QLineEdit()
        self.wall_length_input.setStyleSheet(input_style)
        
        self.wall_batter_input = QLineEdit()
        self.wall_batter_input.setStyleSheet(input_style)
        
        self.wall_backslopeangle_input = QLineEdit()
        self.wall_backslopeangle_input.setStyleSheet(input_style)
        
        self.wall_backsloperise_input = QLineEdit()
        self.wall_backsloperise_input.setStyleSheet(input_style)

        # Enhanced labels for each input field
        wall_height_label = QLabel("Wall Height (H: m):")
        wall_height_label.setStyleSheet(main_label_style)
        embedment_depth_label = QLabel("Embedment Depth (m):")
        embedment_depth_label.setStyleSheet(main_label_style)
        wall_length_label = QLabel("Overall Length of Reinforcement (m):")
        wall_length_label.setStyleSheet(main_label_style)
        wall_batter_label = QLabel("Batter (°):")
        wall_batter_label.setStyleSheet(main_label_style)
        backslope_angle_label = QLabel("Back Slope Angle (°):")
        backslope_angle_label.setStyleSheet(main_label_style)
        backslope_rise_label = QLabel("Back Slope Rise (m):")
        backslope_rise_label.setStyleSheet(main_label_style)

        # Add labeled input fields to form layout
        form_layout.addRow(wall_height_label, self.wall_height_input)
        form_layout.addRow(embedment_depth_label, self.embedment_depth_input)
        form_layout.addRow(wall_length_label, self.wall_length_input)
        form_layout.addRow(wall_batter_label, self.wall_batter_input)
        form_layout.addRow(backslope_angle_label, self.wall_backslopeangle_input)
        form_layout.addRow(backslope_rise_label, self.wall_backsloperise_input)


        self.geometry_layout.addLayout(form_layout)

        # Enhanced styling for message labels (notes)
        self.batter_message_label = QLabel("Note: Batter < 20° is only designed as walls.\nBatter < 10° is considered a vertical wall in calculations as a conservative approach.")
        self.batter_message_label.setStyleSheet(note_label_style)
        self.geometry_layout.addWidget(self.batter_message_label)

        self.Backslope_message_label = QLabel("Note: Slope of soil above wall should be less than the friction angle, else it should be reinforced.")
        self.Backslope_message_label.setStyleSheet(note_label_style)
        self.geometry_layout.addWidget(self.Backslope_message_label)

        # Style and add back button
        button_style = """
            QPushButton {
                background-color: #4CAF50; /* Button background color */
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049; /* Darker green on hover */
            }
        """

        self.geometry_back_button = QPushButton("Back")
        self.geometry_back_button.setStyleSheet(button_style)
        self.geometry_back_button.clicked.connect(self.show_main_view)
        self.geometry_layout.addWidget(self.geometry_back_button, alignment=Qt.AlignRight)

        # Set layout for the geometry widget and add it to the stacked layout
        self.geometry_widget.setLayout(self.geometry_layout)
        self.stacked_layout.addWidget(self.geometry_widget)




    def create_reinforced_soil_inputs(self):
        self.reinforced_soil_widget = QWidget()
        self.reinforced_soil_layout = QFormLayout()

        # Styles for input fields, main labels, and note labels
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """
        note_label_style = """
            QLabel {
                font-size: 13px;
                color: #555;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #ffa500;
                border-radius: 5px;
                margin-top: 8px;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """

        # Apply styles to input fields
        self.reinforced_soil_density_input = QLineEdit()
        self.reinforced_soil_density_input.setStyleSheet(input_style)

        self.reinforced_angle_input = QLineEdit()
        self.reinforced_angle_input.setStyleSheet(input_style)

        self.reinforced_cohesion_input = QLineEdit()
        self.reinforced_cohesion_input.setStyleSheet(input_style)

        # Enhanced labels for each input field
        density_label = QLabel("Soil Density (γ: kN/m³):")
        density_label.setStyleSheet(main_label_style)
        friction_angle_label = QLabel("Angle of Internal Friction (φ°):")
        friction_angle_label.setStyleSheet(main_label_style)
        cohesion_label = QLabel("Cohesion (c: kN/m²):")
        cohesion_label.setStyleSheet(main_label_style)

        # Add labeled input fields to form layout
        self.reinforced_soil_layout.addRow(density_label, self.reinforced_soil_density_input)
        self.reinforced_soil_layout.addRow(friction_angle_label, self.reinforced_angle_input)
        self.reinforced_soil_layout.addRow(cohesion_label, self.reinforced_cohesion_input)

        # Enhanced styling for note label
        self.cohesion_message_label = QLabel("Note: Cohesion is neglected for all calculations.")
        self.cohesion_message_label.setStyleSheet(note_label_style)
        self.reinforced_soil_layout.addWidget(self.cohesion_message_label)

        # Style back button and align it to the right
        self.reinforced_soil_back_button = QPushButton("Back")
        self.reinforced_soil_back_button.setStyleSheet(button_style)
        self.reinforced_soil_back_button.clicked.connect(self.show_main_view)
        
        # Create a horizontal layout for button alignment
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.reinforced_soil_back_button)
        
        # Add the button layout to the form layout
        self.reinforced_soil_layout.addRow(button_layout)

        # Set layout for reinforced soil widget
        self.reinforced_soil_widget.setLayout(self.reinforced_soil_layout)
        self.stacked_layout.addWidget(self.reinforced_soil_widget)

    def create_retained_soil_inputs(self):
        self.retained_soil_widget = QWidget()
        self.retained_soil_layout = QFormLayout()

        # Styles for input fields, main labels, and note labels
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """
        note_label_style = """
            QLabel {
                font-size: 13px;
                color: #555;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #ffa500;
                border-radius: 5px;
                margin-top: 8px;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """

        # Apply styles to input fields
        self.retained_soil_density_input = QLineEdit()
        self.retained_soil_density_input.setStyleSheet(input_style)

        self.retained_angle_input = QLineEdit()
        self.retained_angle_input.setStyleSheet(input_style)

        self.retained_cohesion_input = QLineEdit()
        self.retained_cohesion_input.setStyleSheet(input_style)

        # Enhanced labels for each input field
        density_label = QLabel("Soil Density (γ: kN/m³):")
        density_label.setStyleSheet(main_label_style)
        friction_angle_label = QLabel("Angle of Internal Friction (φ°):")
        friction_angle_label.setStyleSheet(main_label_style)
        cohesion_label = QLabel("Cohesion (c: kN/m²):")
        cohesion_label.setStyleSheet(main_label_style)

        # Add labeled input fields to form layout
        self.retained_soil_layout.addRow(density_label, self.retained_soil_density_input)
        self.retained_soil_layout.addRow(friction_angle_label, self.retained_angle_input)
        self.retained_soil_layout.addRow(cohesion_label, self.retained_cohesion_input)

        # Enhanced styling for note label
        self.cohesion_message_label = QLabel("Note: Cohesion is neglected for all calculations.")
        self.cohesion_message_label.setStyleSheet(note_label_style)
        self.retained_soil_layout.addWidget(self.cohesion_message_label)

        # Style back button and align it to the right
        self.retained_soil_back_button = QPushButton("Back")
        self.retained_soil_back_button.setStyleSheet(button_style)
        self.retained_soil_back_button.clicked.connect(self.show_main_view)
        
        # Create a horizontal layout for button alignment
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.retained_soil_back_button)
        
        # Add the button layout to the form layout
        self.retained_soil_layout.addRow(button_layout)

        # Set layout for retained soil widget
        self.retained_soil_widget.setLayout(self.retained_soil_layout)
        self.stacked_layout.addWidget(self.retained_soil_widget)




    def create_foundation_soil_inputs(self):
        self.foundation_soil_widget = QWidget()
        self.foundation_soil_layout = QFormLayout()

        # Styles for input fields, main labels, and note labels
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """
        note_label_style = """
            QLabel {
                font-size: 13px;
                color: #555;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #ffa500;
                border-radius: 5px;
                margin-top: 8px;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """

        # Apply styles to input fields
        self.foundation_soil_density_input = QLineEdit()
        self.foundation_soil_density_input.setStyleSheet(input_style)

        self.foundation_angle_input = QLineEdit()
        self.foundation_angle_input.setStyleSheet(input_style)

        self.foundation_cohesion_input = QLineEdit()
        self.foundation_cohesion_input.setStyleSheet(input_style)

        self.foundation_eccentricity_input = QLineEdit()
        self.foundation_eccentricity_input.setStyleSheet(input_style)

        self.foundation_eccentricityseismic_input = QLineEdit()
        self.foundation_eccentricityseismic_input.setStyleSheet(input_style)

        self.watertabledepth_input = QLineEdit()
        self.watertabledepth_input.setStyleSheet(input_style)

        # Enhanced labels for each input field
        density_label = QLabel("Equivalent Foundation Soil Density (γ: kN/m³):")
        density_label.setStyleSheet(main_label_style)
        friction_angle_label = QLabel("Equivalent Angle of Internal Friction (φ°):")
        friction_angle_label.setStyleSheet(main_label_style)
        cohesion_label = QLabel("Equivalent Cohesion (c: kN/m²):")
        cohesion_label.setStyleSheet(main_label_style)
        eccentricity_label = QLabel("Allowable Eccentricity: Static (e/L):")
        eccentricity_label.setStyleSheet(main_label_style)
        eccentricity_seismic_label = QLabel("Allowable Eccentricity: Seismic (e/L):")
        eccentricity_seismic_label.setStyleSheet(main_label_style)
        watertable_label = QLabel("Water Table depth below founding level (m):")
        watertable_label.setStyleSheet(main_label_style)

        # Add labeled input fields to form layout
        self.foundation_soil_layout.addRow(density_label, self.foundation_soil_density_input)
        self.foundation_soil_layout.addRow(friction_angle_label, self.foundation_angle_input)
        self.foundation_soil_layout.addRow(cohesion_label, self.foundation_cohesion_input)
        self.foundation_soil_layout.addRow(eccentricity_label, self.foundation_eccentricity_input)
        self.foundation_soil_layout.addRow(eccentricity_seismic_label, self.foundation_eccentricityseismic_input)
        self.foundation_soil_layout.addRow(watertable_label, self.watertabledepth_input)

        # Style back button and align it to the right
        self.foundation_soil_back_button = QPushButton("Back")
        self.foundation_soil_back_button.setStyleSheet(button_style)
        self.foundation_soil_back_button.clicked.connect(self.show_main_view)
        
        # Create a horizontal layout for button alignment
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.foundation_soil_back_button)
        
        # Add the button layout to the form layout
        self.foundation_soil_layout.addRow(button_layout)

        # Set layout for foundation soil widget
        self.foundation_soil_widget.setLayout(self.foundation_soil_layout)
        self.stacked_layout.addWidget(self.foundation_soil_widget)

    
    ##################################

    def create_external_loads_options(self):
        self.external_loads_options_widget = QWidget()
        self.external_loads_options_layout = QVBoxLayout()

        # Define individual button styles with distinct colors
        button_styles = {
            "Dead Load": """
                QPushButton {
                    background-color: #FF5733; /* Coral color */
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #E74C3C; /* Darker coral */
                }
            """,
            "Live Load": """
                QPushButton {
                    background-color: #3498DB; /* Sky blue */
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980B9; /* Darker blue */
                }
            """,
            "Strip Load": """
                QPushButton {
                    background-color: #1ABC9C; /* Sea green */
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #16A085; /* Darker green */
                }
            """,
            "Impact Load": """
                QPushButton {
                    background-color: #F1C40F; /* Yellow */
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F39C12; /* Darker yellow */
                }
            """,
            "Earthquake Load": """
                QPushButton {
                    background-color: #9B59B6; /* Purple */
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8E44AD; /* Darker purple */
                }
            """
        }

        # Define button text and corresponding actions
        buttons = [
            ("Dead Load", self.show_dead_load_inputs),
            ("Live Load", self.show_live_load_inputs),
            ("Strip Load", self.show_strip_load_inputs),
            ("Impact Load", self.show_impact_load_inputs),
            ("Earthquake Load", self.show_earthquake_load_inputs)
        ]

        # Create and style buttons
        for text, func in buttons:
            button = QPushButton(text)
            button.setStyleSheet(button_styles[text])  # Apply the unique style for each button
            button.clicked.connect(func)
            self.external_loads_options_layout.addWidget(button)

        # Back button with a neutral style

        back_button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """


        self.external_loads_back_button = QPushButton("Back")
        self.external_loads_back_button.setStyleSheet(back_button_style)
        self.external_loads_back_button.clicked.connect(self.show_main_view)
        self.external_loads_options_layout.addWidget(self.external_loads_back_button, alignment=Qt.AlignRight)

        # Set layout and add to stacked layout
        self.external_loads_options_widget.setLayout(self.external_loads_options_layout)
        self.stacked_layout.addWidget(self.external_loads_options_widget)

        
    def create_dead_load_inputs(self):
        self.dead_load_widget = QWidget()
        self.dead_load_layout = QFormLayout()

        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        button_style2 = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """

        dead_load1_label = QLabel("Dead Load 1 (kN/m²):")
        dead_load1_label.setStyleSheet(main_label_style)
        self.dead_load1_input = QLineEdit()
        self.dead_load1_input.setStyleSheet(input_style)
        self.dead_load_layout.addRow(dead_load1_label, self.dead_load1_input)

        dead_load2_label = QLabel("Dead Load 2 (kN/m²):")
        dead_load2_label.setStyleSheet(main_label_style)
        self.dead_load2_input = QLineEdit()
        self.dead_load2_input.setStyleSheet(input_style)
        self.dead_load_layout.addRow(dead_load2_label, self.dead_load2_input)

        dead_load3_label = QLabel("Dead Load 3 (kN/m²):")
        dead_load3_label.setStyleSheet(main_label_style)
        self.dead_load3_input = QLineEdit()
        self.dead_load3_input.setStyleSheet(input_style)
        self.dead_load_layout.addRow(dead_load3_label, self.dead_load3_input)

        # Note label
        self.deadload_message_label = QLabel("Note: Dead load1: Soil type surcharges (EV); Dead load2: Wearing surfaces and utilities (DW); Dead load3: Non-soil type surcharges (ES).")
        self.deadload_message_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #555;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #ffa500;
                border-radius: 5px;
                margin-top: 8px;
            }
        """)
        self.dead_load_layout.addWidget(self.deadload_message_label)

        # Back button
        self.dead_load_back_button = QPushButton("Back")
        self.dead_load_back_button.setStyleSheet(button_style2)
        self.dead_load_back_button.clicked.connect(self.show_external_loads_options)
        self.dead_load_layout.addWidget(self.dead_load_back_button)

        # Create a horizontal layout for the Back button to align it to the right
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # Adds flexible space to push the button to the right
        button_layout.addWidget(self.dead_load_back_button, alignment=Qt.AlignRight)
            # Add button layout to the main layout
        self.dead_load_layout.addRow(button_layout)



        self.dead_load_widget.setLayout(self.dead_load_layout)
        self.stacked_layout.addWidget(self.dead_load_widget)

    def create_live_load_inputs(self):
        self.live_load_widget = QWidget()
        self.live_load_layout = QFormLayout()

        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """

        button_style2 = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """

        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """

        # Live Loads
        live_load1_label = QLabel("Live Load 1 (kN/m²):")
        live_load1_label.setStyleSheet(main_label_style)
        self.live_load1_input = QLineEdit()
        self.live_load1_input.setStyleSheet(input_style)
        self.live_load_layout.addRow(live_load1_label, self.live_load1_input)

        live_load2_label = QLabel("Live Load 2 (kN/m²):")
        live_load2_label.setStyleSheet(main_label_style)
        self.live_load2_input = QLineEdit()
        self.live_load2_input.setStyleSheet(input_style)
        self.live_load_layout.addRow(live_load2_label, self.live_load2_input)

        live_load3_label = QLabel("Live Load 3 (kN/m²):")
        live_load3_label.setStyleSheet(main_label_style)
        self.live_load3_input = QLineEdit()
        self.live_load3_input.setStyleSheet(input_style)
        self.live_load_layout.addRow(live_load3_label, self.live_load3_input)

        # Back button
        self.live_load_back_button = QPushButton("Back")
        self.live_load_back_button.setStyleSheet(button_style2)
        self.live_load_back_button.clicked.connect(self.show_external_loads_options)
        self.live_load_layout.addWidget(self.live_load_back_button)


        # Create a horizontal layout for the Back button to align it to the right
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # Adds flexible space to push the button to the right
        button_layout.addWidget(self.live_load_back_button, alignment=Qt.AlignRight)
            # Add button layout to the main layout
        self.live_load_layout.addRow(button_layout)

        self.live_load_widget.setLayout(self.live_load_layout)
        self.stacked_layout.addWidget(self.live_load_widget)

    def create_strip_load_inputs(self):
        self.strip_load_widget = QWidget()
        self.strip_load_layout = QFormLayout()
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        button_style2 = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """

        # Strip Loads
        vertical_strip_load1_label = QLabel("Vertical Strip Load 1 (kN/m²):")
        vertical_strip_load1_label.setStyleSheet(main_label_style)
        self.Vertical_Strip_Load1_input = QLineEdit()
        self.Vertical_Strip_Load1_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(vertical_strip_load1_label, self.Vertical_Strip_Load1_input)

        horizontal_strip_load1_label = QLabel("Horizontal Strip Load 1 (kN/m):")
        horizontal_strip_load1_label.setStyleSheet(main_label_style)
        self.Horizontal_Strip_Load1_input = QLineEdit()
        self.Horizontal_Strip_Load1_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(horizontal_strip_load1_label, self.Horizontal_Strip_Load1_input)

        strip_load1_width_label = QLabel("Strip Load 1 Width (m):")
        strip_load1_width_label.setStyleSheet(main_label_style)
        self.Strip_Load1_width_input = QLineEdit()
        self.Strip_Load1_width_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(strip_load1_width_label, self.Strip_Load1_width_input)

        strip_load1_distance_label = QLabel("Strip Load 1 Distance (m):")
        strip_load1_distance_label.setStyleSheet(main_label_style)
        self.Strip_Load1_distance_input = QLineEdit()
        self.Strip_Load1_distance_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(strip_load1_distance_label, self.Strip_Load1_distance_input)
        
        # Strip Load 2
        vertical_strip_load2_label = QLabel("Vertical Strip Load 2 (kN/m²):")
        vertical_strip_load2_label.setStyleSheet(main_label_style)
        self.Vertical_Strip_Load2_input = QLineEdit()
        self.Vertical_Strip_Load2_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(vertical_strip_load2_label, self.Vertical_Strip_Load2_input)

        horizontal_strip_load2_label = QLabel("Horizontal Strip Load 2 (kN/m):")
        horizontal_strip_load2_label.setStyleSheet(main_label_style)
        self.Horizontal_Strip_Load2_input = QLineEdit()
        self.Horizontal_Strip_Load2_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(horizontal_strip_load2_label, self.Horizontal_Strip_Load2_input)

        strip_load2_width_label = QLabel("Strip Load 2 Width (m):")
        strip_load2_width_label.setStyleSheet(main_label_style)
        self.Strip_Load2_width_input = QLineEdit()
        self.Strip_Load2_width_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(strip_load2_width_label, self.Strip_Load2_width_input)

        strip_load2_distance_label = QLabel("Strip Load 2 Distance (m):")
        strip_load2_distance_label.setStyleSheet(main_label_style)
        self.Strip_Load2_distance_input = QLineEdit()
        self.Strip_Load2_distance_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(strip_load2_distance_label, self.Strip_Load2_distance_input)

        # Strip Load 3
        vertical_strip_load3_label = QLabel("Vertical Strip Load 3 (kN/m²):")
        vertical_strip_load3_label.setStyleSheet(main_label_style)
        self.Vertical_Strip_Load3_input = QLineEdit()
        self.Vertical_Strip_Load3_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(vertical_strip_load3_label, self.Vertical_Strip_Load3_input)

        horizontal_strip_load3_label = QLabel("Horizontal Strip Load 3 (kN/m):")
        horizontal_strip_load3_label.setStyleSheet(main_label_style)
        self.Horizontal_Strip_Load3_input = QLineEdit()
        self.Horizontal_Strip_Load3_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(horizontal_strip_load3_label, self.Horizontal_Strip_Load3_input)

        strip_load3_width_label = QLabel("Strip Load 3 Width (m):")
        strip_load3_width_label.setStyleSheet(main_label_style)
        self.Strip_Load3_width_input = QLineEdit()
        self.Strip_Load3_width_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(strip_load3_width_label, self.Strip_Load3_width_input)

        strip_load3_distance_label = QLabel("Strip Load 3 Distance (m):")
        strip_load3_distance_label.setStyleSheet(main_label_style)
        self.Strip_Load3_distance_input = QLineEdit()
        self.Strip_Load3_distance_input.setStyleSheet(input_style)
        self.strip_load_layout.addRow(strip_load3_distance_label, self.Strip_Load3_distance_input)

        # Back button
        self.strip_load_back_button = QPushButton("Back")
        self.strip_load_back_button.setStyleSheet(button_style2)
        self.strip_load_back_button.clicked.connect(self.show_external_loads_options)
        self.strip_load_layout.addWidget(self.strip_load_back_button)

                # Create a horizontal layout for the Back button to align it to the right
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # Adds flexible space to push the button to the right
        button_layout.addWidget(self.strip_load_back_button, alignment=Qt.AlignRight)
            # Add button layout to the main layout
        self.strip_load_layout.addRow(button_layout)

        self.strip_load_widget.setLayout(self.strip_load_layout)
        self.stacked_layout.addWidget(self.strip_load_widget)

    def create_impact_load_inputs(self):
        self.impact_load_widget = QWidget()
        self.impact_load_layout = QFormLayout()
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        button_style2 = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """
        note_style = """
            QLabel {
                font-size: 13px;
                color: #555;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #ffa500;
                border-radius: 5px;
                margin-top: 8px;
            }
        """

        # Impact Load
        impact_load1_label = QLabel("Rupture Impact Load - Upper layer (kN/m):")
        impact_load1_label.setStyleSheet(main_label_style)
        self.impact_load1_input = QLineEdit()
        self.impact_load1_input.setStyleSheet(input_style)
        self.impact_load_layout.addRow(impact_load1_label, self.impact_load1_input)

                # Impact Load
        impact_load2_label = QLabel("Rupture Impact Load - Second layer from top (kN/m):")
        impact_load2_label.setStyleSheet(main_label_style)
        self.impact_load2_input = QLineEdit()
        self.impact_load2_input.setStyleSheet(input_style)
        self.impact_load_layout.addRow(impact_load2_label, self.impact_load2_input)

        impact_load3_label = QLabel("Pullout Impact Load - Upper layer (kN/m):")
        impact_load3_label.setStyleSheet(main_label_style)
        self.impact_load3_input = QLineEdit()
        self.impact_load3_input.setStyleSheet(input_style)
        self.impact_load_layout.addRow(impact_load3_label, self.impact_load3_input)

                # Impact Load
        impact_load4_label = QLabel("Pullout Impact Load - Second layer from top (kN/m):")
        impact_load4_label.setStyleSheet(main_label_style)
        self.impact_load4_input = QLineEdit()
        self.impact_load4_input.setStyleSheet(input_style)
        self.impact_load_layout.addRow(impact_load4_label, self.impact_load4_input)



                # Note about slope stability
        note_label = QLabel("        Upper layer rutpure impact load = 33.5 kN/m.<br>Second layer rupture impact load = 8.8 kN/m.<br>Upper layer pullout impact load = 19 kN/m - resisted over full length<br>Second layer pullout impact load = 8.8 kN/m - resisted over full length.")
        note_label.setStyleSheet(note_style)
        self.impact_load_layout.addWidget(note_label)




        # Back button
        self.impact_load_back_button = QPushButton("Back")
        self.impact_load_back_button.setStyleSheet(button_style2)
        self.impact_load_back_button.clicked.connect(self.show_external_loads_options)
        self.impact_load_layout.addWidget(self.impact_load_back_button)

                # Create a horizontal layout for the Back button to align it to the right
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # Adds flexible space to push the button to the right
        button_layout.addWidget(self.impact_load_back_button, alignment=Qt.AlignRight)
            # Add button layout to the main layout
        self.impact_load_layout.addRow(button_layout)


        self.impact_load_widget.setLayout(self.impact_load_layout)
        self.stacked_layout.addWidget(self.impact_load_widget)




    def create_earthquake_load_inputs(self):
        self.earthquake_load_widget = QWidget()
        self.earthquake_load_layout = QFormLayout()

        input_style = """
            QLineEdit {
                padding: 8px;
                border: 2px solid #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #66afe9;
                background-color: #ffffff;
            }
        """
        checkbox_style = """
            QCheckBox {
                font-size: 14px;
                color: #4a4a4a;
                font-weight: 600;
            }
        """
        note_style = """
            QLabel {
                font-size: 13px;
                color: #555;
                padding: 10px;
                background-color: #f0f8ff;
                border-left: 4px solid #ffa500;
                border-radius: 5px;
                margin-top: 8px;
            }
        """
        button_style2 = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """
        main_label_style = """
            QLabel {
                font-size: 16px;
                color: #4a4a4a;
                font-weight: 600;
                padding: 3px 0;
            }
        """
        # Earthquake Load Acceleration Input
        earthquake_load_label = QLabel("Acceleration (g):")
        earthquake_load_label.setStyleSheet(main_label_style)
        self.earthquake_load_input = QLineEdit()
        self.earthquake_load_input.setStyleSheet(input_style)
        self.earthquake_load_layout.addRow(earthquake_load_label, self.earthquake_load_input)

        # Checkbox to enable kh input
        self.use_direct_kh_checkbox = QCheckBox("Directly Input Seimic Force for External Stability")
        self.use_direct_kh_checkbox.setStyleSheet(checkbox_style)
        self.earthquake_load_layout.addWidget(self.use_direct_kh_checkbox)

        # Horizontal Seismic Coefficient Input
        kh_label = QLabel("Seimic Horizontal Earth Pressure Force (P):")
        kh_label.setStyleSheet(main_label_style)
        self.kh_input = QLineEdit()
        self.kh_input.setStyleSheet(input_style)
        self.kh_input.setDisabled(True)  # Initially disabled
        self.earthquake_load_layout.addRow(kh_label, self.kh_input)

        # Connect checkbox to enable/disable kh_input using the `toggled` signal
        self.use_direct_kh_checkbox.toggled.connect(self.toggle_kh_input)

        # Note about slope stability
        note_label = QLabel("Note: For backfills sloped at 3H:1V or steeper, M-O solutions may be invalid. <br> Use seismic earth pressure force from GLE analysis and input directly.")
        note_label.setStyleSheet(note_style)
        self.earthquake_load_layout.addWidget(note_label)


        # Back Button
        self.earthquake_load_back_button = QPushButton("Back")
        self.earthquake_load_back_button.setStyleSheet(button_style2)
        self.earthquake_load_back_button.clicked.connect(self.show_external_loads_options)

        # Align the Back button to the right
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.earthquake_load_back_button, alignment=Qt.AlignRight)
        self.earthquake_load_layout.addRow(button_layout)

        self.earthquake_load_widget.setLayout(self.earthquake_load_layout)
        self.stacked_layout.addWidget(self.earthquake_load_widget)

    def toggle_kh_input(self, checked):
        """Toggle kh_input field based on checkbox state."""
        if checked:
            print("Checkbox checked: Enabling kh input")
            self.kh_input.setEnabled(True)
            self.kh_input.setFocus()  # Focus on kh_input when enabled
        else:
            print("Checkbox unchecked: Disabling kh input")
            self.kh_input.clearFocus()
            self.kh_input.setEnabled(False)

    
    def show_geometry_view(self):
        self.stacked_layout.setCurrentWidget(self.geometry_widget)

 

    # Function to create reinforcement properties inputs
    def create_reinforcement_properties_inputs(self):
        self.reinforcement_properties_widget = QWidget()
        self.reinforcement_properties_layout = QVBoxLayout()

        header_label = QLabel("Reinforcement Properties")
        header_label.setFont(QFont("Arial", 14, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        self.reinforcement_properties_layout.addWidget(header_label)

        self.reinforcement_properties_table = QTableWidget()
        self.reinforcement_properties_table.setColumnCount(10)
        self.reinforcement_properties_table.setHorizontalHeaderLabels([
            "Type ID", "Name", "Tult", "Rfid", "RFw", "RFcreep", "FS",
            "Pull-Out Interface Angle (°)", "Direct Sliding Interface Angle (°)", "Scale Factor"
        ])
        self.reinforcement_properties_table.horizontalHeader().setStyleSheet("font-size: 11px; color: #555;")
        self.reinforcement_properties_table.setAlternatingRowColors(True)
        self.reinforcement_properties_table.verticalHeader().setVisible(False)
        self.reinforcement_properties_table.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.reinforcement_properties_table.setHorizontalScrollMode(QAbstractItemView.ScrollPerPixel)

        self.add_reinforcement_type_button = QPushButton("Add Reinforcement Type")
        self.add_reinforcement_type_button.setStyleSheet("padding: 6px; background-color: #007ACC; color: white; font-weight: bold;")
        self.add_reinforcement_type_button.clicked.connect(self.add_reinforcement_type)

        self.remove_reinforcement_type_button = QPushButton("Remove Reinforcement Type")
        self.remove_reinforcement_type_button.setStyleSheet("padding: 6px; background-color: #C70039; color: white; font-weight: bold;")
        self.remove_reinforcement_type_button.clicked.connect(self.remove_reinforcement_type)

        back_button = QPushButton("Back")
        back_button.setStyleSheet("padding: 6px; background-color: #555; color: white; font-weight: bold;")
        back_button.clicked.connect(self.show_main_view)

        self.reinforcement_properties_layout.addWidget(self.reinforcement_properties_table)
        self.reinforcement_properties_layout.addWidget(self.add_reinforcement_type_button)
        self.reinforcement_properties_layout.addWidget(self.remove_reinforcement_type_button)
        self.reinforcement_properties_layout.addWidget(back_button)

        self.reinforcement_properties_widget.setLayout(self.reinforcement_properties_layout)
        self.stacked_layout.addWidget(self.reinforcement_properties_widget)

    def add_reinforcement_type(self):
        row_position = self.reinforcement_properties_table.rowCount()
        self.reinforcement_properties_table.insertRow(row_position)

        # Styled table items
        type_id_item = QTableWidgetItem(f"Type-{row_position + 1}")
        name_item = QTableWidgetItem()
        tult_item = QTableWidgetItem()
        rfid_item = QTableWidgetItem()
        rfw_item = QTableWidgetItem()
        rfcreep_item = QTableWidgetItem()
        fs_item = QTableWidgetItem()
        pullout_angle_item = QTableWidgetItem()
        sliding_angle_item = QTableWidgetItem()
        scale_factor_item = QTableWidgetItem()

        for item in [type_id_item, name_item, tult_item, rfid_item, rfw_item, rfcreep_item, fs_item, pullout_angle_item, sliding_angle_item, scale_factor_item]:
            item.setTextAlignment(Qt.AlignCenter)
            item.setFont(QFont("Arial", 10))

        self.reinforcement_properties_table.setItem(row_position, 0, type_id_item)
        self.reinforcement_properties_table.setItem(row_position, 1, name_item)
        self.reinforcement_properties_table.setItem(row_position, 2, tult_item)
        self.reinforcement_properties_table.setItem(row_position, 3, rfid_item)
        self.reinforcement_properties_table.setItem(row_position, 4, rfw_item)
        self.reinforcement_properties_table.setItem(row_position, 5, rfcreep_item)
        self.reinforcement_properties_table.setItem(row_position, 6, fs_item)
        self.reinforcement_properties_table.setItem(row_position, 7, pullout_angle_item)
        self.reinforcement_properties_table.setItem(row_position, 8, sliding_angle_item)
        self.reinforcement_properties_table.setItem(row_position, 9, scale_factor_item)

        # Connect signals to update dropdown when names are changed
        self.reinforcement_properties_table.itemChanged.connect(self.update_reinforcement_dropdown)


    def remove_reinforcement_type(self):
        selected_row = self.reinforcement_properties_table.currentRow()
        if selected_row >= 0:
            self.reinforcement_properties_table.removeRow(selected_row)
            self.update_reinforcement_dropdown()

    # Function to update reinforcement dropdowns in the reinforcement details table
    def update_reinforcement_dropdown(self):
        """Updates the reinforcement dropdown in Reinforcement Details based on names in Reinforcement Properties."""
        reinforcement_names = []

        # Collect all names from the Reinforcement Properties table
        for row in range(self.reinforcement_properties_table.rowCount()):
            item = self.reinforcement_properties_table.item(row, 1)  # Name is in column 1
            if item and item.text().strip():
                reinforcement_names.append(item.text().strip())

        # Update the dropdowns in Reinforcement Details
        for row in range(self.reinforcement_table.rowCount()):
            combo_box = self.reinforcement_table.cellWidget(row, 3)  # Reinforcement Type column
            if isinstance(combo_box, QComboBox):
                combo_box.clear()
                combo_box.addItems(reinforcement_names)

    def create_reinforcement_details_inputs(self):
        self.reinforcement_details_widget = QWidget()
        self.reinforcement_details_layout = QVBoxLayout()

        # Header label
        header_label = QLabel("Reinforcement Details")
        header_label.setFont(QFont("Arial", 14, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        self.reinforcement_details_layout.addWidget(header_label)

                # Label for number of reinforcements with improved style
        reinforcements_label = QLabel("Number of Reinforcements:")
        reinforcements_label.setFont(QFont("Arial", 10, QFont.Bold))
        reinforcements_label.setStyleSheet("color: #333; padding: 4px;")

        # Enhanced input style with arrows for increasing/decreasing
        self.num_reinforcements_input = QSpinBox()
        self.num_reinforcements_input.setStyleSheet("padding: 4px; font-size: 10px; color: #007ACC;")
        self.num_reinforcements_input.setMinimum(1)
        self.num_reinforcements_input.setMaximum(100)
        self.num_reinforcements_input.setFont(QFont("Arial", 12))
        self.num_reinforcements_input.valueChanged.connect(self.update_reinforcement_rows)

        # Add label and input to layout
        self.reinforcement_details_layout.addWidget(reinforcements_label)
        self.reinforcement_details_layout.addWidget(self.num_reinforcements_input)

        self.reinforcement_table = QTableWidget()
        self.reinforcement_table.setColumnCount(6)
        self.reinforcement_table.setHorizontalHeaderLabels([
            "Location from bottom (m)", "Length (m)", "Coverage Ratio", "Reinforcement Type",
            "Connection Strength RF (Long-Term)", "Connection Strength RF (Bio-Chem)"
        ])
        self.reinforcement_table.horizontalHeader().setStyleSheet("font-size: 12px; color: #555;")
        self.reinforcement_table.verticalHeader().setVisible(False)
        self.reinforcement_table.setAlternatingRowColors(True)

        self.reinforcement_details_layout.addWidget(self.num_reinforcements_input)

        self.reinforcement_details_layout.addWidget(self.reinforcement_table)

        # Back button with styling
        self.reinforcement_details_back_button = QPushButton("Back")
        self.reinforcement_details_back_button.setStyleSheet("padding: 6px; background-color: #007ACC; color: white; font-weight: bold;")
        self.reinforcement_details_back_button.clicked.connect(self.show_main_view)
        self.reinforcement_details_layout.addWidget(self.reinforcement_details_back_button)

        # Final layout setup
        self.reinforcement_details_widget.setLayout(self.reinforcement_details_layout)
        self.stacked_layout.addWidget(self.reinforcement_details_widget)

    def update_reinforcement_rows(self):
        num_reinforcements = self.num_reinforcements_input.value()
        self.reinforcement_table.setRowCount(num_reinforcements)

        for i in range(num_reinforcements):
            depth_item = QTableWidgetItem()
            length_item = QTableWidgetItem()
            coverage_ratio_item = QTableWidgetItem()

            depth_item.setFont(QFont("Arial", 10))
            length_item.setFont(QFont("Arial", 10))
            coverage_ratio_item.setFont(QFont("Arial", 10))

            reinforcement_type_combo = QComboBox()
            reinforcement_type_combo.setFont(QFont("Arial", 10))
            reinforcement_type_combo.setEditable(True)

            # Insert dynamic options
            reinforcement_type_combo.addItems([self.reinforcement_properties_table.item(row, 1).text() for row in range(self.reinforcement_properties_table.rowCount()) if self.reinforcement_properties_table.item(row, 1) is not None])

            self.reinforcement_table.setItem(i, 0, depth_item)
            self.reinforcement_table.setItem(i, 1, length_item)
            self.reinforcement_table.setItem(i, 2, coverage_ratio_item)
            self.reinforcement_table.setCellWidget(i, 3, reinforcement_type_combo)

    def show_main_view(self):
        self.stacked_layout.setCurrentIndex(0)  # Switch back to the main view (index 0)



    def draw_arrow(self, x, y, width, height, scene, color):
        arrow = QPolygonF([
            QPointF(x, y),
            QPointF(x - width / 2, y - height),
            QPointF(x + width / 2, y - height),
        ])
        arrow_item = scene.addPolygon(arrow, QPen(color), QBrush(color))
        scene.addItem(arrow_item)

    def draw_line(self, x1, y1, x2, y2, scene, color):
        line = scene.addLine(x1, y1, x2, y2, QPen(color))
        scene.addItem(line)


    def draw_generic_geometry(self):
                self.graphics_scene.clear()

                # Define pen and brushes for background
                pen = QPen(Qt.black)
                green_brush = QBrush(QColor("green"))
                orange_brush = QBrush(QColor("orange"))

                # Reinforced Soil with sandy color and pattern
                reinforced_brush = QBrush(QColor(255, 223, 128))  # Light sandy color
                reinforced_brush.setStyle(Qt.Dense5Pattern)  # Set pattern style

                # Retained Mass with earthy brown color and pattern
                retained_brush = QBrush(QColor(139, 69, 19))  # Earthy brown color
                retained_brush.setStyle(Qt.Dense6Pattern)  # Set pattern style

                # Wall dimensions
                H = 15
                L = 10

                # Scaling factor for sizes
                box_width = L * 40
                box_height = H * 40

                # Draw Reinforced Soil Wall rectangle
                rect1 = QGraphicsRectItem(0, 0, box_width, box_height)
                rect1.setPen(pen)
                rect1.setBrush(reinforced_brush)
                self.graphics_scene.addItem(rect1)

                # Create text for Reinforced Soil Wall
                text1 = QGraphicsTextItem("Reinforced Soil")
                font1 = QFont("Arial", 12, QFont.Bold)
                text1.setFont(font1)
                text1.setDefaultTextColor(QColor(0, 0, 0))  # Set text color to black
                text1.setPos(10, box_height / 2 - text1.boundingRect().height() / 2 - 50)  # Dynamic position
                self.graphics_scene.addItem(text1)

                # Draw Retained Mass rectangle
                rect2 = QGraphicsRectItem(box_width, 0, box_width, box_height)
                rect2.setPen(pen)
                rect2.setBrush(retained_brush)
                self.graphics_scene.addItem(rect2)

                # Create text for Retained Mass
                text2 = QGraphicsTextItem("Retained Soil")
                font2 = QFont("Arial", 12, QFont.Bold)
                text2.setFont(font2)
                text2.setDefaultTextColor(QColor(0, 0, 0))  # Set text color to black
                text2.setPos(box_width + 10, box_height / 2 - text2.boundingRect().height() / 2 - 50)  # Dynamic position
                self.graphics_scene.addItem(text2)

                # Optional: Adding a background shadow effect to the text
               # shadow_pen = QPen(QColor(100, 100, 100))  # Dark grey shadow color
               # text1_shadow = QGraphicsTextItem("Reinforced Soil")
               # text1_shadow.setFont(font1)
               # text1_shadow.setDefaultTextColor(QColor(100, 100, 100))  # Set shadow color
               # text1_shadow.setPos(text1.x() + 2, text1.y() + 2)  # Slight offset for shadow
               # self.graphics_scene.addItem(text1_shadow)

              #  text2_shadow = QGraphicsTextItem("Retained Soil")
              #  text2_shadow.setFont(font2)
              #  text2_shadow.setDefaultTextColor(QColor(100, 100, 100))  # Set shadow color
              #  text2_shadow.setPos(text2.x() + 2, text2.y() + 2)  # Slight offset for shadow
              #  self.graphics_scene.addItem(text2_shadow)       

    def draw_arrow(self, x, y, width, height, scene, color):
        arrow = QPolygonF([
            QPointF(x, y),
            QPointF(x - width / 2, y - height),
            QPointF(x + width / 2, y - height),
        ])
        arrow_item = scene.addPolygon(arrow, QPen(color), QBrush(color))
        scene.addItem(arrow_item)

    def draw_line(self, x1, y1, x2, y2, scene, color):
        line = scene.addLine(x1, y1, x2, y2, QPen(color))
        scene.addItem(line)

    def draw_geometry(self):
        self.graphics_scene.clear()
       # self.visualize_widget = QWidget()
     #   self.visualize_layout = QVBoxLayout()


        try:
            H = float(self.wall_height_input.text())
            L = float(self.wall_length_input.text())
            embedment_depth = float(self.embedment_depth_input.text())
            batter = float(self.wall_batter_input.text())
            back_slope_angle = float(self.wall_backslopeangle_input.text())
            back_slope_rise = float(self.wall_backsloperise_input.text())
        except ValueError:
            print("Please enter valid numerical values.")
            return
        

      #  self.graphics_widget = QWidget()
      #  self.graphics_layout = QVBoxLayout()

     #   self.graphics_view = QGraphicsView()
      #  self.graphics_scene = QGraphicsScene()

            # Clear the existing scene
      #  self.graphics_scene.clear()  # Highlighted line


        pen = QPen(Qt.black)
        green_brush = QBrush(QColor("green"))
        orange_brush = QBrush(QColor("orange"))
       # reinforced_brush = QBrush(Qt.Dense5Pattern) 
       # retained_brush = QBrush(Qt.Dense6Pattern)

        # For reinforced soil (sand-like texture with a pattern)
        reinforced_brush = QBrush(QColor(255, 223, 128))  # Light sandy color
        reinforced_brush.setStyle(Qt.Dense5Pattern)  # Set pattern style

        # For retained soil (earth-like texture with a pattern)
        retained_brush = QBrush(QColor(139, 69, 19))  # Earthy brown color
        retained_brush.setStyle(Qt.Dense6Pattern)  # Set pattern style

        # Determine the size of the boxes based on H and L
        box_height = H * 40
        box_width = (L / H) * box_height

        # Calculate the batter offset
        batter_radians = math.radians(batter)
        top_width_offset = box_height * math.tan(batter_radians)

        # Draw the batter for the first box (reinforced soil wall)
        points_reinforced_wall = [
            QPointF(0, 0),  # Bottom-left 
            QPointF(box_width , 0),  # Bottom-right
            QPointF(box_width-top_width_offset, box_height),  # Top-right
            QPointF(-top_width_offset, box_height)  # Top-left
        ]

        polygon_reinforced_wall = QPolygonF(points_reinforced_wall)
        rect1 = QGraphicsPolygonItem(polygon_reinforced_wall)
        rect1.setPen(pen)
        rect1.setBrush(reinforced_brush)
        self.graphics_scene.addItem(rect1)
    

        # Label the first box as "Reinforced Soil Wall"
        font = QFont("Arial", 14)  # "Arial" is the font family, and 16 is the font size
        font.setBold(True)         # Optionally, make the text bold

        text1 = QGraphicsTextItem("Reinforced Soil")
        text1.setFont(font)        # Set the font for the text item
        text1.setPos(5, box_height / 2 - text1.boundingRect().height() / 2 - 50)
        self.graphics_scene.addItem(text1)

        # Draw the batter for the second box (retained soil wall)
        points_retained_wall = [
            QPointF(box_width , 0),  # Bottom-left 
            QPointF(2.5*box_width , 0),  # Bottom-right
            QPointF(2.5*box_width, box_height),  # Top-right
            QPointF(box_width-top_width_offset, box_height)  # Top-left
        ]

        polygon_retained_wall = QPolygonF(points_retained_wall)
        rect2 = QGraphicsPolygonItem(polygon_retained_wall)
        rect2.setPen(pen)
        rect2.setBrush(retained_brush)
        self.graphics_scene.addItem(rect2)
       

        adjusted_box_width = box_width 

  

        # Label the second box as "Retained Mass"
        text2 = QGraphicsTextItem("Retained Soil")
        text2.setFont(font)        # Set the font for the text item
        text2.setPos(box_width + 5, box_height / 2 - text2.boundingRect().height() / 2 - 50)
        self.graphics_scene.addItem(text2)

        # Draw dimension lines and arrows for the second box (rect2)
        self.draw_dimension((2.5*box_width), 0, (2.5*box_width), box_height, f"H: {H} m", Qt.Vertical, is_outside=True)

        self.draw_dimension(-top_width_offset, box_height, box_width-top_width_offset, box_height, f"L: {L} m", Qt.Horizontal, is_outside=True)

        # Draw the slope
        if back_slope_angle != 0:
            back_slope_angle_radians = math.radians(back_slope_angle)
            slope_base_length = back_slope_rise / math.tan(back_slope_angle_radians)
            slope_start_x = 0
            slope_start_y = 0
            slope_end_x = slope_start_x + (slope_base_length * 40)
            slope_end_y = slope_start_y - back_slope_rise * 40
            slope_horizontal_length = (2.5 * box_width) - slope_end_x

            # Create the slope polygon
            slope_polygon = QPolygonF([
                QPointF(slope_start_x, slope_start_y),
                QPointF(slope_end_x, slope_end_y),
                QPointF(slope_end_x + slope_horizontal_length, slope_end_y),
                QPointF(slope_end_x + slope_horizontal_length, slope_start_y)
            ])
            slope_item = QGraphicsPolygonItem(slope_polygon)
            slope_item.setPen(QPen(Qt.black))
            slope_item.setBrush(QBrush(Qt.gray))
            self.graphics_scene.addItem(slope_item)

        # Draw the reinforcements
        self.draw_reinforcements(box_height, box_width)

        # Calculate dead load and live load positions
        dead_load1 = float(self.dead_load1_input.text()) if self.dead_load1_input.text() else 0.0
        dead_load2 = float(self.dead_load2_input.text()) if self.dead_load2_input.text() else 0.0
        dead_load3 = float(self.dead_load3_input.text()) if self.dead_load3_input.text() else 0.0

        total_dead_load = dead_load1 + dead_load2 + dead_load3

        live_load1 = float(self.live_load1_input.text()) if self.live_load1_input.text() else 0.0
        live_load2 = float(self.live_load2_input.text()) if self.live_load2_input.text() else 0.0
        live_load3 = float(self.live_load3_input.text()) if self.live_load3_input.text() else 0.0

        total_live_load = live_load1 + live_load2 + live_load3

        

        # Adjust dead load and live load positions if slope is drawn
        if back_slope_angle != 0:
            load_start_x = slope_end_x
            load_y = slope_end_y - 20
        else:
            load_start_x = 0
            load_y = -20

        arrow_spacing = ((adjusted_box_width-load_start_x)) / 10
        numberofarrows = int((((2.5 * box_width)-load_start_x) / arrow_spacing)) + 1

        for i in range(numberofarrows):
            if total_dead_load > 0:
                self.draw_arrow(load_start_x + i * arrow_spacing, load_y, 10, 20, self.graphics_scene, QColor("red"))

        dead_load_text = QGraphicsTextItem(f"Dead Load: {total_dead_load} kPa")
        dead_load_text.setPos(2 * adjusted_box_width - 100, load_y - 50)
        dead_load_text.setDefaultTextColor(QColor("red"))
        self.graphics_scene.addItem(dead_load_text)

        load_y -= 50

        for i in range(numberofarrows):
            if total_live_load > 0:
                self.draw_arrow(load_start_x + i * arrow_spacing, load_y, 10, 20, self.graphics_scene, QColor("blue"))

        live_load_text = QGraphicsTextItem(f"Live Load: {total_live_load} kPa")
        live_load_text.setPos(2 * adjusted_box_width - 100, load_y - 50)
        live_load_text.setDefaultTextColor(QColor("blue"))
        self.graphics_scene.addItem(live_load_text)

        # Draw lines for dead loads and live loads
        self.draw_line(load_start_x, load_y + 30, 2.5 * adjusted_box_width, load_y + 30, self.graphics_scene, QColor("red"))
        self.draw_line(load_start_x, load_y - 20, 2.5 * adjusted_box_width, load_y - 20, self.graphics_scene, QColor("blue"))

        # Draw strip loads
        self.draw_strip_loads(load_y - 70)

        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_layout.addWidget(self.graphics_view)

        self.graphics_back_button = QPushButton("Back")
        self.graphics_back_button.clicked.connect(self.show_main_view)
        self.graphics_layout.addWidget(self.graphics_back_button)

        self.graphics_widget.setLayout(self.graphics_layout)
        self.stacked_layout.addWidget(self.graphics_widget)
        self.stacked_layout.setCurrentWidget(self.graphics_widget)

    def draw_strip_loads(self, strip_load_y):
        strip_load1_vert = float(self.Vertical_Strip_Load1_input.text()) if self.Vertical_Strip_Load1_input.text() else 0.0
        strip_load1_width = float(self.Strip_Load1_width_input.text()) if self.Strip_Load1_width_input.text() else 0.0
        strip_load1_distance = float(self.Strip_Load1_distance_input.text()) if self.Strip_Load1_distance_input.text() else 0.0

        strip_load2_vert = float(self.Vertical_Strip_Load2_input.text()) if self.Vertical_Strip_Load2_input.text() else 0.0
        strip_load2_width = float(self.Strip_Load2_width_input.text()) if self.Strip_Load2_width_input.text() else 0.0
        strip_load2_distance = float(self.Strip_Load2_distance_input.text()) if self.Strip_Load2_distance_input.text() else 0.0

        strip_load3_vert = float(self.Vertical_Strip_Load3_input.text()) if self.Vertical_Strip_Load3_input.text() else 0.0
        strip_load3_width = float(self.Strip_Load3_width_input.text()) if self.Strip_Load3_width_input.text() else 0.0
        strip_load3_distance = float(self.Strip_Load3_distance_input.text()) if self.Strip_Load3_distance_input.text() else 0.0

        strip_load_color = QColor("purple")

        if strip_load1_vert > 0:
            strip_load1_x = (strip_load1_distance - strip_load1_width / 2) * 40
            strip_load1 = QGraphicsRectItem(strip_load1_x, strip_load_y, strip_load1_width * 40, 20)
            strip_load1.setPen(QPen(strip_load_color))
            strip_load1.setBrush(QBrush(strip_load_color))
            self.graphics_scene.addItem(strip_load1)
            strip_load1_text = QGraphicsTextItem(f"Strip Load 1: {strip_load1_vert} kPa")
            strip_load1_text.setPos(strip_load1_x + strip_load1_width * 20 - 50, strip_load_y - 30)
            strip_load1_text.setDefaultTextColor(strip_load_color)
            self.graphics_scene.addItem(strip_load1_text)

        if strip_load2_vert > 0:
            strip_load2_x = (strip_load2_distance - strip_load2_width / 2) * 40
            strip_load2 = QGraphicsRectItem(strip_load2_x, strip_load_y, strip_load2_width * 40, 20)
            strip_load2.setPen(QPen(strip_load_color))
            strip_load2.setBrush(QBrush(strip_load_color))
            self.graphics_scene.addItem(strip_load2)
            strip_load2_text = QGraphicsTextItem(f"Strip Load 2: {strip_load2_vert} kPa")
            strip_load2_text.setPos(strip_load2_x + strip_load2_width * 20 - 50, strip_load_y - 30)
            strip_load2_text.setDefaultTextColor(strip_load_color)
            self.graphics_scene.addItem(strip_load2_text)

        if strip_load3_vert > 0:
            strip_load3_x = (strip_load3_distance - strip_load3_width / 2) * 40
            strip_load3 = QGraphicsRectItem(strip_load3_x, strip_load_y, strip_load3_width * 40, 20)
            strip_load3.setPen(QPen(strip_load_color))
            strip_load3.setBrush(QBrush(strip_load_color))
            self.graphics_scene.addItem(strip_load3)
            strip_load3_text = QGraphicsTextItem(f"Strip Load 3: {strip_load3_vert} kPa")
            strip_load3_text.setPos(strip_load3_x + strip_load3_width * 20 - 50, strip_load_y - 30)
            strip_load3_text.setDefaultTextColor(strip_load_color)
            self.graphics_scene.addItem(strip_load3_text)



    def draw_reinforcements(self, box_height, box_width):
        # Dictionary to store colors for each grade
        grade_colors = {}
        
        def get_random_color():
            """Generates a random color."""
            return QColor(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))

        try:
            H1 = float(self.wall_height_input.text())
            batter1 = float(self.wall_batter_input.text())
        except ValueError:
            print("Please enter valid numerical values.")
            return

        batter_radians1 = math.radians(batter1)

        for row in range(self.reinforcement_table.rowCount()):
            try:
                location_from_bottom = float(self.reinforcement_table.item(row, 0).text()) * 40
                length_of_reinforcement = float(self.reinforcement_table.item(row, 1).text()) * 40
                
                # Access grade from QComboBox
                grade_widget = self.reinforcement_table.cellWidget(row, 3)
                if isinstance(grade_widget, QComboBox):
                    grade = grade_widget.currentText()
                else:
                    continue

            except (ValueError, AttributeError):
                print(f"Invalid data in row {row + 1}")
                continue

            # Assign a color to the grade if not already assigned
            if grade not in grade_colors:
                grade_colors[grade] = get_random_color()

            color = grade_colors[grade]
            pen = QPen(color)
            pen.setWidth(2)

            # Draw reinforcement line
            y_pos = box_height - location_from_bottom
            line = QGraphicsLineItem(
                0 - (y_pos * batter_radians1), y_pos,
                0 - (y_pos * batter_radians1) + length_of_reinforcement, y_pos
            )
            line.setPen(pen)
            self.graphics_scene.addItem(line)

            # Add label text with color
            label_text = f"L = {length_of_reinforcement / 40:.2f} m, {grade}"
            text_item = QGraphicsTextItem(label_text)
            
            # Set font and color
            font = QFont("Arial", 10, QFont.Bold)
            text_item.setFont(font)
            text_item.setDefaultTextColor(color)

            # Position text above the line, centered
            text_item.setPos(
                0 - (y_pos * batter_radians1) + (length_of_reinforcement / 2) - 40, 
                y_pos - 20
            )
            
            self.graphics_scene.addItem(text_item)

    def draw_dimension(self, x1, y1, x2, y2, text, orientation, is_outside=False):
        pen = QPen(Qt.black)
        line = QGraphicsLineItem(x1, y1, x2, y2)
        line.setPen(pen)
        self.graphics_scene.addItem(line)

        arrow_size = 10

        if orientation == Qt.Vertical:
            # Vertical arrow and dimension
            if is_outside:
                arrow1 = QPolygonF([QPointF(x1, y1), QPointF(x1 - arrow_size / 2, y1 + arrow_size), QPointF(x1 + arrow_size / 2, y1 + arrow_size)])
                arrow2 = QPolygonF([QPointF(x2, y2), QPointF(x2 - arrow_size / 2, y2 - arrow_size), QPointF(x2 + arrow_size / 2, y2 - arrow_size)])
            else:
                arrow1 = QPolygonF([QPointF(x1, y1), QPointF(x1 + arrow_size / 2, y1 - arrow_size / 2), QPointF(x1 + arrow_size / 2, y1 + arrow_size / 2)])
                arrow2 = QPolygonF([QPointF(x2, y2), QPointF(x2 - arrow_size / 2, y2 - arrow_size / 2), QPointF(x2 - arrow_size / 2, y2 + arrow_size / 2)])
            
            arrow_item1 = QGraphicsPolygonItem(arrow1)
            arrow_item1.setBrush(QBrush(Qt.black))
            self.graphics_scene.addItem(arrow_item1)

            arrow_item2 = QGraphicsPolygonItem(arrow2)
            arrow_item2.setBrush(QBrush(Qt.black))
            self.graphics_scene.addItem(arrow_item2)

            if is_outside:
                dimension_text = QGraphicsTextItem(text)
                dimension_text.setPos(x2 + 5, (y1 + y2) / 2 - dimension_text.boundingRect().height() / 2)
            else:
                dimension_text = QGraphicsTextItem(text)
                dimension_text.setPos(x1 + 10, (y1 + y2) / 2 - dimension_text.boundingRect().height() / 2)

        elif orientation == Qt.Horizontal:
            # Horizontal arrow and dimension
            if is_outside:
                arrow1 = QPolygonF([QPointF(x1, y1+0), QPointF(x1 + arrow_size, y1+0 - arrow_size / 2), QPointF(x1 + arrow_size, y1+0 + arrow_size / 2)])
                arrow2 = QPolygonF([QPointF(x2, y2+0), QPointF(x2 - arrow_size, y2+0 - arrow_size / 2), QPointF(x2 - arrow_size, y2+0 + arrow_size / 2)])
            else:
                arrow1 = QPolygonF([QPointF(x1, y1+0), QPointF(x1 + arrow_size, y1+0 - arrow_size / 2), QPointF(x1 + arrow_size, y1+0 + arrow_size / 2)])
                arrow2 = QPolygonF([QPointF(x2, y2+0), QPointF(x2 - arrow_size, y2+0 - arrow_size / 2), QPointF(x2 - arrow_size, y2+0 + arrow_size / 2)])

            arrow_item1 = QGraphicsPolygonItem(arrow1)
            arrow_item1.setBrush(QBrush(Qt.black))
            self.graphics_scene.addItem(arrow_item1)

            arrow_item2 = QGraphicsPolygonItem(arrow2)
            arrow_item2.setBrush(QBrush(Qt.black))
            self.graphics_scene.addItem(arrow_item2)

            if is_outside:
                dimension_text = QGraphicsTextItem(text)
                dimension_text.setPos((x1 + x2) / 2 - dimension_text.boundingRect().width() / 2, y1 + 5)
                # dimension_text.setPos((x1 + x2) / 2 - dimension_text.boundingRect().width() / 2, y1 - dimension_text.boundingRect().height() - 10)
            else:
                dimension_text = QGraphicsTextItem(text)
                dimension_text.setPos((x1 + x2) / 2 - dimension_text.boundingRect().width() / 2, y1 + 5)

        self.graphics_scene.addItem(dimension_text)

    def show_main_view(self):
        # Implement your show_main_view logic here
        pass


    
    def create_external_results_view(self):
        self.external_results_widget = QWidget()
        self.external_results_layout = QVBoxLayout()

        self.external_results_label = QLabel("External Stability Results")
        self.external_results_layout.addWidget(self.external_results_label)

        self.external_results_back_button = QPushButton("Back")
        self.external_results_back_button.clicked.connect(self.show_main_view)
        self.external_results_layout.addWidget(self.external_results_back_button)

        self.external_results_widget.setLayout(self.external_results_layout)
        self.stacked_layout.addWidget(self.external_results_widget)

    def create_internal_results_view(self):
        self.internal_results_widget = QWidget()
        self.internal_results_layout = QVBoxLayout()

        self.internal_results_label = QLabel("Internal Stability Results")
        self.internal_results_layout.addWidget(self.internal_results_label)

        self.internal_results_back_button = QPushButton("Back")
        self.internal_results_back_button.clicked.connect(self.show_main_view)
        self.internal_results_layout.addWidget(self.internal_results_back_button)

        self.internal_results_widget.setLayout(self.internal_results_layout)
        self.stacked_layout.addWidget(self.internal_results_widget)

    def show_main_view(self):
        self.stacked_layout.setCurrentWidget(self.main_widget)


    def show_projectinfo_inputs(self):
        self.stacked_layout.setCurrentWidget(self.projectinfo_widget) 

 #   def show_designmethod_inputs(self):
 #       self.stacked_layout.setCurrentWidget(self.designmethod_widget)     

    def show_geometry_inputs(self):
        self.stacked_layout.setCurrentWidget(self.geometry_widget)

    def show_reinforced_soil_inputs(self):
        self.stacked_layout.setCurrentWidget(self.reinforced_soil_widget)

    def show_retained_soil_inputs(self):
        self.stacked_layout.setCurrentWidget(self.retained_soil_widget)

    def show_foundation_soil_inputs(self):
        self.stacked_layout.setCurrentWidget(self.foundation_soil_widget)



    def show_external_loads_options(self):
        self.stacked_layout.setCurrentWidget(self.external_loads_options_widget)

    def show_dead_load_inputs(self):
        self.stacked_layout.setCurrentWidget(self.dead_load_widget)
        # Set default values to zero
        #self.dead_load1_input.setText("0")
        #self.dead_load2_input.setText("0")
        #self.dead_load3_input.setText("0")

    def show_live_load_inputs(self):
        self.stacked_layout.setCurrentWidget(self.live_load_widget)
        # Set default values to zero
        #self.live_load1_input.setText("0")
       # self.live_load2_input.setText("0")
        #self.live_load3_input.setText("0")

    def show_strip_load_inputs(self):
        self.stacked_layout.setCurrentWidget(self.strip_load_widget)
        # Set default values to zero
        #self.Vertical_Strip_Load1_input.setText("0")
       # self.Horizontal_Strip_Load1_input.setText("0")
       # self.Strip_Load1_width_input.setText("0")
       # self.Strip_Load1_distance_input.setText("0")
       # self.Vertical_Strip_Load2_input.setText("0")
       # self.Horizontal_Strip_Load2_input.setText("0")
       # self.Strip_Load2_width_input.setText("0")
       # self.Strip_Load2_distance_input.setText("0")
       # self.Vertical_Strip_Load3_input.setText("0")
       # self.Horizontal_Strip_Load3_input.setText("0")
       # self.Strip_Load3_width_input.setText("0")
       # self.Strip_Load3_distance_input.setText("0")

    def show_impact_load_inputs(self):
        self.stacked_layout.setCurrentWidget(self.impact_load_widget)
        # Set default value to zero
       # self.impact_load_input.setText("0")

    def show_earthquake_load_inputs(self):
        self.stacked_layout.setCurrentWidget(self.earthquake_load_widget)
        # Set default value to zero
       # self.earthquake_load_input.setText("0")

    def show_reinforcement_properties(self):
        self.stacked_layout.setCurrentWidget(self.reinforcement_properties_widget)

    def show_reinforcement_details(self):
        self.stacked_layout.setCurrentWidget(self.reinforcement_details_widget)

    
    
    def show_external_results(self):
        self.stacked_layout.setCurrentWidget(self.external_results_widget)

    def show_internal_results(self):
        self.stacked_layout.setCurrentWidget(self.internal_results_widget)

    def show_generate_report(self):
        self.stacked_layout.setCurrentWidget(self.generate_report_widget)


#############################################################################


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = GRSWALLDESIGNER()
    window.show()
    sys.exit(app.exec())
