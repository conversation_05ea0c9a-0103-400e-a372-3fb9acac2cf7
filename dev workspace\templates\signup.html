<!-- filepath: c:\Users\<USER>\OneDrive - iitgn.ac.in\github\GRS_Software\dev workspace\templates\signup.html -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up</title>
  </head>
  <body>
    <h2>Sign Up</h2>
    <form method="POST" action="{{ url_for('signup') }}">
      <label for="username">Username:</label>
      <input type="text" id="username" name="username" required /><br />
      <label for="password">Password:</label>
      <input type="password" id="password" name="password" required /><br />
      <button type="submit">Sign Up</button>
    </form>
    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %}
    <ul>
      {% for category, message in messages %}
      <li class="{{ category }}">{{ message }}</li>
      {% endfor %}
    </ul>
    {% endif %} {% endwith %}
  </body>
</html>
