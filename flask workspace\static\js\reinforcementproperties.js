document.addEventListener("DOMContentLoaded", function () {
    loadTableData(); // Load data on page load
  });

  function addRow(data = {}) {
    let table = document
      .getElementById("reinforcementTable")
      .getElementsByTagName("tbody")[0];
    let rowCount = table.rows.length;
    let newRow = table.insertRow(rowCount);

    // Type ID
    let typeCell = newRow.insertCell(0);
    typeCell.innerHTML = `Type-${rowCount + 1}`;

    // Name Field (Wider)
    let nameCell = newRow.insertCell(1);
    let nameInput = document.createElement("input");
    nameInput.type = "text";
    nameInput.className = "form-control wide-input";
    nameInput.value = data.name || "";
    nameCell.appendChild(nameInput);

    // Other numerical input fields
    for (let i = 2; i <= 9; i++) {
      let cell = newRow.insertCell(i);
      let input = document.createElement("input");
      input.type = "number";
      input.className = "form-control uniform-input";
      typeCell.className = "type-id";
      input.step = "0.1";
      input.value = data[`col${i}`] || "";
      cell.appendChild(input);
    }

    // Remove Button Column (Now under Scale Factor)
    let removeCell = newRow.insertCell(10);
    let removeButton = document.createElement("button");
    removeButton.innerHTML = "X";
    removeButton.className = "btn-remove";
    removeButton.onclick = function () {
      removeRow(this);
    };
    removeCell.appendChild(removeButton);

    saveTableData(); // Save after adding row
  }

  function removeRow(button) {
    let row = button.parentNode.parentNode;
    let table = row.parentNode;
    if (table.rows.length > 1) {
      // Ensure at least 1 row remains
      row.remove();
      saveTableData(); // Save after deletion
    } else {
      alert("At least one reinforcement type must be present.");
    }
  }

  function saveTableData() {
    let table = document
      .getElementById("reinforcementTable")
      .getElementsByTagName("tbody")[0];
    let rows = table.getElementsByTagName("tr");
    let data = [];

    for (let row of rows) {
      let inputs = row.getElementsByTagName("input");
      let rowData = {
        name: inputs[0].value, // Name input
      };

      for (let i = 1; i < inputs.length; i++) {
        // Exclude remove button
        rowData[`col${i + 1}`] = inputs[i].value;
      }

      data.push(rowData);
    }

    localStorage.setItem("reinforcementData", JSON.stringify(data)); // Store data
  }

  function loadTableData() {
    let storedData = localStorage.getItem("reinforcementData");
    if (storedData) {
      let data = JSON.parse(storedData);
      data.forEach((rowData) => addRow(rowData));
    } else {
      // Load default two rows if no data exists
      addRow();
      addRow();
    }
  }