document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById("project-info-form");

    // Load saved data (if available)
    if (localStorage.getItem("project_name")) {
        document.getElementById("project_name").value = localStorage.getItem("project_name");
    }
    if (localStorage.getItem("project_id")) {
        document.getElementById("project_id").value = localStorage.getItem("project_id");
    }
    if (localStorage.getItem("designer")) {
        document.getElementById("designer").value = localStorage.getItem("designer");
    }
    if (localStorage.getItem("client")) {
        document.getElementById("client").value = localStorage.getItem("client");
    }
    if (localStorage.getItem("description")) {
        document.getElementById("description").value = localStorage.getItem("description");
    }
    if (localStorage.getItem("date")) {
        document.getElementById("date").value = localStorage.getItem("date");
    }
    if (localStorage.getItem("revision")) {
        document.getElementById("revision").value = localStorage.getItem("revision");
    }

    // Save data when input changes
    form.addEventListener("input", function() {
        localStorage.setItem("project_name", document.getElementById("project_name").value);
        localStorage.setItem("project_id", document.getElementById("project_id").value);
        localStorage.setItem("designer", document.getElementById("designer").value);
        localStorage.setItem("client", document.getElementById("client").value);
        localStorage.setItem("description", document.getElementById("description").value);
        localStorage.setItem("date", document.getElementById("date").value);
        localStorage.setItem("revision", document.getElementById("revision").value);
    });

    // Save form data asynchronously with AJAX
    form.addEventListener("submit", function(e) {
        e.preventDefault();  // Prevent normal form submission

        const formData = new FormData(form);

        // Send the data to the server using AJAX
        fetch('/project_info', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);  // Show a success message
            // Optionally, you can clear localStorage after successful save
            // localStorage.removeItem("project_name");
            // localStorage.removeItem("project_id");
            // localStorage.removeItem("designer");
            // localStorage.removeItem("client");
            // localStorage.removeItem("description");
            // localStorage.removeItem("date");
            // localStorage.removeItem("revision");
        })
        .catch(error => {
            console.error('Error:', error);
            alert('There was an error with the submission.');
        });
    });
});