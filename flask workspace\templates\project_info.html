{% extends "base.html" %} {% block content %}
<div id="project-info-form">
  <h1>Project Information</h1>
  <form id="project-info-form">
    <div class="form-group">
      <label for="project_name">Project Name</label>
      <input
        type="text"
        class="form-control"
        id="project_name"
        name="project_name"
        value="{{ data.get('project_name', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="project_id">Project ID</label>
      <input
        type="text"
        class="form-control"
        id="project_id"
        name="project_id"
        value="{{ data.get('project_id', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="designer">Designer</label>
      <input
        type="text"
        class="form-control"
        id="designer"
        name="designer"
        value="{{ data.get('designer', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="client">Client</label>
      <input
        type="text"
        class="form-control"
        id="client"
        name="client"
        value="{{ data.get('client', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="description">Description</label>
      <textarea class="form-control" id="description" name="description">
{{ data.get('description', '') }}</textarea
      >
    </div>
    <div class="form-group">
      <label for="date">Date</label>
      <input
        type="date"
        class="form-control"
        id="date"
        name="date"
        value="{{ data.get('date', '') }}"
      />
    </div>
    <div class="form-group">
      <label for="revision">Revision</label>
      <input
        type="text"
        class="form-control"
        id="revision"
        name="revision"
        value="{{ data.get('revision', '') }}"
      />
    </div>
    <button type="submit" class="btn btn-primary">Save</button>
  </form>
</div>

<script src="{{ url_for('static', filename='js/project_info.js') }}"></script>
{% endblock %}
