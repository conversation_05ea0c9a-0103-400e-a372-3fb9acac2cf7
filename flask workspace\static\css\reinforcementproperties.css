/* Container Styling */
#reinforcement-form {
    margin-top: 20px;
}

#reinforcement-form h2 {
    font-size: 24px;
    color: #333;
    font-weight: bold;
    text-align: center;
}

/* Table Styling */
.table-container {
    overflow-x: auto;
    border-radius: 8px;
    background: #f8f9fa;
    padding: 15px;
}

#reinforcementTable {
    width: 100%;
    border-collapse: collapse;
}

#reinforcementTable th, #reinforcementTable td {
    text-align: center;
    padding: 8px;
    border: 1px solid #ddd;
}

#reinforcementTable thead {
    background-color: #e3f2fd;
    font-weight: bold;
}

/* Input Fields */
#reinforcementTable input[type="text"], 
#reinforcementTable input[type="number"] {
    width: 100%;
    padding: 6px;
    border: 2px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
}

#reinforcementTable input[type="number"]:focus, 
#reinforcementTable input[type="text"]:focus {
    border: 2px solid #007bff;
    background-color: #ffffff;
}

/* Buttons */
.btn-add {
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
    display: block;
    margin: 20px auto;
}

.btn-add:hover {
    background-color: #218838;
}

.btn-remove {
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
}

.btn-remove:hover {
    background-color: #c82333;
}
.type-id {
    width: 80px; /* Adjust width to fit Type-1 to Type-10 */
    text-align: center;
    white-space: nowrap; /* Prevents wrapping */
}
.form-control {
    padding: 5px;
    font-size: 14px;
}

.wide-input {
    width: 200px; /* Name column wider */
}

.uniform-input {
    width: 100px; /* Other columns same size */
}

/* Table Styling */
.table-container {
    max-width: 100%;
    overflow-x: auto;
}

.btn-add {
    margin-top: 10px;
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}

.btn-remove {
    background-color: red;
    color: white;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 5px;
}