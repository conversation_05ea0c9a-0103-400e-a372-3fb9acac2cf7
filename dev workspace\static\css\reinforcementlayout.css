/* Container Styling */
#reinforcementlayout-form {
    margin-top: 20px;
}

#reinforcementlayout-form h2 {
    font-size: 24px;
    color: #333;
    font-weight: bold;
    text-align: center;
}

/* Table Styling */
.table-container {
    overflow-x: auto;
    border-radius: 8px;
    background: #f8f9fa;
    padding: 15px;
}

#reinforcementLayoutTable {
    width: 100%;
    border-collapse: collapse;
}

#reinforcementLayoutTable th,
#reinforcementLayoutTable td {
    text-align: center;
    padding: 8px;
    border: 1px solid #ddd;
}

#reinforcementLayoutTable thead {
    background-color: #e3f2fd;
    font-weight: bold;
}

/* Input Fields */
#reinforcementLayoutTable input[type="number"] {
    width: 100%;
    padding: 6px;
    border: 2px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
}

#reinforcementLayoutTable input[type="number"]:focus {
    border: 2px solid #007bff;
    background-color: #ffffff;
}

/* Select Dropdown */
#reinforcementLayoutTable select {
    width: 100%;
    padding: 6px;
    border: 2px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
}

/* Buttons */
.btn-add {
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
    display: block;
    margin: 20px auto;
}

.btn-add:hover {
    background-color: #218838;
}

.btn-remove {
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;
}

.btn-remove:hover {
    background-color: #c82333;
}
